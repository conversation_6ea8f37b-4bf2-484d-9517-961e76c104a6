package com.ecco.dom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.Assert;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ServiceAclId implements IdName<Long> {

    static {
        // If you change the package, a database script to update ACLs will be needed.
        Assert.state(ServiceAclId.class.getName().equals("com.ecco" + ".dom.ServiceAclId")); // + avoids refactor tools changing this
    }

    public long id;

    public String name;

}
