package com.ecco.dto;

import static java.util.stream.Collectors.toList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.ecco.dom.*;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationAclId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;

public class ServicesProjectsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private List<ServiceProjectsDto> serviceProjectsDto = new ArrayList<>();
    List<ServiceAclId> restrictedServices;
    List<ProjectAclId> restrictedProjects;
    private List<ServiceCategorisationViewModel> svcCatsAll = new ArrayList<>();

    private boolean hasProjectRestrictions = false;

    /**
     * Create access. Either pass a set of services (and empty restrictedProjects) to control at a service level. Or supply combinations of
     * services and projects where this class will loop through valid combinations of services with projects creating the restricted combinations.
     * Currently, -1 can be provided on the restrictedProjects to indicate all projects (if no restricted matches exist).
     * The result of this code is used in ReferralListFilter (reports etc), and referralsListName.jspf when listing matching referrals to an entered name (see ServicesProjectsDto.canAccess_allServicesWithUnrestrictedProjectsAndProjects)
     */
    // NB The arguments are domain objects
    public ServicesProjectsDto(List<ServiceAclId> restrictedServices, List<ProjectAclId> restrictedProjects,
                               List<ServiceCategorisationViewModel> svcCats) {
        // we allow null arguments for testing and usages where no restrictions should be applied
        // however we don't want to keep testing null, so create here as necessary
        if (restrictedServices == null) {
            restrictedServices = new ArrayList<>();
        }
        if (restrictedProjects == null) {
            restrictedProjects = new ArrayList<>();
        }

        this.svcCatsAll = svcCats;
        this.restrictedServices = restrictedServices;
        this.restrictedProjects = restrictedProjects;

        createRestrictedServicesProjects();
        // a current limitation of ReferralListFilter - see ECCO-420
        highlightRestrictionProblems();
    }

    /**
     * Create a list of Service -> Projects from two seemingly unrelated lists of restricted Services and Projects.
     * The created data structures serve two purposes:
     *  - for the 'referrals list': hasProjectRestrictions
     *  - for individual access: servicesProjectsDto
     */
    private void createRestrictedServicesProjects() {

        restrictedServices.forEach(s -> {
            ServiceProjectsDto dto = createRestrictedDto(restrictedProjects, s);
            log.debug("restriction: {}", dto);
            serviceProjectsDto.add(dto);
        });

        // override hasProjectRestrictions if we have accessAllProjects for the referral list filter code
        // whilst retaining the service's projects for individual access
        restrictedProjects.forEach(p -> {
            if (Objects.equals(p.getId(), ProjectAclId.ACCESS_ALL_PROJECTS)) {
                this.hasProjectRestrictions = false;
            }
        });
    }

    /**
     * From acls of serviceId and projectId that a user may have permissions to
     * we convert into a list of services with projects.
     * NB service.getCategorisations is from services_projects which does not have -1 project in it
     * which means the -1 is effectively translated into services_projects, which can have null projects.
     */
    private ServiceProjectsDto createRestrictedDto(List<ProjectAclId> restrictedProjects, ServiceAclId service) {

        var projectCats = getProjectCategorisations(service);

        // does the service have any projects
        // ? WAS service.getProjects() - but this is WITHOUT restrictions but with -1 ?
        if (projectCats.isEmpty()) {
            // use empty list for projects when there are no projects on the service
            return new ServiceProjectsDto(service);
        }
        if (restrictedProjects.isEmpty()) {
            // allow all projects, including null (because categorisations have to be saved with null)

            // ** PERFORMANCE .getCategorisations ** need to use RepositoryBasedProjectService
            // ** reset cache

            return new ServiceProjectsDto(service, !projectCats.isEmpty(), projectCats);
        }
        // We now have 'can access all projects' (accessAllProjects -1) in the ACL admin page. Therefore we can have a
        // simpler clause here to determine project restrictions. Previously we were attempting to deprecate accessAllProjects
        // in favour of automatically deducing it. This is possible because service level permissions automatically give
        // access to all projects, therefore we could assume that if a user had any restricted projects at all, and that
        // those projects were not relevant for a particular service, then what we intended was access to all its projects.
        // Therefore we reset the allowed project list back to the service's projects. However, this is not the simplest
        // approach and can in fact magically give access if all projects if a restricted project is taken away from a
        // service. Its best we KISS and force users to specify -1 in the ACLs until we change to be service+project
        // combinations, but to avoid any migration, we retain the approach where access is given to services and -1 is
        // not supplied (see below). We'll probably just go straight to having a service+project domain and service domain on
        // the ACLs and avoid intermediate migration of legacy systems to -1 'can access all projects' usage.
        boolean hasAccessAllProjects = restrictedProjects.stream()
                .anyMatch(p -> Objects.equals(p.getId(), ProjectAclId.ACCESS_ALL_PROJECTS));
        if (hasAccessAllProjects) {
            // allow all projects, including null (because categorisations have to be saved with null)
            return new ServiceProjectsDto(service, !projectCats.isEmpty(), projectCats);
        }

        var restrictedProjectIds = restrictedProjects.stream().map(ProjectAclId::getId).toList();
        var allowedProjects = projectCats.stream()
                .filter(sc -> restrictedProjectIds.contains(sc.getProjectId()))
                .collect(toList());

        // If we get here then some projects exist for the service and some projects are being restricted for the user. If
        // the allowedProjects are empty, then we have a service but no projects combination granted. We therefore assume
        // that the entire service is accessible because that is the principle we have when selecting just services.
        // Ideally people now choose the 'can access all projects' but we don't want any support hassle of adding this for legacy
        // situations for the sake of maintaining the code we just had for a bit longer - until we move to service+project
        // domain ACLs. This does leave us vulnerable to the case (which is in ServicesProjectsDtoTest) where if restricted
        // projects gets unassigned from the service then all of a sudden people would see every project.
        if (allowedProjects.isEmpty()) {
            allowedProjects = projectCats;
        } else {
            // if the service has projects and we have assigned project restrictions of which none match the service
            // then we now assume we have access to the service, so getting here means we have project restrictions to apply
            this.hasProjectRestrictions = true;
        }

        return new ServiceProjectsDto(service, !projectCats.isEmpty(), allowedProjects);
    }

    // ?? getProjectId() != null
    private List<ServiceCategorisationViewModel> getProjectCategorisations(ServiceAclId service) {
        return this.svcCatsAll.stream()
                .filter(s -> Objects.equals(s.getServiceId(), service.getId()))
                .filter(s -> s.getProjectId() != null)
                .toList();
    }

    // method to help filters understand if they need to join on the projects table
    // NB this is ONLY used from EntityRestrictionCommandPredicates (and tests)
    // but it is NOT used for individual access checks
    public boolean hasProjectRestrictions() {
        return this.hasProjectRestrictions;
    }

    public boolean hasNoProjectRestrictions(long serviceId) {
        // ? should be: return serviceProjectsDto.hasProjectRestrictions();
        return !serviceProjectsDto(serviceId).getRestrictedProjects().isEmpty();
    }

    public List<Long> getProjectRestrictionIds(long serviceId) {
        return serviceProjectsDto(serviceId).getRestrictedProjects().stream().map(ProjectAclId::getId).toList();
    }

    // ? replace
    private ServiceProjectsDto serviceProjectsDto(long serviceId) {
        for (ServiceProjectsDto spDto : serviceProjectsDto) {
            if (Objects.equals(serviceId, spDto.getRestrictedService().getId())) {
                return spDto;
            }
        }
        return null;
    }

    /**
     * This determines if the service has projects. This is NOT returning whether the service has restricted projects (@see getServiceRestrictedProjects)
     */
    public boolean serviceHasProjects(long serviceId) {
        for (ServiceProjectsDto spDto : serviceProjectsDto) {
            if (Objects.equals(serviceId, spDto.getRestrictedService().getId())) {
                return (spDto.isServiceHasProjects());
            }
        }
        return false;
    }

    /**
     * @return The list of projects found on this service, or null
     */
    // TODO rename NotNull to be clear to calling methods which are fine with not null (or unused)
    public List<ProjectAclId> getServiceRestrictedProjectIds(long serviceId) {
        for (ServiceProjectsDto spDto : serviceProjectsDto) {
            if (Objects.equals(serviceId, spDto.getRestrictedService().getId())) {
                return new ArrayList<>(spDto.getRestrictedProjects());
            }
        }
        return null;
    }

    /**
     * Get the security restrictions for services, with the restricted projects inside the service, if any.
     * NB This is only used client-side but widely, implying the -1/null-filtering of projects is expected
     * NB This does, however, still return services when there are no projects
     * TODO migrate to getRestrictedServiceCategorisations
     */
    /*public List<ServiceViewModel> getRestrictedServicesWithRestrictedProjects() {
        List<ServiceViewModel> services = new ArrayList<>();
        for (ServiceProjectsDto spDto : serviceProjectsDto) {
            // does not add staffWithAccess, even though its only used by SessionDataController
            // which did mean the newer 'setup initial assessment' interview dropdowns were empty
            // however, we don't want staffWithAccess to just a Service, it needs to include Project
            ServiceViewModel s = spDto.getRestrictedService().toViewModel();
            // add any projects if there are any
            s.projects = s.projects.stream()
                    .filter(p -> spDto.getRestrictedProjects().stream().anyMatch(rp -> rp.getId().intValue() == p.id))
                    .collect(toList());
            services.add(s);
        }
        return services;
    }*/

    /**
     * Get the security restrictions for services, with the restricted projects inside the service.
     * serviceCategorisation-focused customers expect services to always have projects, and staff to always have projects
     * however, this code is being used more widely by serviceProject-focused customers as one place to determine security client-side,
     * and serviceProject-focused customers can have no project assigned to a service, and no project assigned to staff.
     * The two approaches can co-exist if the serviceCategorisations do not exclude a service simply because there is no project
     * match - ie allowing nulls, and the client side is handled appropriately for both approaches.
     * One example of determining the exact security client side is 'deletion requests' ('referrals list' security is handled server side).
     * The 'deletion requests' required checking 'isServiceHasProjects' (DEV-713) and allowing null projects through (DEV-835).
     * This work gives us more accurate client-side security matching for what is configured on their accounts.
     */
    public List<ServiceCategorisationAclId> getRestrictedServiceCategorisationIds() {
        List<ServiceCategorisationAclId> cats = new ArrayList<>();
        for (ServiceProjectsDto restriction : serviceProjectsDto) {
            var s = restriction.getRestrictedService();
            // ? ADD the service
            List<ServiceCategorisationViewModel> scvms = getProjectCategorisations(s).stream() // for all services_projects
                    .filter(sc ->

                        // NB This was 'spDto.getRestrictedProjects().isEmpty()'
                        // which occurs if the service has no projects (see createDto above)
                        // or if only a null project was assigned - both amount to the same thing
                        // so we make it clearer with isServiceHasProjects

                        // serviceCategorisation-focused customers will not be affected by this no-project check as services always have projects, and staff always have projects
                        // but good for other customers to get services without projects
                        (!restriction.isServiceHasProjects()
                            ||
                        // serviceCategorisation-focused customers will not be affected by this null-check as services always have projects, and staff always have projects
                        // but good for other customers to get services without projects
                         restriction.getRestrictedProjectsAndNullProjects().stream()
                            .anyMatch(rp -> rp == null
                                ? sc.projectId == null
                                : Objects.equals(sc.projectId, rp.getId())
                            )
                        )
                    )
                    .toList();
            cats.addAll(scvms);
        }
        // ?? remove dups - sort order ?
        // remove duplicates by cats id
        return cats.stream()
                .distinct()
                .collect(toList());
    }

    public List<Long> getRestrictedServiceIds() {
        return serviceProjectsDto.stream().map(s -> s.getRestrictedService().id).toList();
    }

    // method to help highlight problems with the current impl
    // the confusion comes in because we can't capture the correct intention yet when restricted projects across multiple services.
    // there are 2 issues:
    //  - 1) we restrict access too much
    //      if the service was intended to be all projects (yet a restricted project matches) - the service will be restricted to the project
    //  - 2) we expose too much access
    //      if a project is shared between restricted services - then a project is shown that was never intended
    // both scenarios aren't good, but the first scenario represents a restriction of access which can be overcome by breaking down into individual projects (but could stumble on problem 2 more)
    // but the unliklihood of these scenarios actually occuring are low

    // we can highlight 1) above by determining if global projects (through -1) was requested along with other restricted projects
    //  a) this will help investigate whether one service was meant to be 'access all projects'
    //  - but in fact, the code will restrict to projects that match other service's projects
    // we can highlight 1) above by determining if global projects (by not restricting projects) was requested along with other restricted projects
    //  b) this will help investigate whether one service was meant to be 'access all projects' by not defining projects
    //  - but in fact, the code will restrict to projects that match other service's projects
    // we can highlight 2) above by determining if a project is shared between services
    //  c) this will help investigate whether the each service was meant to be restricted by the same project
    //  - but in fact, the code will allow access the project in the other service (problem 2 highlighted in the above para)
    int highlightRestrictionProblems() {

        int countProblems = 0;

        // if we have more than one service assigned then we need to check potential problems
        if (serviceProjectsDto.size() > 1) {

            // a) if we have globalProjects (through legacy -1), yet we have more projects defined (not just -1) - then services intended with global projects may be restricted to the restricted projects list
            // NB this is no longer the case - a -1 indicates 'can access all projects' as the admin page suggests.

            // b) if we have globalProjects (through not defining projects), then services intended with global projects may be restricted to the restricted projects list
            // so it could be ok if there isn't any overlap between the service's projects otherwise the service is restricted to the mathed projects
            // so see if the number of restricted services is that of the projects
            // (taking into account that some of the services might not have projects)
            int srvCount = restrictedServices.size();
            for (ServiceProjectsDto spDto : serviceProjectsDto) {
                if (!spDto.isServiceHasProjects()) {
                    srvCount--;
                }
            }
            if (srvCount > restrictedProjects.size()) {
                log.debug("possible INCORRECT ACL application [restriction]: multiple services where some services have and some haven't been configured with projects: services intended with global projects may be restricted to the restricted projects list and could be empty (for current user)");
                countProblems++;
            }

            // c) check that the projects don't overlap other service's projects
            // so for each of the restricted projects, check doesn't exist in services
            boolean warn = false;
            for (var p : restrictedProjects) {
                // for each project, we check the count in the other services
                int count = 0;
                for (ServiceProjectsDto spDto : serviceProjectsDto) {
                    // TODO check ServiceProjectsDto getRestrictedService().getCategorisations() IS this.getProjectCategorisations
                    var serviceProjects = this.getProjectCategorisations(spDto.getRestrictedService());
                    for (var sc : serviceProjects) {
                        if (Objects.equals(sc.getProjectId(), p.getId())) {
                            count++;
                        }
                    }
                }
                if (count >= 2) {
                    warn = true;
                }
            }
            // we can assign a project once, more than that could be a mistake
            if (warn) {
                // this problem will result in an extra project appearing on another service
                log.debug("possible INCORRECT ACL application [restriction/exposure]: multiple services where a restricted project exists across other services: services with matching projects will be allowed (for current user)");
                countProblems++;
            }
        }

        return countProblems;
    }

    public boolean canAccess(long serviceId) {
        return canAccessInternal(serviceId, null);
    }

    public boolean canAccess(long serviceId, long projectId) {
        return canAccessInternal(serviceId, projectId);
    }

    private boolean canAccessInternal(long serviceId, Long projectId) {

        if (projectId == null) {
            projectId = ProjectAclId.ACCESS_ALL_PROJECTS;
        }

        // check can see the service
        ServiceProjectsDto spDto = serviceProjectsDto(serviceId);
        if (spDto == null) {
            return false;
        }

        // if no projects exist for the service - we can still access it
        if (!spDto.isServiceHasProjects()) {
            return true;
        }

        // special case, when testing a referral a project may not be assigned, for instance in the wizard process (see referralsListName.jspf)
        if (Objects.equals(projectId, ProjectAclId.ACCESS_ALL_PROJECTS)) {
            // we could re-introduce access for 'none' projects which we did through allowNullProjects - now deleted
            return true;
        }

        // if projects exist (because we've checked isServiceHasProjects above)
        // and a project was specified (because projectId is not null here)
        // then check the projectId, so its good that getRestrictedProjects filters null projects
        long finalProjectId = projectId;
        return restrictedProjects.stream().anyMatch(proj -> Objects.equals(proj.getId(), finalProjectId));
    }

    private static <PK extends Serializable, T extends BaseEntity<PK>> T findInCollectionById(PK id, Iterable<T> entities) {
        for (T entity : entities) {
            if (entity.getId().equals(id)) {
                return entity;
            }
        }
        return null;
    }

}
