package com.ecco.serviceConfig.config;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.serviceConfig.hact.repositories.HactOutcomeMappingRepository;
import com.ecco.serviceConfig.repositories.*;
import com.ecco.serviceConfig.service.RepositoryBasedServiceTypeService;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.security.dto.AclExtractor;
import com.ecco.serviceConfig.service.SessionDataServiceImpl;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToViewModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.util.List;

@Configuration(proxyBeanMethods = false)
@ComponentScan(basePackageClasses = SessionDataServiceImpl.class)
@EnableJpaRepositories(basePackageClasses={ServiceTypeRepository.class, HactOutcomeMappingRepository.class})
public class ServiceConfigConfig {

    @Bean // NOTE: Name is serviceTypeService2 as we have 2 defs which would result in one being overwritten
    ServiceTypeService serviceTypeService2(ServiceTypeRepository serviceTypeRepository,
                                           ServiceTypeWorkflowRepository serviceTypeWorkflowRepository) {
        return new RepositoryBasedServiceTypeService(serviceTypeRepository, serviceTypeWorkflowRepository);
    }

    @Bean
    ServiceCategorisationToViewModel serviceCategorisationToViewModel(ListDefinitionRepository listDefinitionRepository) {
        return new ServiceCategorisationToViewModel(listDefinitionRepository);
    }

    @Bean
    AclExtractor<ProjectAclId> projectAclExtractor(ProjectRepository projectRepository) {
        return new AclExtractor<>() {
            @Override
            public List<ProjectAclId> listObjects() {
                var projects = projectRepository.findAllAclIds();
                projects.add(ProjectAclId.accessAllProjectsFakeProject); // manually add fake so we get it in lists
                return projects;
            }

            @Override
            public Class<ProjectAclId> getClazz() {
                return ProjectAclId.class;
            }
        };
    }

    @Bean
    AclExtractor<ServiceAclId> serviceAclExtractor(ServiceRepository serviceRepository) {
        return new AclExtractor<>() {
            @Override
            public List<ServiceAclId> listObjects() {
                return serviceRepository.findAllAclIds();
            }

            @Override
            public Class<ServiceAclId> getClazz() {
                return ServiceAclId.class;
            }
        };
    }

}
