package com.ecco.serviceConfig.viewModel;

import java.util.List;

import com.ecco.serviceConfig.dom.GroupSupportActivityType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IdNameWithServicesViewModel extends IdNameViewModel {

    public List<Long> serviceIds;

    /** A count of the number of referrals linked to this property on this service.
     * For example, this could be the count of referrals where a given {@link GroupSupportActivityType} is selected.
     */
    public int numReferrals;

}
