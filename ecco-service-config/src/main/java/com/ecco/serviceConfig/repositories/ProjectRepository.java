package com.ecco.serviceConfig.repositories;

import com.ecco.dom.Project;
import com.ecco.dom.ProjectAclId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ProjectRepository extends JpaRepository<Project, Long> {

    Project findOneByNameIgnoreCase(String name);

    @Query("SELECT new com.ecco.dom.ProjectAclId(p.id, p.name) from Project p")
    List<ProjectAclId> findAllAclIds();

}
