# ui messages
domain.title=eccosolutions.co.uk
domain.friendlyName=ecco solutions
videoURL=http://systems.eccosolutions.co.uk

# the type of service recipient: (r)eferral, (b)uilding, (w)orker, (i)ncident, (m)maint/repair, (ct)ontract (gs)roupsupport */
sr.identifier.r=r-id
sr.identifier.b=b-id
sr.identifier.w=w-id
sr.identifier.i=i-id
# m is maintenance, which is a repair, so we use rp-id
sr.identifier.m=rp-id
sr.identifier.ct=ct-id
sr.identifier.gs=gs-id


# *********************
# terminology
# *********************

# For use in "add goal" "edit goal" etc. some want to say "add outcome" or "add target"
terminology.smartstep=goal

listDef.nullText.riskMatrixSeverity=- severity -
listDef.nullText.riskMatrixLikelihood=- likelihood -

# *********************
# main menu url text
# *********************
menu.linktext.siteAdmin=site admin
menu.linktext.buildings=buildings
menu.linktext.referrals=referrals
menu.linktext.clients=clients
menu.linktext.find_referral=find referral
menu.linktext.list_referrals=list referrals
menu.linktext.my_referrals=my referrals
menu.linktext.live_referrals=quick log
menu.linktext.loneworker=lone worker
menu.linktext.projectcalendar=project calendar
menu.linktext.delete_referral=referral deletion requests
menu.linktext.support_plans=support plans
menu.linktext.support=support
menu.linktext.calendar=calendar
menu.linktext.contacts=contacts
menu.linktext.community_support=community support
menu.linktext.group_support=group support
menu.linktext.projects=locations
menu.linktext.hr=hr
menu.linktext.rota=rota
menu.linktext.reports=reports
menu.linktext.settings=settings
menu.linktext.logins=logins
menu.linktext.LDAP_group_mapping=LDAP group mapping
menu.linktext.lists=lists
menu.linktext.outcomes=outcome actions
menu.linktext.referral_process=referral process
menu.linktext.logo=logo
menu.linktext.dashboard=dashboard
menu.linktext.reportCharts=charts-earlydemo
menu.linktext.savedCharts=reports list
menu.linktext.services=services
menu.linktext.submissions=submissions
menu.linktext.finances=finances
menu.linktext.staff=staff
menu.linktext.user_audit=login audit
menu.linktext.allocation_basket=allocation basket
menu.linktext.duty_basket=duty basket
menu.linktext.my_tasks=my tasks
menu.linktext.resources.beds=bed allocations
menu.linktext.resources.dayService=day services
menu.linktext.workload=workload
menu.linktext.offline_admin=offline
menu.linktext.offline_install_use=offline
menu.linktext.contactAdmin=contacts (in dev)
menu.linktext.agencyAdmin=agencies (in dev)

# *********************
# form messages
# *********************
referralBreadcrumb.region=region
referralBreadcrumb.project=location
referralBreadcrumb.clientWithContact=client
referralBreadcrumb.staffDetail=staff
referralBreadcrumb.relationshipToPrimary=relationship
referralBreadcrumb.dataProtection=data protection
referralBreadcrumb.consent=consent
referralBreadcrumb.source=source
referralBreadcrumb.referralDetails=details
referralBreadcrumb.referralView=overview

referralDetails.project=location
referralDetails.projectPreferred=preferred location

# Across all evidence (overridden by {taskName}.goal[Plan|Name].label)
goalName.label=goal
goalPlan.label=comment. e.g. status or plan of action?

# support
# needsReduction.goalPlan.label=
support.goal.new.label=new action
support.subGoal.new.label=new sub action

# risk
threatAssessment.goalPlan.label=comment e.g. current / previous risk history
threatReduction.goalPlan.label=comment e.g. current / previous risk history
threatAssessmentReduction.goalPlan.label=comment e.g. current / previous risk history

threat.triggerControl.hazard.label=triggers
threat.triggerControl.intervention.label=actions to reduce risk

threat.goal.new.label=new action
threat.subGoal.new.label=new sub action

# TAB - overview
referralView.tabs.overview=overview

# TAB - risks
referralView.tabs.risk=risk

# TAB - support
referralView.tabs.support=support

# TAB - support history
referralView.tabs.supportHistory=support history

# TAB - support history
referralView.tabs.visitHistory=visit history

# TAB - checklist history
referralView.tabs.checklistHistory=checklist history

# TAB - risk history
referralView.tabs.riskHistory=risk history

referralView.tabs.formHistory=form history

referralView.tabs.forwardPlan=forward plan
referralView.tabs.forwardRiskPlan=forward risk plan

# TAB - appointments
referralView.tabs.appointments=appointments

# TAB - invoices
referralView.tabs.invoices=invoices

# TAB - family support
referralView.tabs.relationshipStar=relationship star
referralView.newMultipleReferral=new party

# TAB - services
referralView.tabs.services=services
referralView.tabs.servicesChildren=services


# TAB - audit history
referralView.tabs.auditHistory=audit history


# TAB - tasks
referralView.tabs.tasks=tasks
referralView.createdBy=created
referralView.clientWithContact=client details
referralView.allocateWorker=allocate key worker
referralView.staffDetail=staff detail
referralView.staffLocation=staff location
referralView.project=destination of referral
referralView.projectRegion=destination of referral
referralView.dataProtection=data protection
referralView.consent=consent
referralView.from=source of referral
referralView.sourceWithIndividual=source of referral
referralView.referralDetails=details of referral
referralView.referralDetailsSubmission=monitoring information
referralView.referralAccepted=appropriate referral
referralView.referralsAccepted=appropriate referrals
reports.referralsAccepted=appropriate
acceptOrSignpost=appropriate or signpost
referralView.projectAsAccommodation=accommodation
referralView.assessmentDate=setup initial assessment
referralView.funding=funding
referralView.eligibleFunding=funding eligibility
referralView.pendingStatus=pending status
#referralView.riskAssessment=risk assessment
#referralView.riskAssessmentRAG=risk assessment
referralView.decideFinal=accept on service
# below needed for Activiti workflow on daycentre service
referralView.assessmentAccepted=accept on service
referralView.eligibility=eligibility
referralView.employmentSkill=employment/skill
referralView.assessmentDetails=initial assessment
referralView.additionalAssessment=additional information
referralView.initialAssessment=initial assessment
initialAssessments=initial assessments
referralView.nextMeeting=schedule next meeting
referralView.scheduleMeetings=schedule meetings
referralView.emergencyDetails=emergency details
referralView.agreementOfAppointments=visit schedules
referralView.needsAssessment=needs assessment
referralView.needsAssessmentHousehold=household needs assessment
referralView.iaptAttendance=iapt attendance
referralView.iaptInitialAssessment=iapt initial assessment
referralView.iaptCurrentView=iapt current view
referralView.iaptSessions=iapt sessions
referralView.iaptGoals=iapt goals
referralView.iaptImpact=iapt impact
referralView.iaptFeedback=iapt feedback
referralView.ecorys=ecorys
referralView.pfpRoutines=routines and guidelines
referralView.feedbackQuestionnaire=feedback
# retian misspelling in case live sites using it (or even "retain" :-p )
referralView.feedbackQuestionniare=feedback
referralView.referralActivities=activities
referralView.needsAssessmentReduction=support plan
referralView.needsAssessmentReductionReview=review
referralView.threatAssessment=risk assessment
referralView.threatReduction=risk management
referralView.threatAssessmentReduction=risk management
referralView.riskManagement=risk management
referralView.riskManagementLone=lone risk assessment
referralView.CAF10risk=CAF 10
referralView.threatAssessmentReductionPartner=perpetrator risk management
referralView.socialImpact=social impact
referralView.start=start on service
referralView.startAccommodation=start on service
referralView.starting=starting
referralView.multiAllocate=allocate
referralView.allocateToServices=allocate
referralView.contract=contract
referralView.waitingListCriteria=waiting list criteria
referralView.spclientrecord=client record (SP)
referralView.scheduleReviews=schedule reviews
referralView.needsReduction=support plan
referralView.needsReductionSP=exit sp outcomes
referralView.rotaVisit=rota visit
referralView.deliveredBy=delivered by
referralView.close=close
referralView.newReferral=new referral
referralView.newReferral.beta=new referral (beta)
referralView.newReferral.old=new referral (old screens)
referralView.supportStaffNotes=notes
referralView.referralComments=referral comments
referralView.engagementComments=referral comments
referralView.familytype=family type
referralView.managerNotes=manager notes
referralView.groupActivities=group activities
referralView.generalQuestionnaire=questionnaire
referralView.hactQuestionnaire=HACT
referralView.economicStatus=economic status
referralView.initial-sp_data=initial sp data
referralView.exit-sp_data=exit sp data
supportServiceView.needsCapture=support plan
referralView.dailyRoutines=daily routines
referralView.dietaryIntake=dietary intake
referralView.customForm1=custom form 1
referralView.customForm2=custom form 2
referralView.customForm3=custom form 3
referralView.customForm4=custom form 4
referralView.customForm5=custom form 5
referralView.customForm6=custom form 6
referralView.customForm7=custom form 7
referralView.customForm8=custom form 8
referralView.customForm9=custom form 9
referralView.customForm10=custom form 10
referralView.customForm11=custom form 11
referralView.customForm12=custom form 12
referralView.customForm13=custom form 13
referralView.customForm14=custom form 14
referralView.customForm15=custom form 15
referralView.customForm16=custom form 16
referralView.customForm17=custom form 17
referralView.customFormMgr=custom form mgr

workerJobView.jobDetails=job details

# per referral status - see referralsListStatus.jspf
status.forAssessment=for assessment
status.pending=pending
status.started=started
status.toStart=waiting
status.signposted=signposted
status.incomplete=new
status.exited=exited
status.hidden=delete requested

referralDecision.acceptReferral=appropriate referral
referralDecision.assessmentAccepted=accept on service
referralDecision.startAssessment=start assessment

# per referral status - was used mainly for ReferralFlowAction.setupReferralState but this isn't used
# also used sparodically - eg menu/referralsList.jsp
referralState.unknown=unknown
referralState.new=new
referralState.incomplete=incomplete
referralState.acceptedReferral=for assessment
referralState.accepted=accepted
referralState.started=started
referralState.rejected=rejected

referralActivities=activities
referralActivity=activity

referralActivity.agreementExpected=agreement expected
referralActivity.agreementInPlace=agreement in place
referralActivity.agreementInPlace.yes=yes
referralActivity.agreementInPlace.no=no

referralFunding.eligibleFundingDate=eligible funding date
referralFunding.fundingBand=funding band
referralFunding.decisionDate=funding decision date
referralFunding.accepted=funding was accepted
referralFunding.fundingHoursOfSupport=hours of support

reports.activities=activities
reports.appointments.one=appointment
reports.appointments.many=appointments
reports.appointments.rota-list.title=client appointments for {0}

reports.title.supportplan=support plan
reports.title.rota=rota
reports.title.securityAudit=username security audit
reports.title.loginAudit=username login audit


form.contactDetail.project=area

form.emergencyDetails.description=description details
form.emergencyDetails.communicationNeeds=communication needs
form.emergencyDetails.communicationKeyword=safe/trusted communication keyword
form.emergencyDetails.emergencyDetails=emergency details
form.emergencyDetails.medicationDetails=medication details
form.emergencyDetails.doctorsDetails=doctors details
form.emergencyDetails.dentistDetails=dentist details
form.emergencyDetails.risksAndConcerns=risks and concerns

form.agreements.signature=signature

form.funding.reviewDate=funding review date
form.funding.paymentReference=funding payment reference
form.assessmentDate.firstOfferedInterviewDate=interview date first offered
form.evidenceComment.location=location
form.evidenceComment.meetingStatus=meeting status
form.evidenceComment.clientStatus=client status
form.evidenceComment.type=type

clientEmergencyDetails.grabSheet=emergency sheet

agreementOfAppointments.overview=service agreement

rota.demandType.worker.displayName=worker
rota.demandType.worker.apiUrl=/api/rota
rota.demandType.carerun.displayName=care run
rota.demandType.bookableResource.displayName=resource
rota.demandType.bookableResource.apiUrl=/api/rota/resources

offline_admin.title=offline admin
offline_admin.linktext.review_queued_commands=review queued commands
command_queue.title=command queue
command_queue.status.UNREADABLE=unreadable
command_queue.status.NEEDS_REVIEW=needs review
command_queue.status.DISMISSED=dismissed
command_queue.status.COMPLETED_OK=completed ok
command_queue.status.FAILED=failed
command_queue.status.ARCHIVED=archived

searchClients.global.apiUrl=/api/clients/all/query
searchClients.local.apiUrl=/api/clients/local/query
searchClients.refine=there are multiple matches - please provide enough information to uniquely identify the client
searchClients.results=there are matches
searchClients.errors=no matches were found - but there's a problem...

# *********************
# form text
# *********************
projects=locations
project=location
signpost=signpost
signpostBack=signpost back to referrer
signpostComment=comment
birthDate=birth date
fundingBand=funding band
economicStatus=economic status

# *********************
# report entity text
# *********************
reportEntity.Referral=referrals
reportEntity.ReferralFull=referrals
reportEntity.ReferralSummary=referrals
reportEntity.SupportWork=support work
reportEntity.RiskWork=risk work
reportEntity.SupportRiskWork=support and risk work
reportEntity.RiskFlags=risk flags
reportEntity.SupportFlags=evidence flags
reportEntity.ActivityAttendance=group support
reportEntity.Questionnaire=questionnaires
reportEntity.QuestionnaireSnapshot=questionnaire snapshots
reportEntity.SmartStepSingleSnapshot=smartstep snapshots
reportEntity.SmartStepMultiSnapshot=smartstep snapshots
reportEntity.ServiceRecipient=service recipients
reportEntity.GroupedWorkAnalysis=support work
reportEntity.RotaDemand=rota demand
reportEntity.RotaAgreement=rota agreement
reportEntity.RotaSchedule=rota schedule
reportEntity.EventResource=events
reportEntity.ReferralsByMonth=referrals
reportEntity.ReferralsByService=referrals
reportEntity.TasksByMonth=tasks
reportEntity.Client=clients
reportEntity.Agency=agencies
reportEntity.AssociatedContact=referral contacts
reportEntity.Professional=professionals
reportEntity.TaskStatus=tasks
reportEntity.ServiceRecipientCommand=audits
reportEntity.User=users
reportEntity.Review=reviews
reportEntity.ServiceType=service types
reportEntity.AddressHistory=address history
reportEntity.OccupancyHistory=occupancy history
reportEntity.FinanceCharge=finance charges
reportEntity.FinanceReceipt=finance receipts
reportEntity.SupportWorkSnapshot=work snapshots
reportEntity.RiskWorkSnapshot=work snapshots
reportEntity.CustomFormSnapshot=custom form snapshot
reportEntity.Building=buildings


# *********************
# other link text
# *********************
overview=history
overview.all=all
overview.changes=changes
overview.supportOnly=support only
overview.needsOnly=needs only
overview.print.supporttitle=support history
overview.print.threattitle=risk management

attachments=attachments

# *********************
# tooltip
# *********************


# *********************
# boxmessages
# *********************


# *********************
# submit buttons/entity action text
# *********************


# *********************
# helper objects
# *********************
fromnow=from now

category.null=none
contact.null=none

minute.null=minute
hour.null=hour

year.null=year

dateorder1=day
dateorder2=month
dateorder3=year
dateorder4=time
day.null=day
month.null=month
month.1=January
month.2=February
month.3=March
month.4=April
month.5=May
month.6=June
month.7=July
month.8=August
month.9=September
month.10=October
month.11=November
month.12=December

hour.0=00
hour.1=01
hour.2=02
hour.3=03
hour.4=04
hour.5=05
hour.6=06
hour.7=07
hour.8=08
hour.9=09
hour.10=10
hour.11=11
hour.12=12
hour.13=13
hour.14=14
hour.15=15
hour.16=16
hour.17=17
hour.18=18
hour.19=19
hour.20=20
hour.21=21
hour.22=22
hour.23=23

minute.0=00
minute.15=15
minute.30=30
minute.45=45

a=a
b=b
c=c
d=d
e=e
f=f
g=g
h=h
i=i
j=j
k=k
l=l
m=m
n=n
o=o
p=p
q=q
r=r
s=s
t=t
u=u
v=v
w=w
x=x
y=y
z=z
