package com.ecco.acceptancetests.api.referral;

import com.ecco.data.client.ServiceOptions;
import com.ecco.test.support.UniqueDataService;
import com.ecco.data.client.actors.*;
import com.ecco.acceptancetests.steps.ReferralSteps;
import com.ecco.data.client.ReferralOptions;
import com.ecco.data.client.steps.ReferralStepDefinitions;
import com.ecco.data.client.steps.ReferralData;
import com.ecco.infrastructure.time.Clock;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.viewModels.WorkflowTaskViewModel;
import com.ecco.webApi.viewModels.WorkflowViewModel;
import com.google.common.collect.ImmutableMap;
import kotlin.Pair;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.Assert.assertTrue;
import static com.ecco.data.client.steps.ReferralStepDefinitions.*;

/**
 * @since 12/10/2016
 */
@SuppressWarnings("unused")
public class ReferralStepsWebApi extends BaseActor implements ReferralSteps {
    private final ReferralActor referralActor;
    private final RestTemplate restTemplate;
    private Clock clock = Clock.DEFAULT;
    private DateTime now = clock.now();
    private final WorkflowActor workflowActor;

    private final UniqueDataService unique = UniqueDataService.instance;

    private final Map<String, BiFunction<ReferralData, String, ReferralData>> commands;

    public ReferralStepsWebApi(RestTemplate restTemplate, ReferralActor referralActor, AgreementActor agreementActor,
                               WorkflowActor workflowActor, SessionDataActor sessionDataActor, ClientActor clientActor,
                               ContactActor contactActor, CalendarActor calendarActor, UserActor userActor, ListDefActor listDefActor,
                               ServiceActor serviceActor, CacheActor cacheActor) {
        this(restTemplate, referralActor, workflowActor,
                new ReferralStepsWebApiSupport(agreementActor, sessionDataActor, referralActor, clientActor, contactActor, calendarActor, userActor, listDefActor, serviceActor, cacheActor));
    }

    public ReferralStepsWebApi(RestTemplate restTemplate, ReferralActor referralActor,
                               WorkflowActor workflowActor, final ReferralStepDefinitions registrar) {
        super(restTemplate);
        this.referralActor = referralActor;
        this.restTemplate = restTemplate;
        this.workflowActor = workflowActor;

        final ImmutableMap.Builder<String, BiFunction<ReferralData, String, ReferralData>> builder = ImmutableMap.builder();
        registrar.registerStepDefinitions(builder);
        commands = builder.build();
    }

    public Pair<Long, String> processReferral(ReferralOptions referralOptions, ServiceOptions service, String firstName, String lastName, String niNumber, String referralComment) {
        ReferralViewModel rvm = processUniqueReferralToViewModel(referralOptions, service, firstName, lastName, referralComment);
        return new Pair<>(rvm.referralId, rvm.getDisplayName());
    }

    @Override
    public long processReferralToId(ReferralOptions referralOptions, ServiceOptions service, String firstName, String lastName, String niNumber, String referralComment) {
        return processUniqueReferralToViewModel(referralOptions, service, firstName, lastName, referralComment).getReferralId();
    }

    private ReferralViewModel processUniqueReferralToViewModel(ReferralOptions referralOptions, ServiceOptions service, String firstName, String lastName, String referralComment) {

        String firstNameUnique = unique.clientFirstNameFor(firstName);
        String lastNameUnique = unique.clientLastNameFor(lastName);
        String clientCode = referralOptions.withClientCode();
        ReferralViewModel referralViewModel = referralActor.createMinimalReferralAndClient(clientCode, firstNameUnique, lastNameUnique, service);

        ReferralData referralData = new ReferralData(referralViewModel, service, referralOptions, getTasksByTaskName(referralViewModel));
        referralData = executeTask(referralData, SOURCE_OF_REFERRAL);
        // Do the commands from ReferralAPITests to match ReferralSteps.editTaskDefinitions
        referralData = editTaskDefinitions(referralData, referralOptions);

        return referralData.referralViewModel;
    }

    @Override
    public void processTaskDefinitions(ReferralOptions referralOptions, ServiceOptions service) {
        throw new UnsupportedOperationException("Not applicable to Web API");
    }

    @Override
    public ReferralViewModel createReferral(boolean unique, boolean existingClientExpected, boolean useExistingClient, String expectedReferralClientName, ReferralOptions options, ServiceOptions serviceName, String firstName, String lastName, LocalDate dob, String niNumber, String gender, String language, String ethnicity) {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    @Override
    public void findClientThenReferral(String clientCode, String firstNamePrefix, String lastNamePrefix, String referralCode) {
        throw new UnsupportedOperationException("Not applicable to Web API");
    }

    @Override
    public List<String> listReferrals() {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    @Override
    public void printReferralOverview(String firstName, String lastName) {
        throw new UnsupportedOperationException("Not applicable to Web API");
    }

    @Override
    public void navigateToAppointments(String firstName, String lastName) {
        throw new UnsupportedOperationException("Not applicable to Web API");
    }

    @Override
    public void exited(String firstName, String lastName, LocalDate exitedDate) {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    @SuppressWarnings("StatementWithEmptyBody")
    public ReferralData editTaskDefinitions(ReferralData state, ReferralOptions options) {

        if (options.requiresDataProtection()) {
            state = completeStep(state, DATA_PROTECTION);
        }
        if (options.requiresEmergencyDetails()) {
            state = completeStep(state, EMERGENCY_DETAILS);
        }
        if (options.requiresSource()) {
            // TODO: editTaskDefinitionSource(options);
        }
        if (options.requiresDeliveredBy()) {
            state = completeStep(state, DELIVERED_BY);
        }
        if (options.requiresProjects() /*&& !referralPage.isDetailsEnabled()*/) { // we must still need to select a project
            // TODO: editTaskDefinitionDestination(options);
        }
        if (options.requiresDetails()) {
            state = completeStep(state, REFERRAL_DETAILS);
        }
        if (options.requiresPendingStatus()) {
            state = completeStep(state, PENDING_STATUS);
        }
        if (options.requiresAppropriateReferral()) {
            state = completeStep(state, APPROPRIATE_REFERRAL);
        }
        if (options.requiresAccommodation()) {
            // TODO: editTaskDefinitionAccommodation(options);
        }
        if (options.requiresInitialAssessment()) {
            state = completeStep(state, SETUP_INITIAL_ASSESSMENT);
        }
        if (options.requiresFunding()) {
            // TODO: editTaskDefinitionFunding(options);
        }
        if (options.requiresAcceptOnService()) {
            state = completeStep(state, ACCEPT_ON_SERVICE);
        }
        if (options.requiresStartAccommodation()) {
            state = completeStep(state, START_ACCOMMODATION);
        } else if (options.requiresStart()) {
            state = completeStep(state, START);
        }
        if (options.withServiceAgreement()) {
            state = completeStep(state, AGREEMENT_OF_APPOINTMENTS);
        }
        if (options.requiresSupportStaffNotes()) {
            // TODO: editReferralCaseNotes(options);
        }
        if (options.hasExitedDate()) {
            completeStep(state, CLOSE_OFF);
        }

        return state;
    }

    public ReferralData completeStep(ReferralData state, String taskName) {
        if (state.referralOptions.shouldDoWorkflow()) {
            workflowActor.claimTask(state.tasks.get(taskName));
        }
        final ReferralData updatedModel = executeTask(state, taskName);
        Map<String, WorkflowTaskViewModel> taskState = getTasksByTaskName(state.referralViewModel);
        // Allow for the task having possibly already been marked completed by command handler
        if (taskState.get(taskName).isCompleted) {
            log.info("Task {} wasn't automatically marked completed", taskName);
            if (!state.referralOptions.shouldDoWorkflow()) {
                workflowActor.markTaskCompleted(state.tasks.get(taskName));
                taskState = getTasksByTaskName(state.referralViewModel); // refresh it
            }
        }
        final ReferralData updatedState = new ReferralData(updatedModel.referralViewModel,
                updatedModel.serviceOptions,
                updatedModel.referralOptions,
                taskState);
        assertTrue("Failed to complete task: " + taskName,
                updatedState.tasks.get(taskName).isCompleted || !state.referralOptions.shouldDoWorkflow());
        log.info("Completed task: {}", taskName);
        return updatedState;
    }

    public ReferralData executeTask(ReferralData referralData, String taskName) {
        return commands.get(taskName).apply(referralData, referralData.taskHandleFor(taskName));
    }

    private Map<String, WorkflowTaskViewModel> getTasksByTaskName(ReferralViewModel rvm) {
        WorkflowViewModel wvm = workflowActor.getTasksForRecipient(rvm.serviceRecipientId).getBody();
        return wvm.tasks.stream()
                .collect(Collectors.toMap(task -> task.taskName, Function.identity()));
    }

}
