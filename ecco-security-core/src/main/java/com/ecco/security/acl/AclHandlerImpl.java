package com.ecco.security.acl;

import com.ecco.security.dao.acl.SpringDataMutableAclDao;
import com.ecco.security.dto.AclEntryDto;
import com.ecco.security.dto.AclExtractor;
import com.ecco.infrastructure.entity.AbstractLongKeyedEntity;
import com.ecco.security.SecurityUtil;
import com.ecco.dom.BaseEntity;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.acls.domain.ObjectIdentityImpl;
import org.springframework.security.acls.domain.PrincipalSid;
import org.springframework.security.acls.model.*;
import org.springframework.util.Assert;

import java.util.*;
import java.util.Map.Entry;

import static java.util.Collections.singletonList;

/**
 * Top level ACL handler, which provides more convenient method arguments and
 * additional methods over MutableAclService which is a spring interface.
 * @see SpringDataMutableAclDao , @see SpringDataAclDao
 */
// see http://static.springsource.org/spring-security/site/docs/3.0.x/apidocs/org/springframework/security/acls/jdbc/JdbcMutableAclService.html
// and http://static.springsource.org/spring-security/site/docs/3.0.x/reference/domain-acls.html
@RequiredArgsConstructor
public class AclHandlerImpl implements AclHandler {

    private static Logger logger = LoggerFactory.getLogger(AclHandlerImpl.class);

    private final AclService aclService;
    /** Individual beans should be created and they'll be wired in as a collection */
    private final Collection<AclExtractor<? extends BaseEntity<Long>>> aclExtractors;

    @Override
    public boolean checkAclExists(String className, long id) {
        ObjectIdentity oid = new ObjectIdentityImpl(className, id);
        Map<ObjectIdentity, Acl> result = aclService.readAclsById(singletonList(oid), null);

        // @see SpringDataAclDao.readAclsById
        return result.containsKey(oid);
    }

    //@CacheEvict(allEntries=true, cacheNames= {"aclVisibilityService", "aclByGroups"})
    // per item
    @Override
    public void createAcl(String className, long id) throws AlreadyExistsException {
        ObjectIdentity oid = new ObjectIdentityImpl(className, id);
        aclService.createAcl(oid);
    }

    @Override
    public Set<AclEntryDto> getAclEntries() {
        return getAclEntries(null);
    }

    @Override
    public Set<AclEntryDto> getAclEntries(PrincipalSid recipient) {
        Set<AclEntryDto> dtos = new HashSet<>();
        for (AclExtractor<? extends BaseEntity<Long>> aclExtractor : aclExtractors) {
            Set<AclEntryDto> aclEntryDtos = getAclEntries(recipient, aclExtractor);
            dtos.addAll(aclEntryDtos);
        }
        return dtos;
    }

    private <T extends BaseEntity<Long>> Set<AclEntryDto> getAclEntries(PrincipalSid recipient,
            AclExtractor<T> aclExtractor) {
        Class<T> clazz = aclExtractor.getClazz();
        List<T> entities = aclExtractor.listObjects();

        // its possible the extractor has no items to secure
        // at which point the SpringDataLookup.readAclsById barfs on an Assert
        if (entities.isEmpty()) {
            return Collections.emptySet();
        }

        // getting all entries from a user perpective is a pain, we need to have knowledge of how we store things and be specific
        // so we provide the ids for the classes we 'look after'

        // then for each of the ids we look after, get their ACLs with the user in question
        List<ObjectIdentity> ois = new ArrayList<>();
        for (BaseEntity<Long> e : entities) {
            ObjectIdentity oi = new ObjectIdentityImpl(clazz, e.getId());
            ois.add(oi);
        }

        // bundle the recipient into a set
        List<Sid> sids = null;
        if (recipient != null) {
            sids = new ArrayList<>();
            sids.add(recipient);
        }

        // get the whole ACLs for the ois (possibly restricted by sids)
        Map<ObjectIdentity, Acl> aclMap = aclService.readAclsById(ois, sids);
        // 'readAclsById' doesn't guarantee only returning the recipients, so we additionally filter it
        Set<AclEntryDto> aclEntrys = deriveAclEntrys(recipient, aclMap);

        return aclEntrys;
    }

    private Set<AclEntryDto> deriveAclEntrys(PrincipalSid sid, Map<ObjectIdentity, Acl> sourceObjectAcls) {

        // the resulting set
        Set<AclEntryDto> avms = new HashSet<>();

        // loop each objects acl
        for (ObjectIdentity oi : sourceObjectAcls.keySet()) {

            logger.debug("deriveAclEntrys: looping existing acl entries " + oi.getType() + ": " + oi.getIdentifier());
            Acl acl = sourceObjectAcls.get(oi);
            List<AccessControlEntry> aces = acl.getEntries();

            // we assume read permission is granted if there are some aces for the identifier
            for (AccessControlEntry ace : aces) {

                // only create the dto where we match
                boolean matched = false;
                if (sid == null) {
                    matched = true;
                } else {
                    if (ace.getSid().equals(sid)) {
                        matched = true;
                    }
                }
                if (matched) {
                    AclEntryDto ae = createAclEntryDto((PrincipalSid) ace.getSid(), oi.getType(), (Long) oi.getIdentifier());
                    logger.debug("deriveAclEntrys: MATCHED acl entry for user; adding existing entry " + ace.getSid() + ": " + oi.getType() + ": " + oi.getIdentifier());
                    avms.add(ae);
                }
            }
        }
        return avms;
    }

    private AclEntryDto createAclEntryDto(PrincipalSid sid, String clazz, long id) {
        Class<?> c;
        try {
            c = Class.forName(clazz);
        } catch (ClassNotFoundException e1) {
            logger.error("ACL class NOT found (acl not applied): " + clazz, e1);
            return null;
        }
        // read is assumed, since currently our acl usage is to get a list of filtered services/projects to use in queries, not on the objects themselves
        // so we just want the list, and read permission is what we use to allow access
        Permission p = org.springframework.security.acls.domain.BasePermission.READ;
        AclEntryDto ae = new AclEntryDto(sid.getPrincipal(), id, c, p);
        return ae;
    }

    @Override
    public void updateAclEntryDifferences(Collection<AclExtractor<? extends BaseEntity<Long>>> aclExtractors, Set<AclEntryDto> toAclEntrys, List<String> usernamesToClear) {

        // gather the set by username
        Map<String, Set<AclEntryDto>> usernameMap = new HashMap<>();
        for (AclEntryDto dto : toAclEntrys) {
            if (usernameMap.containsKey(dto.username)) {
                usernameMap.get(dto.username).add(dto);
            } else {
                Set<AclEntryDto> s = new HashSet<>();
                s.add(dto);
                usernameMap.put(dto.username, s);
            }
        }

        // loop the usernames to update
        for (Entry<String, Set<AclEntryDto>> entry : usernameMap.entrySet()) {
            updateAclEntryDifferences(new PrincipalSid(entry.getKey()), aclExtractors, entry.getValue());
        }

        // loop the usernames to clear
        if (usernamesToClear != null) {
            for (String username : usernamesToClear) {
                updateAclEntryDifferences(new PrincipalSid(username), aclExtractors, null);
            }
        }

    }

    // TODO rewrite updateAclEntryDifferences to be acl-focused, not principal-focused to improve performance, but currently performance not important here
    // updateAclEntryDifference gets every acl-able entity, loads the acls, then manipulates them according to the toAclEntrys
    // however, its focus currently is on the individual user, so it loads the acl-able entities per user provided
    // once loaded, it then adds and deletes accordingly - which it does by re-loading the acl to add or remove for each method!
    // so updateAclEntryDifference could be more efficient if we grouped all the Acl's together instead of per user change per permission
    /*
    // group into acl-keyed set
    Map<ObjectIdentity, List<AclEntryDto>> aclMap = new HashMap<ObjectIdentity, List<AclEntryDto>>();
    for (AclEntryDto ae : toAclEntrys) {
        ObjectIdentity oi = new ObjectIdentityImpl(ae.clazz, ae.secureObjectId);
        if (aclMap.containsKey(oi)) {
            List ael = new ArrayList<AclEntryDto>();
            aclMap.put(oi, ael);
        } else {
            aclMap.get(oi).add(ae);
        }
    }
    // load all the involved acls
    Map<ObjectIdentity, Acl> aclMapSource = mutableAclService.readAclsById(new ArrayList(aclMap.keySet()));
    MutableAcl acl = (MutableAcl) acls.get(new ObjectIdentityImpl(dto.clazz, dto.secureObjectId));
    */

    @Override
    public void addAclEntries(Set<AclEntryDto> addAclEntrys) {
        Assert.notNull(addAclEntrys);

        logger.info("addAclEntry: aclsToAdd: " + addAclEntrys);

        for (AclEntryDto ae : addAclEntrys) {
            BaseEntity<Long> e = new AbstractLongKeyedEntity() {};
            e.setId(ae.getSecureObjectId());
            // this reloads the acl - but presumably this is cached by now
            aclService.setPermission(e, new PrincipalSid(ae.getUsername()), ae.getPermission(), ae.getClazz());
        }
    }

    @Override
    public void removeAclEntries(Set<AclEntryDto> removeAclEntrys) {
        Assert.notNull(removeAclEntrys);

        logger.info("removeAclEntry: aclsToRemove: " + removeAclEntrys);

        for (AclEntryDto ae : removeAclEntrys) {
            BaseEntity<Long> e = new AbstractLongKeyedEntity() {};
            e.setId(ae.getSecureObjectId());
            // this reloads the acl - but presumably this is cached by now
            aclService.deletePermission(e, new PrincipalSid(ae.getUsername()), ae.getPermission(), ae.getClazz());
        }
    }

    @Override
    public void updateAclEntryDifferences(PrincipalSid sid, Collection<AclExtractor<? extends BaseEntity<Long>>> aclExtractors, Set<AclEntryDto> toAclEntrys) {
        // without this, the 'fromAclEntrys' end up holding everything, and so removing everything
        Assert.notNull(sid);
        if (toAclEntrys == null) {
            toAclEntrys = new HashSet<>();
        }

        Set<AclEntryDto> fromAclEntrys = new HashSet<>();
        for (AclExtractor<? extends BaseEntity<Long>> aclExtractor : aclExtractors) {
            Set<AclEntryDto> dtos = getAclEntries(sid, aclExtractor);
            fromAclEntrys.addAll(dtos);
        }
        logger.info("updateAclEntryDifferences: existing acls: " + fromAclEntrys);

        // get the differences
        Set<AclEntryDto> toAdd = new HashSet<>(toAclEntrys);
        Set<AclEntryDto> toRemove = new HashSet<>(fromAclEntrys);
        toAdd.removeAll(fromAclEntrys);
        toRemove.removeAll(toAclEntrys);
        logger.info("updateAclEntryDifferences: aclsToAdd: " + toAdd.size());
        logger.info("updateAclEntryDifferences: aclsToRemove: " + toRemove.size());


        if (!toAdd.isEmpty()) {
            for (AclEntryDto ae : toAdd) {
                logger.info("saving permission: aclsToAdd: " + ae);
                BaseEntity<Long> e = new AbstractLongKeyedEntity() {};
                e.setId(ae.getSecureObjectId());
                aclService.setPermission(e, new PrincipalSid(ae.getUsername()), ae.getPermission(), ae.getClazz());
            }
        }
        if (!toRemove.isEmpty()) {
            for (AclEntryDto ae : toRemove) {
                logger.info("saving permission: aclsToRemove: " + ae);
                BaseEntity<Long> e = new AbstractLongKeyedEntity() {};
                e.setId(ae.getSecureObjectId());
                aclService.deletePermission(e, new PrincipalSid(ae.getUsername()), ae.getPermission(), ae.getClazz());
            }
        }
        logger.info("saving permission: completed");
    }

    @Override
    public Collection<AclExtractor<? extends BaseEntity<Long>>> aclExtractors() {
        return aclExtractors;
    }

    protected String getUsername() {
        return SecurityUtil.getAuthenticatedUsername();
        /*
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        if (auth.getPrincipal() instanceof UserDetails) {
            return ((UserDetails) auth.getPrincipal()).getUsername();
        } else {
            return auth.getPrincipal().toString();
        }
        */
    }


}
