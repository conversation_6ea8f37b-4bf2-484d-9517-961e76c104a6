package com.ecco.security.dto;

import java.util.List;

import com.ecco.dom.BaseEntity;
import com.ecco.dom.IdName;
import com.ecco.dom.Identified;

/**
 * An AclExtractor understands the domain objects and classes that ACLs operate on
 * @param <T> The class of the ACL for which this AclExtractor works
 */
public interface AclExtractor<T extends Identified<Long>> {

    /**
     * The list of entities to gather ACLs for
     * @return
     */
    List<T> listObjects();

    /**
     * @return The class of the ACL for which this AclExtractor works, see {@param <T>}
     */
    Class<T> getClazz();

}
