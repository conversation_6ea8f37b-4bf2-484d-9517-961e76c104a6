package com.ecco.security.dao.acl;

import com.ecco.security.acl.dom.AclObjectIdentity;
import com.google.common.base.Function;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import org.hibernate.proxy.HibernateProxy;
import org.springframework.security.acls.domain.ObjectIdentityImpl;
import org.springframework.security.acls.jdbc.LookupStrategy;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.AclService;
import org.springframework.security.acls.model.NotFoundException;
import org.springframework.security.acls.model.ObjectIdentity;
import org.springframework.security.acls.model.Sid;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class SpringDataAclDao implements AclService {
    final AclObjectIdentityRepository objectIdentityRepository;
    private final LookupStrategy lookupStrategy;

    public SpringDataAclDao(AclObjectIdentityRepository objectIdentityRepository, LookupStrategy lookupStrategy) {
        Assert.notNull(objectIdentityRepository, "AclObjectIdentityRepository required");
        Assert.notNull(lookupStrategy, "LookupStrategy required");
        this.objectIdentityRepository = objectIdentityRepository;
        this.lookupStrategy = lookupStrategy;
    }

    @Override
    public List<ObjectIdentity> findChildren(ObjectIdentity parentIdentity) {
        validateObjectIdentity(parentIdentity);

        Iterable<AclObjectIdentity> objectIdentities = objectIdentityRepository.findByParentObject_ObjectIdClass_ClassNameAndParentObject_ObjectIdIdentity(parentIdentity.getType(), (Long) parentIdentity.getIdentifier());
        return ImmutableList.copyOf(Iterables.transform(objectIdentities, new Function<AclObjectIdentity, ObjectIdentity>() {
            @Override
            public ObjectIdentity apply(AclObjectIdentity input) {
                return new ObjectIdentityImpl(input.getObjectIdClass().getClassName(), input.getObjectIdIdentity());
            }
        }));
    }

    @Override
    public Acl readAclById(ObjectIdentity object) throws NotFoundException {
        return readAclById(object, null);
    }

    @Override
    public Acl readAclById(ObjectIdentity object, List<Sid> sids) throws NotFoundException {
        Map<ObjectIdentity, Acl> map = readAclsById(Arrays.asList(object), sids);
        Assert.isTrue(map.containsKey(object), "There should have been an Acl entry for ObjectIdentity " + object);

        return map.get(object);
    }

    @Override
    public Map<ObjectIdentity, Acl> readAclsById(List<ObjectIdentity> objects) throws NotFoundException {
        return readAclsById(objects, null);
    }

    @Override
    public Map<ObjectIdentity, Acl> readAclsById(List<ObjectIdentity> objects, List<Sid> sids) throws NotFoundException {
        for (ObjectIdentity oid : objects) {
            // NB could use AntiProxyUtils
            Assert.isTrue(!(oid instanceof HibernateProxy), "ObjectIdentity should not be a hibernate proxy");
            validateObjectIdentity(oid);
        }

        Map<ObjectIdentity, Acl> result = lookupStrategy.readAclsById(objects, sids);

        // Check every requested object identity was found (throw NotFoundException if needed)
        for (ObjectIdentity oid : objects) {
            if (!result.containsKey(oid)) {
                throw new NotFoundException("Unable to find ACL information for object identity '" + oid + "'");
            }
        }

        return result;
    }

    void validateObjectIdentity(ObjectIdentity objectIdentity) {
        Assert.notNull(objectIdentity, "Object Identity required");
        Assert.notNull(objectIdentity.getIdentifier(), "Object Identity doesn't provide an identifier");
        try {
            Long id = (Long) objectIdentity.getIdentifier();
        } catch (ClassCastException e) {
            throw new IllegalArgumentException("SpringDataAclDao supports Long object identities only - not " + objectIdentity.getIdentifier().getClass());
        }
    }
}
