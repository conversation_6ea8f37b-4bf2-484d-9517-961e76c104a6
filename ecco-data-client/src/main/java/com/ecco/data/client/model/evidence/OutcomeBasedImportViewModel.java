package com.ecco.data.client.model.evidence;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.HashMap;

@Getter
@Setter
public class OutcomeBasedImportViewModel extends BaseEvidenceImportViewModel {

    public BigDecimal mileageTo;
    public BigDecimal mileageDuring;
    public String targetSchedule;

    /**
     * Map from smart step name to EvidenceGoalImportViewModel
     * Allows us to build a picture of each smart step in one piece of work.
     * eg goals["maximise income"].wanted with spEL #this.equals('Yes') ? True : False
     */
    public HashMap<String, GoalImportViewModel> goals = new HashMap<>();

}
