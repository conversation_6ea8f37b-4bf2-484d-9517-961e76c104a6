package com.ecco.data.client.model.evidence;

import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
public class GoalImportViewModel {

    public Integer referralId; // temporarily whilst the report doesn't have an srId column
    public Integer serviceRecipientId;
    public UUID actionInstanceUuid;
    public Integer actionDefId;
    public String evidenceTask;

    /**
     * Sets the smart step as wanted
     * NB {@link achieved} takes priority
     */
    public boolean wanted;

    /**
     * Sets the smart step as achieved
     */
    public boolean achieved;

}
