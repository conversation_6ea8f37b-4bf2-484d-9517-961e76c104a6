package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.model.ServiceRecipientCommandImportViewModel;
import com.ecco.webApi.evidence.BaseCommandViewModel;
import org.springframework.web.client.RestTemplate;

public class <PERSON><PERSON>andler extends AbstractHandler<ServiceRecipientCommandImportViewModel> {

    public CommandHandler(RestTemplate restTemplate) {
        super(restTemplate);
    }

    @Override
    protected void processEntity(ImportOperation<ServiceRecipientCommandImportViewModel> operation) {

        ServiceRecipientCommandImportViewModel input = operation.record;

        //var cmd = new GoalUpdateCommandViewModel(
        //syncCommandToServer(operation.baseUri.concat(apiPath), cmd);
    }

    private long syncCommandToServer(String uri, BaseCommandViewModel input) {
        com.ecco.webApi.viewModels.Result result = executeCommand(uri, input);
        return Long.parseLong(result.getId());
    }

}
