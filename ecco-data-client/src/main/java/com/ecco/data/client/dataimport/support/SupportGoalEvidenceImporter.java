package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.dataimport.csv.CSVBeanReader;
import com.ecco.data.client.model.evidence.GoalImportViewModel;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.io.CharSource;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Map;

public class SupportGoalEvidenceImporter {

    protected final Map<String, String> fieldAliases = ImmutableMap.<String,String>builder()
            // the evidence
            .build();

    protected final RestTemplate restTemplate;

    public SupportGoalEvidenceImporter(RestTemplate template) {
        this.restTemplate = template;
    }

    public void read(CharSource source) {

      final Map<String, String> allSynonyms = ImmutableMap.<String,String>builder().putAll(Synonyms.synonymsToNormalizedMap).build();
      try {
        new CSVBeanReader<>(source, GoalImportViewModel.class, fieldAliases, allSynonyms)
                .readUsing(new SupportGoalEvidenceHandler(restTemplate));
      } catch (IOException e) {
        throw Throwables.propagate(e);
      }
    }

}
