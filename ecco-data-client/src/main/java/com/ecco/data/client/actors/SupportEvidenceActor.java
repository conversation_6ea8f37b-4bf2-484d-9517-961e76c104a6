package com.ecco.data.client.actors;

import com.ecco.acceptancetests.api.jackson.SliceImpl;
import com.ecco.dao.EvidenceAttachmentViewModel;
import com.ecco.dom.EvidenceGroup;
import com.ecco.webApi.evidence.SupportSmartStepsSnapshotViewModel;
import com.ecco.webApi.evidence.EvidenceSupportWorkViewModel;
import com.google.common.collect.ImmutableMap;
import org.joda.time.DateTime;
import org.joda.time.Instant;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nullable;
import java.util.List;
import java.util.UUID;

import static org.joda.time.DateTimeZone.UTC;

public class SupportEvidenceActor extends BaseActor {


    public SupportEvidenceActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public ResponseEntity<SliceImpl<EvidenceSupportWorkViewModel>> findAllSupportWorkSummaryByServiceRecipientId(
            int serviceRecipientId,
            EvidenceGroup evidenceGroup) {
        return findSupportWorkSummaryByServiceRecipientId("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/",
                serviceRecipientId, evidenceGroup);
    }

    public ResponseEntity<EvidenceSupportWorkViewModel> findOneSupportWorkByWorkUuid(
            int serviceRecipientId,
            EvidenceGroup evidenceGroup,
            UUID uuid
    ) {
        return restTemplate.getForEntity(apiBaseUrl + "service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/uuid/{uuid}/",
                EvidenceSupportWorkViewModel.class,
                ImmutableMap.of(
                        "serviceRecipientId", serviceRecipientId,
                        "evidenceGroup", evidenceGroup.nameAsLowercase(),
                        "uuid", uuid.toString()
                ));
    }

    public ResponseEntity<SliceImpl<EvidenceSupportWorkViewModel>> findAttachmentsSupportWorkSummaryByServiceRecipientId(
            int serviceRecipientId,
            EvidenceGroup evidenceGroup) {
        return findSupportWorkSummaryByServiceRecipientId("service-recipients/{serviceRecipientId}/evidence/needs/{evidenceGroup}/attachments/",
                serviceRecipientId, evidenceGroup);
    }

    public ResponseEntity<List<EvidenceAttachmentViewModel>> findAttachmentsByServiceRecipientId(int serviceRecipientId) {
        ParameterizedTypeReference<List<EvidenceAttachmentViewModel>> typeRef = new ParameterizedTypeReference<>() {};
        return restTemplate.exchange(apiBaseUrl + "service-recipients/{serviceRecipientId}/evidence/attachments/",
                HttpMethod.GET, null, typeRef,
                ImmutableMap.of("serviceRecipientId", serviceRecipientId));
    }

    public ResponseEntity<SliceImpl<EvidenceSupportWorkViewModel>> findHactOnlySupportWorkSummaryByServiceRecipientId(
            int serviceRecipientId,
            EvidenceGroup evidenceGroup) {
        return findSupportWorkSummaryByServiceRecipientId("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/?hactOnly=true",
                serviceRecipientId, evidenceGroup);
    }

    public ResponseEntity<SliceImpl<EvidenceSupportWorkViewModel>> findStatusChangeOnlySupportWorkSummaryByServiceRecipientId(
            int serviceRecipientId,
            EvidenceGroup evidenceGroup) {
        return findSupportWorkSummaryByServiceRecipientId("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/?statusChangeOnly=true",
                serviceRecipientId, evidenceGroup);
    }

    private ResponseEntity<SliceImpl<EvidenceSupportWorkViewModel>> findSupportWorkSummaryByServiceRecipientId(
            String url,
            int serviceRecipientId,
            EvidenceGroup evidenceGroup) {
        // getForEntity doesn't support Iterable, so use exchange - see http://stackoverflow.com/questions/6173182/spring-json-convert-a-typed-collection-like-listmypojo
        ParameterizedTypeReference<SliceImpl<EvidenceSupportWorkViewModel>> typeRef = new ParameterizedTypeReference<>() {};
        return restTemplate.exchange(apiBaseUrl + url,
                HttpMethod.GET, null, typeRef,
                ImmutableMap.of("serviceRecipientId", serviceRecipientId, "evidenceGroup", evidenceGroup.nameAsLowercase()));
    }

    // TODO refactor to use the underlying /snapshots
    public ResponseEntity<SupportSmartStepsSnapshotViewModel> findLatestSupportSnapshot(int serviceRecipientId, EvidenceGroup evidenceGroup) {
        ResponseEntity<SupportSmartStepsSnapshotViewModel> response = restTemplate.getForEntity(apiBaseUrl +
                        "service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/snapshots/latest/",
                SupportSmartStepsSnapshotViewModel.class,
                ImmutableMap.of("serviceRecipientId", serviceRecipientId, "evidenceGroup", evidenceGroup.nameAsLowercase()));
        return response;
    }

    public ResponseEntity<SupportSmartStepsSnapshotViewModel> findTimestampSupportSnapshot(int serviceRecipientId, EvidenceGroup evidenceGroup, DateTime workDate, @Nullable Instant created) {
        var t = ISODateTimeFormat.dateTime().print(workDate);
        return (created != null)
            ? restTemplate.getForEntity(apiBaseUrl + "service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/snapshots/?workDate={workdate}&created={created}",
                    SupportSmartStepsSnapshotViewModel.class,
                    ImmutableMap.of("serviceRecipientId", serviceRecipientId, "evidenceGroup", evidenceGroup.nameAsLowercase(),
                            "workdate", ISODateTimeFormat.dateTime().print(workDate),
                            "created", ISODateTimeFormat.dateTime().print(created.toDateTime(UTC))))
            : restTemplate.getForEntity(apiBaseUrl + "service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/snapshots/",
                SupportSmartStepsSnapshotViewModel.class,
                ImmutableMap.of("serviceRecipientId", serviceRecipientId, "evidenceGroup", evidenceGroup.nameAsLowercase()));
    }
}
