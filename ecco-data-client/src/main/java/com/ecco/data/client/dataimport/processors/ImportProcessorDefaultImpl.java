package com.ecco.data.client.dataimport.processors;

import com.ecco.data.client.dataimport.support.*;
import com.ecco.infrastructure.rest.RestClient;
import com.ecco.infrastructure.rest.ThrowOnlyOn400And5xxErrorHandler;
import com.google.common.io.CharSource;

import org.springframework.web.client.RestTemplate;

public class ImportProcessorDefaultImpl extends ImportProcessorBase {

    private final CommandImporter commandImporter;

    private final ReferralAggregateImporter referralAggregateImporter;

    private final AssociatedContactImporter associatedContactImporter;

    private final AgenciesImporter agenciesImporter;

    private final SupportEvidenceImporter supportEvidenceImporter;

    private final SupportGoalEvidenceImporter supportGoalEvidenceImporter;

    private final QuestionnaireEvidenceImporter questionnaireEvidenceImporter;

    private final AclImporter aclImporter;

    private final WorkerImporter workerImporter;

    private final BuildingImporter buildingImporter;

    private final ListDefImporter listDefImporter;

    private final TaskStatusImporter taskStatusImporter;

    private final AppointmentScheduleImporter scheduleImporter;

    private final ServiceImporter serviceImporter;

    public ImportProcessorDefaultImpl(RestClient restClient) {
        super(restClient);
        RestTemplate template = restClient.template();
        template.setErrorHandler(new ThrowOnlyOn400And5xxErrorHandler());
        commandImporter = new CommandImporter(template);
        referralAggregateImporter = new ReferralAggregateImporter(template);
        agenciesImporter = new AgenciesImporter(template);
        associatedContactImporter = new AssociatedContactImporter(template);
        supportEvidenceImporter = new SupportEvidenceImporter(template);
        supportGoalEvidenceImporter = new SupportGoalEvidenceImporter(template);
        questionnaireEvidenceImporter = new QuestionnaireEvidenceImporter(template);
        aclImporter = new AclImporter(template);
        workerImporter = new WorkerImporter(template);
        buildingImporter = new BuildingImporter(template);
        listDefImporter = new ListDefImporter(template);
        taskStatusImporter = new TaskStatusImporter(template);
        scheduleImporter = new AppointmentScheduleImporter(template);
        serviceImporter = new ServiceImporter(template);
    }

    @Override
    public void process(CharSource... charSources) {
        // TODO add a column to the header row with an enum for 'importType' - referral / outcome / agency etc
        importReferralAggregate(charSources[0]);
    }

    public void importCommand(CharSource source) {
        commandImporter.read(source);
    }

    public void importReferralAggregate(CharSource source) {
        referralAggregateImporter.read(source);
    }

    public void importAgencies(CharSource source) {
        agenciesImporter.read(source);
    }

    public void importAssociatedContacts(CharSource source) {
        associatedContactImporter.read(source);
    }

    public void importOutcomeEvidence(CharSource source) {
        supportEvidenceImporter.read(source);
    }

    public void importGoalEvidence(CharSource source) {
        supportGoalEvidenceImporter.read(source);
    }

    public void importQuestionEvidence(CharSource source) {
        questionnaireEvidenceImporter.read(source);
    }

    public void importAcls(CharSource source) {
        aclImporter.read(source);
    }

    public void importWorkers(CharSource source) {
        workerImporter.read(source);
    }

    public void importBuildings(CharSource source) { buildingImporter.read(source); }

    public void importListDef(CharSource source) {
        listDefImporter.read(source);
    }

    public void importSchedules(CharSource source) {
        scheduleImporter.read(source);
    }

    public void importServices(CharSource source) {
        serviceImporter.read(source);
    }

    public void importTaskStatus(CharSource source) {
        taskStatusImporter.read(source);
    }

}
