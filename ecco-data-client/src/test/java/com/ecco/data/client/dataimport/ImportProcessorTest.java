package com.ecco.data.client.dataimport;

import com.ecco.data.client.ClientConfig;
import com.ecco.data.client.dataimport.processors.ImportProcessorDefaultImpl;
import com.ecco.infrastructure.rest.RestClient;
import com.ecco.webApi.viewModels.Result;
import com.google.common.base.Charsets;
import com.google.common.io.CharSource;
import com.google.common.io.Resources;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class ImportProcessorTest {

    private static final String SAMPLE_COMMAND_IMPORT_CSV = "sampleCommandImport.csv";
    private static final String SAMPLE_ACL_IMPORT_CSV = "sampleAclImport.csv";
    private static final String SAMPLE_AGENCY_IMPORT_CSV = "sampleAgencyImport.csv";
    private static final String SAMPLE_ASSOC_CONTACT_IMPORT_CSV = "sampleAssocContactImport.csv";
    private static final String SAMPLE_CLIENT_IMPORT_CSV = "sampleClientImport.csv";
    private static final String SAMPLE_EVIDENCE_IMPORT_CSV = "sampleEvidenceImport.csv";
    private static final String SAMPLE_EVIDENCE_GOAL_IMPORT_CSV = "sampleGoalEvidenceImport.csv";
    private static final String SAMPLE_QUESTIONNAIRE_IMPORT_CSV = "sampleQuestionnaireImport.csv";
    private static final String SAMPLE_WORKER_IMPORT_CSV = "sampleWorkerImport.csv";
    private static final String SAMPLE_BUILDING_IMPORT_CSV = "sampleBuildingImport.csv";
    private static final String SAMPLE_TASK_IMPORT_CSV = "sampleTaskImport.csv";
    private static final String SAMPLE_SCHEDULE_IMPORT_CSV = "sampleScheduleImport.csv";
    private static final String SAMPLE_LISTDEF_IMPORT_CSV = "sampleListDefImport.csv";
    private static final String SAMPLE_SERVICE_IMPORT_CSV = "sampleServiceImport.csv";

    @Mock private RestClient restClient;
    @Mock private RestTemplate restTemplate;

    private ImportProcessorDefaultImpl defaultProcessor;

    @Before
    public void before() {
        Mockito.when(restClient.template()).thenReturn(restTemplate);
        Mockito.when(restTemplate.postForEntity(any(String.class), any(), same(Result.class)))
            .thenReturn(new ResponseEntity<Result>(new Result(1), HttpStatus.CREATED));
        restClient.setTemplate(restTemplate);

        System.setProperty("target.host.url", "http://localhost/ecco-war"); // Fake to get us through tests

        defaultProcessor = new ImportProcessorDefaultImpl(restClient);
        defaultProcessor.login();
    }

    @Test
    public void importClientCSVShouldParseCorrectlyButLogFailures() {
        CharSource source = Resources.asCharSource(Resources.getResource(SAMPLE_CLIENT_IMPORT_CSV), Charsets.UTF_8);
        defaultProcessor.importReferralAggregate(source);
    }

    @Test
    public void importAgenciesCSVShouldParseCorrectlyButLogFailures() {
        CharSource source = Resources.asCharSource(Resources.getResource(SAMPLE_AGENCY_IMPORT_CSV), Charsets.UTF_8);
        defaultProcessor.importAgencies(source);
    }

//    @Test
//    @Ignore("Impl needs converting to commands")
//    public void importEvidenceCSVShouldParseCorrectlyButLogFailures() {
//        ReferralViewModel rvm = new ReferralViewModel();
//        rvm.setReferralId(101L);
//        ResponseEntity<ReferralViewModel> responseEntity = new ResponseEntity<>(rvm, HttpStatus.OK);
//        Mockito.when(restTemplate.getForEntity(argThat(any(String.class)), argThat(any(Class.class)), (Object[])anyVararg()))
//            .thenReturn(responseEntity);
//        CharSource source = Resources.asCharSource(Resources.getResource(SAMPLE_EVIDENCE_IMPORT_CSV), Charsets.UTF_8);
//        defaultProcessor.importOutcomeEvidence(source);
//    }

    @Test
    public void importWorkerCSVShouldParseCorrectlyButLogFailures() {
        CharSource source = Resources.asCharSource(Resources.getResource(SAMPLE_WORKER_IMPORT_CSV), Charsets.UTF_8);
        defaultProcessor.importWorkers(source);
    }

    /**
     * Test that no match on separator returns the whole string!
     */
    @Test
    public void spelTest() {
        String str = "this is , my string";
        StandardEvaluationContext itemContext = new StandardEvaluationContext(str);
        SpelExpressionParser spel = new SpelExpressionParser();
        String[] parts = str.split(";");
        Assert.assertEquals(parts[0], str);
    }

    public static void main(String[] args) {

        try (AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(ClientConfig.class)) {
            RestClient restClient = context.getBean(RestClient.class);
            //importCommand(restClient);
            //importAssociatedContacts(restClient);
            //importAcls(restClient);
            //importAgencies(restClient);
            //importClients(restClient);
            //importOutcomeEvidence(restClient);
            //importGoalEvidence(restClient);
            //importQuestionEvidence(restClient);
            //importWorkers(restClient);
            //importBuildings(restClient);
            //importListDefs(restClient);
            //importTasks(restClient);
            //importSchedules(restClient);
            //importServices(restClient);
        }
    }

    private static void importCommand(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource evidenceSrc = Resources.asCharSource(Resources.getResource(SAMPLE_COMMAND_IMPORT_CSV), Charsets.UTF_8);
        processor.importCommand(evidenceSrc);

        processor.logout();
    }

    private static void importAcls(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource aclsSrc = Resources.asCharSource(Resources.getResource(SAMPLE_ACL_IMPORT_CSV), Charsets.UTF_8);
        processor.importAcls(aclsSrc);

        processor.logout();
    }

    private static void importAgencies(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource agenciesSrc = Resources.asCharSource(Resources.getResource(SAMPLE_AGENCY_IMPORT_CSV), Charsets.UTF_8);
        processor.importAgencies(agenciesSrc);

        processor.logout();
    }

    private static void importAssociatedContacts(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource evidenceSrc = Resources.asCharSource(Resources.getResource(SAMPLE_ASSOC_CONTACT_IMPORT_CSV), Charsets.UTF_8);
        processor.importAssociatedContacts(evidenceSrc);

        processor.logout();
    }

    private static void importClients(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();
        CharSource source = Resources.asCharSource(Resources.getResource(SAMPLE_CLIENT_IMPORT_CSV), Charsets.UTF_8);
        processor.process(source);
        processor.logout();
    }

    private static void importOutcomeEvidence(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource evidenceSrc = Resources.asCharSource(Resources.getResource(SAMPLE_EVIDENCE_IMPORT_CSV), Charsets.UTF_8);
        processor.importOutcomeEvidence(evidenceSrc);

        processor.logout();
    }

    private static void importGoalEvidence(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource evidenceSrc = Resources.asCharSource(Resources.getResource(SAMPLE_EVIDENCE_GOAL_IMPORT_CSV), Charsets.UTF_8);
        processor.importGoalEvidence(evidenceSrc);

        processor.logout();
    }

    private static void importQuestionEvidence(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource evidenceSrc = Resources.asCharSource(Resources.getResource(SAMPLE_QUESTIONNAIRE_IMPORT_CSV), Charsets.UTF_8);
        processor.importQuestionEvidence(evidenceSrc);

        processor.logout();
    }

    /**
     * Represents the actual run-through of an import of workers, where the csv files are
     * temporarily pasted into the src/test/resources folder
     */
    private static void importWorkers(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource workersSrc = Resources.asCharSource(Resources.getResource(SAMPLE_WORKER_IMPORT_CSV), Charsets.UTF_8);
        processor.importWorkers(workersSrc);

        processor.logout();
    }

    /**
     * Represents the actual run-through of an import of buildings, where the csv files are
     * temporarily pasted into the src/test/resources folder
     */
    private static void importBuildings(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource source = Resources.asCharSource(Resources.getResource(SAMPLE_BUILDING_IMPORT_CSV), Charsets.UTF_8);
        processor.importBuildings(source);

        processor.logout();
    }

    /**
     * Represents the actual run-through of an import of tasks, where the csv files are
     * temporarily pasted into the src/test/resources folder
     */
    private static void importTasks(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource source = Resources.asCharSource(Resources.getResource(SAMPLE_TASK_IMPORT_CSV), Charsets.UTF_8);
        processor.importTaskStatus(source);

        processor.logout();
    }

    /**
     * Represents the actual run-through of an import of list defs, where the csv files are
     * temporarily pasted into the src/test/resources folder
     */
    private static void importListDefs(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource listDefSrc = Resources.asCharSource(Resources.getResource(SAMPLE_LISTDEF_IMPORT_CSV), Charsets.UTF_8);
        processor.importListDef(listDefSrc);

        processor.logout();
    }

    private static void importSchedules(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource evidenceSrc = Resources.asCharSource(Resources.getResource(SAMPLE_SCHEDULE_IMPORT_CSV), Charsets.UTF_8);
        processor.importSchedules(evidenceSrc);

        processor.logout();
    }

    private static void importServices(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource src = Resources.asCharSource(Resources.getResource(SAMPLE_SERVICE_IMPORT_CSV), Charsets.UTF_8);
        processor.importServices(src);

        processor.logout();
    }

    /**
     * Represets the actual run-through of ML patch B/C import, where the csv files are
     * temporarily pasted into the src/test/resources folder
     */
    private static void importML(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("magentaJun2014.csv"), Charsets.UTF_8);
        processor.importReferralAggregate(referralClientsSrc);

        processor.logout();
    }

    /**
     * Represets the actual run-through of CS import, where the csv files are
     * temporarily pasted into the src/test/resources folder
     */
    private static void importCS(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("liverpool_processed.csv"), Charsets.UTF_8);
        //CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("northlincs_processed.csv"), Charsets.UTF_8);
        //CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("rochdale_processed.csv"), Charsets.UTF_8);
        //CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("wigan_processed.csv"), Charsets.UTF_8);
        processor.importReferralAggregate(referralClientsSrc);

        processor.logout();
    }

    /**
     * Represents the actual run-through of Calico import, where the csv files are
     * temporarily pasted into the src/test/resources folder
     */
    private static void importCalico(RestClient restClient) {
        ImportProcessorDefaultImpl processor = new ImportProcessorDefaultImpl(restClient);
        processor.login();

        // only used to show the missing workers - using a hack in WorkerHandler
        //CharSource workers = Resources.asCharSource(Resources.getResource("02_ECCO North_referral.csv"), Charsets.UTF_8);
        //processor.importWorkers(workers);

        // import that handles several 'pass overs' of the file for different services
        // PHASE 1 - move 'INITIAL SCREENING' headers in place
        // PHASE 2 - move 'SUPPORT ALLOCATION' headers in place
        // PHASE 3 - move 'STREAMS' headers in place
        // PHASE 4 - move 'MAINTENANCE' headers in place
        CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("sampleCalicoImport.csv"), Charsets.UTF_8);
        processor.importReferralAggregate(referralClientsSrc);

        // PHASE 5 - move 'OUTCOME IDENTIFIED/ACHIEVED' headers in place (move out the initial client headers)
        //CharSource evidenceSPSrc = Resources.asCharSource(Resources.getResource("02_ECCO North_referral.csv"), Charsets.UTF_8);
        //processor.importEvidence(evidenceSPSrc);

        processor.logout();
    }

    /**
     * Represets the actual full run-through of RT import, where the csv files are saved in shared encrypted drive
     * (temporarily pasted into the src/test/resources folder)
     */
    private static void importRT(RestClient restClient) {
        /*ImportProcessorRTImpl processor = new ImportProcessorRTImpl(restClient);
        processor.login();

        CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("update_test.csv"), Charsets.UTF_8);
        processor.updateReferralAggregate(referralClientsSrc);

        // AUTOMATIC, would work except we haven't done anything with webApi on creating services/projects
        // nor with assigning them to the referral (also different services depending on the relationship)
        //processor.process(staffSrc, referralClientsSrc, referralSiblingsSrc, referralParentsSrc);


        // MANUALLY by running the methods separately

        // inserted service, projects and regions
        //processor.ensureAcls();

        //CharSource staffSrc = Resources.asCharSource(Resources.getResource("ecco_Users_processed.csv"), Charsets.UTF_8);
        //processor.importStaff(staffSrc);

        //CharSource referralClientsSrc = Resources.asCharSource(Resources.getResource("ecco_Clients_processed.csv"), Charsets.UTF_8);
        //processor.importReferralAggregate(referralClientsSrc);

        //CharSource referralSiblingsSrc = Resources.asCharSource(Resources.getResource("ecco_Siblings_processed.csv"), Charsets.UTF_8);
        //processor.importReferralRelations(referralSiblingsSrc);

        //CharSource referralRelationsSrc = Resources.asCharSource(Resources.getResource("ecco_Parents_processed.csv"), Charsets.UTF_8);
        //processor.importReferralRelations(referralRelationsSrc);

        //CharSource evidenceSPSrc = Resources.asCharSource(Resources.getResource("ecco_Evidence_SP.csv"), Charsets.UTF_8);
        //processor.importSupportEvidence(evidenceSPSrc);

        //CharSource evidenceRASrc = Resources.asCharSource(Resources.getResource("ecco_Evidence_RA.csv"), Charsets.UTF_8);
        //processor.importRiskEvidence(evidenceRASrc);

        //CharSource evidenceNotesSrc = Resources.asCharSource(Resources.getResource("ecco_Evidence_Notes.csv"), Charsets.UTF_8);
        //processor.importSupportEvidence(evidenceNotesSrc);

        processor.logout();*/
    }

}
