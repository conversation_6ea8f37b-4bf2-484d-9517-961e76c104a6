mappingRow,firstRow,lastRow,skipRowsEnabled,skipRowsColumnName,spELOperationsEnabled,,,baseUrl
19,23,23,TRUE,skipRow,TRUE,,,http://localhost:8888/ecco-war
*************,*************,*************,,,,,,http://localhost:8899
See,IMPORT GUIDE.txt ,for instrs,,,,,,http://localhost:8888/ecco-war
*************,*************,*************,,,,,,
,,,,,,,,
,,,,,,,,
,,,,,,,,
,CAREFUL - open encoding as per original,,,,,,,
,CAREFUL - SpeL split returns whole string when can't split,,,,,,,
,CAREFUL - split returns whole string,,,,,,,
,,,,,,,,
,,,,,,,,
,,,,,,,,
,,,,,,,,
,,,,,,,,
,,,,,,,,
min-schedule import row 19,,,,,,,,
,serviceRecipientId,,,evidenceTask,skipRow,actionDefId,actionInstanceUuid,
,,,,'needsAssessment',"#this.equals(""1"") ? True : False",,,
,,,,,,,,
,,,,,,,,
,200014,,,,0,100,4d88bf9d-0dae-46a0-7abe-5bb344e8fa60,
