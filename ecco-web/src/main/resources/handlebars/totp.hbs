{{#> partials/rich-client-page
        classes="login"
        bootstrapRequired="true"
        calendarRequired="true"
        jqplotRequired="true"
        title=""
        importRequireJsModules="evidence/customform/guidance-page"
}}
    <div class="clearfix">
        <a href="{{applicationProperties.applicationRootPath}}" class="navbar-brand">
            <img src="{{applicationProperties.resourceRootPath}}themes/ecco/images/logo_ecco.png" alt="logo" /></a>
        <span class="navbar-title"><small>{{siteTitle}}</small> {{title}}</span>
    </div>

    <div id="login" class="center-block" style="max-width:366px">
        <div class="box clearfix">
            <div class="col-xs-12">
                <form id="totpForm" action="" method="post">

                {{#if register}}
                    <p><span class="badge">1</span> download/open the authentication app chosen by your organisation</p>
                    <p><span class="badge">2</span> scan this code with the authentication app</p>
                    <div class="center-block" style="width: 50%;">
                        <a href="{{otpAuthUrl}}">
                            <img alt="Authenticator app QR code" style="border: 1px solid"
                                 src="{{QR_url}}"/>
                        </a>
                    </div>
                {{/if}}

                <p style="margin-top: 50px;">
                    {{#if register}}<span class="badge">3</span>{{/if}}
                    please enter the code displaying in the authentication app</p>
                <div id="totp-group" class="form-group">
                    <label class="sr-only">code</label>
                    <div class="input-group">
                        <span class="input-group-addon"><i class="glyphicon glyphicon glyphicon-lock"></i></span>
                        <input type="number" maxlength="10" name="totp" placeholder="code"
                               class="password form-control" onfocus="fieldsInUse=true;"
                               autocomplete="off" autofocus
                        />
                    </div>
                    <span id="totp-error" class="help-block"></span>
                    {{#if invalid eq 'totp'}}
                        <p class="alert alert-warning">invalid code, please try again</p>
                    {{/if}}
                </div>
                <input type="submit" class="btn btn-primary pull-right" value="validate">
                </form>
            </div>
        </div>
    </div>
{{/partials/rich-client-page}}