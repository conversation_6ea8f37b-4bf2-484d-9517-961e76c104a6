package com.ecco.config.notifications;

import com.ecco.dom.notifications.NotificationCommand;
import com.ecco.dom.notifications.NotificationRecipient;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.ecco.repositories.notifications.NotificationCommandRepository;
import com.ecco.repositories.notifications.NotificationRepository;
import com.ecco.users.commands.UserCommand;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration(proxyBeanMethods = false)
@EnableJpaRepositories(
    basePackageClasses = {NotificationCommandRepository.class, NotificationRepository.class},
    repositoryBaseClass = QueryDslJpaEnhancedRepositoryImpl.class
)
@EntityScan(basePackageClasses = {NotificationRecipient.class, NotificationCommand.class, UserCommand.class})
public class NotificationConfig {

}
