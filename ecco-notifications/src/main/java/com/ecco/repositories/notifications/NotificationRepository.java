package com.ecco.repositories.notifications;

import com.ecco.dom.notifications.NotificationRecipient;
import org.springframework.data.domain.Pageable;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface NotificationRepository extends CrudRepository<NotificationRecipient, NotificationRecipient.CompositeKey>,
        QuerydslPredicateExecutor<NotificationRecipient> {

    /**
     * Find notifications for a specific user
     */
    List<NotificationRecipient> findByUserId(long userId, Pageable pageable);

    /**
     * Find unread notifications for a specific user
     */
    List<NotificationRecipient> findByUserIdAndReadAtIsNull(long userId, Pageable pageable);

    /**
     * Find all unread notifications for a specific user (without pagination)
     */
    List<NotificationRecipient> findByUserIdAndReadAtIsNull(long userId);

    /**
     * Find a specific notification by ID and user ID
     */
    Optional<NotificationRecipient> findByCommandUuidAndUserId(UUID commandUuid, long userId);

    /**
     * Count unread notifications for a specific user
     */
    long countByUserIdAndReadAtIsNull(long userId);
}
