package com.ecco.dom.notifications;

import com.ecco.users.commands.UserCommand;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Getter
@Setter
@Entity
@DiscriminatorValue("notify")
public class NotificationCommand extends UserCommand {

    /** This is the commandUuid the notification refers to */
    @NotNull
    @Column(name = "notificationUuid", nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    private UUID notificationUuid;

    public NotificationCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                               long userId, @Nonnull String body, UUID notificationUuid) {
        super(uuid, remoteCreationTime, userId, body, null);
        this.notificationUuid = notificationUuid;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected NotificationCommand() {
        super();
    }

}
