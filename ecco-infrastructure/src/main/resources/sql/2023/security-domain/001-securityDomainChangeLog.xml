<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
        logicalFilePath="2023/security-domain">

    <!-- HANDLES: (based on search for <createTable)
     - see main securityDomainChangeLog.xml for what tables are involved in the domain
    -->
    <changeSet id="DEV-2430-edit-tasks-manager-and-senior" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager, as per ROLE_ADMINEVIDENCE -->
            <column name="authority" value="ROLE_EDITTASK"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_EDITTASK"/>
        </insert>
    </changeSet>

    <!-- mssql doesn't like unique on nulls -->
    <!--
        alter table dbo.users add oAuthId VARCHAR(73);
        CREATE UNIQUE INDEX UQ__users__0383B99147E2B81D ON dbo.users(oAuthId) WHERE oAuthId IS NOT NULL;
    -->
    <changeSet id="DEV-2496-azure-365-security" author="adamjhamer">
        <addColumn tableName="users">
            <!-- tid:oid - colon is just helpful divide -->
            <column name="oAuthId" type="VARCHAR(73)">
                <constraints nullable="true" unique="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="DEV-2496-azure-365-security-aud" author="adamjhamer">
        <addColumn tableName="users_aud">
            <!-- tid:oid - colon is just helpful divide -->
            <column name="oAuthId" type="VARCHAR(73)"/>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2545-new-referral-permission" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="2"/> <!-- staff -->
            <column name="authority" value="ROLE_NEWREFERRAL"/>
        </insert>
    </changeSet>
    <changeSet id="DEV-2545-new-referral-permission-more" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager -->
            <column name="authority" value="ROLE_NEWREFERRAL"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_NEWREFERRAL"/>
        </insert>
    </changeSet>

    <!-- security so that we can distinguish the menu item for them -->
    <changeSet id="DEV-838-securityAccess" author="adamjhamer">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="32"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="security"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="32"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="32"/>
            <column name="authority" value="ROLE_STAFF"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="32"/>
            <column name="authority" value="ROLE_SECURITY"/>
        </insert>
    </changeSet>

    <!-- it makes sense to restrict all roles to services/projects assigned (eg reports, 'senior manager' etc) -->
    <!-- so even useradmin should be restricted - but that means we need a permission to allow all services/projects -->
    <!-- so we re-use the 'service access' title here -->
    <!-- NB we did think about re-using 'service admin' 5eff0809, but the aaa approach seems better -->
    <changeSet id="DEV-2585-aaa" author="adamjhamer">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="33"/>
            <column name="version" valueNumeric="1"/>
            <column name="group_name" value="all service access"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="33"/>
            <column name="authority" value="ROLE_AAA"/>
        </insert>
    </changeSet>

    <!-- create 'adminincidents' group with permissions so we can directly tick/allow access to all incidents -->
    <!-- this is a completely new security group, so its OK to add it all here and we may well want it in tests -->
    <!-- see also DEV-2628-incidents-permission-managers which needs to be elsewhere -->
    <!-- currently this group can't operate independently (the api's still expects ROLE_STAFF?) -->
    <changeSet id="DEV-2628-incidents-permission" author="adamjhamer">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="34"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_name" value="incidents"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="34"/>
            <column name="authority" value="ROLE_ADMININCIDENT"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="34"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
    </changeSet>
    <!-- confirm 'ROLE_ADMININCIDENT' is 'all incidents access' -->
    <!-- and add ROLE_INCIDENTS to be able to see the menu/data -->
    <changeSet id="DEV-2628-incidents-permission-view" author="adamjhamer">
        <update tableName="sec_groups">
            <column name="group_name" value="all incidents access"/>
            <where>id=34</where>
        </update>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="34"/>
            <column name="authority" value="ROLE_INCIDENTS"/>
        </insert>
    </changeSet>

    <!-- create 'adminrepairs' group with permissions so we can directly tick/allow access to all repairs -->
    <!-- this is a completely new security group, so its OK to add it all here and we may well want it in tests -->
    <!-- see also DEV-2658-repairs-permission-managers which needs to be elsewhere -->
    <!-- currently this group can't operate independently (the api's still expects ROLE_STAFF?) -->
    <changeSet id="DEV-2658-repairs-permission" author="adamjhamer">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="35"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_name" value="repairs"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="35"/>
            <column name="authority" value="ROLE_ADMINREPAIR"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="35"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
    </changeSet>
    <!-- confirm 'ROLE_ADMINREPAIR' is 'all repairs access' -->
    <!-- and add ROLE_REPAIRS to be able to see the menu/data -->
    <changeSet id="DEV-2658-repairs-permission-view" author="adamjhamer">
        <update tableName="sec_groups">
            <column name="group_name" value="all repairs access"/>
            <where>id=35</where>
        </update>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="35"/>
            <column name="authority" value="ROLE_REPAIRS"/>
        </insert>
    </changeSet>

    <!-- for incidents - managers see their own incidents / senior managers see all -->
    <!-- and we don't need a separate permission which says 'see my incidents' -->
    <!-- for repairs - managers + senior managers see all -->
    <!-- but we do need a separate permissions which says 'see my repairs' -->
    <!-- as well as the 'see all repairs' which may be useful -->
    <changeSet id="DEV-2658-repairs-mine-permission" author="adamjhamer">
        <insert tableName="sec_groups">
            <!-- groups don't get changed really, so just continue with the next id -->
            <column name="id" valueNumeric="36"/>
            <column name="version" valueNumeric="0"/>
            <column name="group_name" value="repairs"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="36"/>
            <column name="authority" value="ROLE_REPAIRS"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="36"/>
            <column name="authority" value="ROLE_USER"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2712-edit-tasks-due" author="adamjhamer" context="!test-data">
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="1"/> <!-- manager, as per ROLE_ADMINEVIDENCE -->
            <column name="authority" value="ROLE_EDITTASK_DUE"/>
        </insert>
        <insert tableName="group_authorities">
            <column name="group_id" valueNumeric="11"/> <!-- senior manager -->
            <column name="authority" value="ROLE_EDITTASK_DUE"/>
        </insert>
    </changeSet>

    <!-- TODO com.ecco.dom.Project to ProjectAclId -->

</databaseChangeLog>