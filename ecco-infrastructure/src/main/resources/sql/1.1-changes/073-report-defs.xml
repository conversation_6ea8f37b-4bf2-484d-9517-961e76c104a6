<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <!-- CLEAR reports since we changed the byServiceId -->
    <changeSet id="ECCO-1321-CLEAR" author="adamjhamer">
        <delete tableName="reportdefinitions">
            <where>uuid not in ('403252ba-0001-babe-babe-dada7ee1600d')</where>
        </delete>
    </changeSet>

    <!-- cannot use valueClobFile with classpath until https://liquibase.jira.com/browse/CORE-1519 is fixed -->
    <changeSet id="ECCO-1321-std-report-defs-1_2" author="nealeu">
        <!-- from reports-definitions.txt - was chart11 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00100000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="name" value="referrals by service, then by project, then breakdown"/>
            <column name="body" >
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart2 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00200000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="2"/>
            <column name="name" value="referrals by 'status now', then breakdown with goals outstanding/achieved"/>
            <column name="body" >
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByStatus&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by status&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart3 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00300000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="3"/>
            <column name="name" value="referrals by age at referral, then breakdown"/>
            <column name="body" >
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByAgeAtDateOfReferral&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by age categories&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart1 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00400000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="4"/>
            <column name="name" value="goals outstanding/achieved by worker, then breakdown"/>
            <column name="body" >
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;goals outstanding/achieved by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;outstanding&quot;,
                            &quot;valuePath&quot;: &quot;totalOutstandingSmartSteps&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        },
                        {
                            &quot;label&quot;: &quot;achieved&quot;,
                            &quot;valuePath&quot;: &quot;totalAchievedSmartSteps&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart4 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00500000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="5"/>
            <column name="name" value="goals outstanding by worker, then by service, then by 'status now', then breakdown"/>
            <column name="body" >
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;goals outstanding by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;outstanding&quot;,
                            &quot;valuePath&quot;: &quot;totalOutstandingSmartSteps&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByStatus&quot; }
                },
                {
                    &quot;description&quot;: &quot;by status&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <!-- cannot use valueClobFile with classpath until https://liquibase.jira.com/browse/CORE-1519 is fixed -->
    <changeSet id="ECCO-1321-std-report-defs-2_2" author="adamjhamer">
        <!-- from reports-definitions.txt - was chart5 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00600000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="6"/>
            <column name="name" value="goals outstanding/achieved by worker breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;SmartStepCountsAnalysis&quot;,
                        &quot;columns&quot;: [&quot;key&quot;, &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart12a from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00700000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="7"/>
            <column name="name" value="referrals with support work by service, then by support work, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter showing time spent and number of entries&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByAuthor&quot; }
                },
                {
                    &quot;description&quot;: &quot;inner ring: number of entries, outer ring: 'time (mins)'&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;mins spent&quot;,
                            &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        },
                        {
                            &quot;label&quot;: &quot;number of entries&quot;,
                            &quot;valuePath&quot;: &quot;totalVisits&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- was chart12b from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00800000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="8"/>
            <column name="name" value="referrals with support work by service, then by project, then by support work, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter showing time spent and number of entries&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByAuthor&quot; }
                },
                {
                    &quot;description&quot;: &quot;inner ring: number of entries, outer ring: 'time (mins)'&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;mins spent&quot;,
                            &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        },
                        {
                            &quot;label&quot;: &quot;number of entries&quot;,
                            &quot;valuePath&quot;: &quot;totalVisits&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart6 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00900000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="9"/>
            <column name="name" value="support work by worker, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter showing time spent and number of entries&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByAuthor&quot; }
                },
                {
                    &quot;description&quot;: &quot;inner ring: number of entries, outer ring: 'time (mins)'&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;mins spent&quot;,
                            &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        },
                        {
                            &quot;label&quot;: &quot;number of entries&quot;,
                            &quot;valuePath&quot;: &quot;totalVisits&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart13a from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="10"/>
            <column name="name" value="support work entries by service, then breakdown of support work"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workFromReferrals&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of support work&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;Work&quot;,
                        &quot;columns&quot;: [&quot;r-id&quot;, &quot;client&quot;, &quot;created&quot;, &quot;worker&quot;, &quot;work date&quot;, &quot;type&quot;, &quot;time (mins)&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart13b from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01100000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="11"/>
            <column name="name" value="support work entries by service, then by project, then by worker, then breakdown of support work"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workFromReferrals&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of support work&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;Work&quot;,
                        &quot;columns&quot;: [&quot;r-id&quot;, &quot;client&quot;, &quot;created&quot;, &quot;worker&quot;, &quot;work date&quot;, &quot;type&quot;, &quot;time (mins)&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart14a from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01200000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="12"/>
            <column name="name" value="support work minutes by service, then breakdown of support work"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workFromReferrals&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of support work&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;Work&quot;,
                        &quot;columns&quot;: [&quot;r-id&quot;, &quot;client&quot;, &quot;created&quot;, &quot;worker&quot;, &quot;work date&quot;, &quot;type&quot;, &quot;time (mins)&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart14b from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01300000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="13"/>
            <column name="name" value="support work minutes by service, then by project, then by worker, then breakdown of support work"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;by worker&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workFromReferrals&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of support work&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;Work&quot;,
                        &quot;columns&quot;: [&quot;r-id&quot;, &quot;client&quot;, &quot;created&quot;, &quot;worker&quot;, &quot;work date&quot;, &quot;type&quot;, &quot;time (mins)&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart15a from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01400000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="14"/>
            <column name="name" value="support work type by service, then breakdown of support work"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workFromReferrals_JOIN_workByCommentType&quot; }
                },
                {
                    &quot;description&quot;: &quot;by type&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of support work&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;Work&quot;,
                        &quot;columns&quot;: [&quot;r-id&quot;, &quot;client&quot;, &quot;created&quot;, &quot;worker&quot;, &quot;task&quot;, &quot;work date&quot;, &quot;type&quot;, &quot;time (mins)&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart7 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01500000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="15"/>
            <column name="name" value="activity demand by project, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on all referrals, showing the best days to schedule activities given the days clients are attending and wanting those activities&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.serviceId&quot;,
                &quot;serviceId&quot;: null,
                &quot;referralStatus&quot;: &quot;allNoDates&quot;,
                &quot;fetchRelatedEntities&quot;: [&quot;activityInterest&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;activitiesByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;activity demand by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;selectionAnalyser&quot;: &quot;referrals&quot;,
                    &quot;selectionKey&quot;: &quot;project 4&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;mon&quot;,
                            &quot;valuePath&quot;: &quot;monday&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        },
                        {
                            &quot;label&quot;: &quot;tues&quot;,
                            &quot;valuePath&quot;: &quot;tuesday&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        },
                        {
                            &quot;label&quot;: &quot;wed&quot;,
                            &quot;valuePath&quot;: &quot;wednesday&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        },
                        {
                            &quot;label&quot;: &quot;thurs&quot;,
                            &quot;valuePath&quot;: &quot;thursday&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        },
                        {
                            &quot;label&quot;: &quot;fri&quot;,
                            &quot;valuePath&quot;: &quot;friday&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        },
                        {
                            &quot;label&quot;: &quot;sat&quot;,
                            &quot;valuePath&quot;: &quot;saturday&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        },
                        {
                            &quot;label&quot;: &quot;sun&quot;,
                            &quot;valuePath&quot;: &quot;sunday&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;activitiesByActivityInterest&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of activity demand across days&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;DaysOfWeekAnalysis&quot;,
                        &quot;columns&quot;: [&quot;activity&quot;, &quot;mon&quot;, &quot;tue&quot;, &quot;wed&quot;, &quot;thur&quot;, &quot;fri&quot;, &quot;sat&quot;, &quot;sun&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- this already exists, but hasn't got an order - so apply one -->
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="16"/>
            <where>uuid='403252ba-0001-babe-babe-dada7ee1600d'</where>
        </update>
        <!-- from reports-definitions.txt - was chart9 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01700000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="17"/>
            <column name="name" value="table of all referrals on the LD Day Centre service"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.serviceId&quot;,
                &quot;serviceId&quot;: 105,
                &quot;referralStatus&quot;: &quot;allNoDates&quot;,
                &quot;fetchRelatedEntities&quot;: [&quot;activityInterest&quot;, &quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;all referrals on service&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;address&quot;, &quot;town&quot;, &quot;postcode&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was chart10 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01800000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="18"/>
            <column name="name" value="referrals with visits by service, by project, breakdown with type 'one-to-one'"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByClient&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of visits&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;VisitsAnalysis&quot;,
                        &quot;columns&quot;: [&quot;client&quot;, &quot;time spent&quot;, &quot;latest work&quot;,
                            {&quot;title&quot;: &quot;total visits&quot;,       &quot;representation&quot;: &quot;visits&quot;, &quot;path&quot;:[&quot;totalVisits&quot;]},
                            {&quot;title&quot;:&quot;visits : -&quot;,          &quot;representation&quot;: &quot;visits&quot;, &quot;path&quot;:[&quot;countByCommentType&quot;,&quot;-&quot;]},
                            {&quot;title&quot;:&quot;visits : one-to-one&quot;, &quot;representation&quot;: &quot;visits&quot;, &quot;path&quot;:[&quot;countByCommentType&quot;,&quot;one-to-one&quot;]},
                            &quot;breakdown&quot;
                        ]
                    }
                }
            ]
        }
            </column>
        </insert>
        <!-- from reports-definitions.txt - was badge16 from ChartDefAjaxRepository -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="01900000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="19"/>
            <column name="name" value="example badge report"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;work in progress&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; },
                    &quot;selectionAnalyser&quot;: &quot;single&quot;
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;selectionAnalyser&quot;: &quot;single&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;Time spent with clients in period&quot;,
                    &quot;stageType&quot;: &quot;BADGE&quot;,
                    &quot;badgeRepresentation&quot;: {
                        &quot;badgeIconCssClasses&quot;: &quot;fa fa-fire&quot;,
                        &quot;recordRepresentationClassName&quot;: &quot;VisitsAnalysis&quot;,
                        &quot;mainIndicatorValue&quot;: &quot;time spent&quot;
                    }
                },
                {
                    &quot;description&quot;: &quot;average time per visit&quot;,
                    &quot;stageType&quot;: &quot;BADGE&quot;,
                    &quot;badgeRepresentation&quot;: {
                        &quot;badgeIconCssClasses&quot;: &quot;fa fa-file&quot;,
                        &quot;recordRepresentationClassName&quot;: &quot;VisitsAnalysis&quot;,
                        &quot;mainIndicatorValue&quot;: &quot;average time spent&quot;
                    }
                },
                {
                    &quot;description&quot;: &quot;latest work&quot;,
                    &quot;stageType&quot;: &quot;BADGE&quot;,
                    &quot;badgeRepresentation&quot;: {
                        &quot;badgeIconCssClasses&quot;: &quot;fa fa-road&quot;,
                        &quot;recordRepresentationClassName&quot;: &quot;VisitsAnalysis&quot;,
                        &quot;mainIndicatorValue&quot;: &quot;latest work&quot;
                    }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <!-- update the report already configured in 072 to use 'byReferralStatus' (v2) and replace sId=105 with byservice + use activeFor=project -->
    <changeSet id="ECCO-1428-update-repDef-activityDemand_3" author="nealeu">
        <update tableName="reportdefinitions">
            <column name="body" >{
    &quot;description&quot;: &quot;on all referrals, showing the best days to schedule activities given the days and services clients are attending and wanting those activities&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.serviceId&quot;,
        &quot;referralStatus&quot;: &quot;allNoDates&quot;,
        &quot;fetchRelatedEntities&quot;: [&quot;activityInterest&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;activeFor&quot;: &quot;project&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;referrals by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;activeFor&quot;: &quot;project&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;activitiesByActivityInterest&quot; }
        },
        {
            &quot;description&quot;: &quot;breakdown of activity demand across days&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;DaysOfWeekAnalysis&quot;,
                &quot;columns&quot;: [&quot;activity&quot;, &quot;mon&quot;, &quot;tue&quot;, &quot;wed&quot;, &quot;thur&quot;, &quot;fri&quot;, &quot;sat&quot;, &quot;sun&quot;]
                }
        }
    ]
}
            </column>
            <where>uuid='403252ba-0001-babe-babe-dada7ee1600d'</where>
        </update>
    </changeSet>


    <!-- hide chart7 (CDO), chart8 (CDO), chart9 (CDO), chart10 (one-to-one), badge16 (wip) -->
    <changeSet id="ECCO-1321-std-report-hide_2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="hidden" valueDate="2015-05-01T07:00:00"/>
            <where>uuid in ('01500000-0000-babe-babe-dadafee1600d', '403252ba-0001-babe-babe-dada7ee1600d', '01700000-0000-babe-babe-dadafee1600d', '01800000-0000-babe-babe-dadafee1600d', '01900000-0000-babe-babe-dadafee1600d')</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1321-repDef-throughput" author="adamjhamer">
        <!-- from reports-definitions.txt -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="02000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="20"/>
            <column name="name" value="referrals by service, then by project, then by 'status now', then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByStatus&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals showing 'status now'&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;status now&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <!-- referrals by region, then by service, then by project, then by 'status now', then breakdown -->
    <changeSet id="ECCO-1321-repDef-throughput-withRegion-withCanSkip" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals this quarter&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: []
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByRegion&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by region&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;canSkip&quot;: &quot;true&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByService&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByProject&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByStatus&quot;
            }
        },
        {
            &quot;description&quot;: &quot;referrals showing 'status now'&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;canSkip&quot;: &quot;true&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;status now&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;BAR&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                    &quot;rid&quot;,
                    &quot;cid&quot;,
                    &quot;client&quot;,
                    &quot;region&quot;,
                    &quot;service&quot;,
                    &quot;project&quot;,
                    &quot;from&quot;,
                    &quot;received&quot;,
                    &quot;status now&quot;,
                    &quot;worker&quot;
                ]
            }
        }
    ]
}
            </column>
            <where>uuid='02000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals by region, then by service, then by project, then by 'status now', then breakdown -->
    <changeSet id="ECCO-1321-repDef-rename-for-region" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="referrals by region, then by service, then by project, then by 'status now', then breakdown"/>
            <where>uuid='02000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- reinstate the original "referrals by service, then by project, then by 'status now', then breakdown" -->
    <!-- this also reuses an ORDERBY 20 so they remain together -->
    <changeSet id="ECCO-1321-repDef-throughput-original" author="adamjhamer">
        <!-- from reports-definitions.txt -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="02b00000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="20"/>
            <column name="name" value="referrals by service, then by project, then by 'status now', then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByStatus&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals showing 'status now'&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;canSkip&quot;: &quot;true&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;status now&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <changeSet id="ECCO-1321-repDef-lengthOnService" author="adamjhamer">
        <!-- from reports-definitions.txt -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="02100000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="21"/>
            <column name="name" value="referrals (closed) by service, then by project, then by length on service, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals closed this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.exitedDate&quot;,
                &quot;referralStatus&quot;: &quot;closed&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByLengthOnService&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by length on service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <changeSet id="ECCO-1321-repDef-mailMerge" author="adamjhamer">
        <!-- from reports-definitions.txt -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="02200000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="22"/>
            <column name="name" value="referrals (live) breakdown for mail merge"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on all referrals, export as CSV for mail merge&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
                &quot;selectionPropertyPath&quot;: &quot;&quot;,
                &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;title&quot;, &quot;first name&quot;, &quot;last name&quot;, &quot;address&quot;, &quot;town&quot;, &quot;postcode&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <!-- referrals by service, then by project, then breakdown - with more columns -->
    <changeSet id="ECCO-1321-add-more-cols" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body" >
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;service&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;decided&quot;, &quot;start&quot;, &quot;exited&quot;, &quot;rejected&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
            <where>uuid='00100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals by service, then by project, then by worker, then breakdown - name corrected -->
    <changeSet id="ECCO-1321-correct-name" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="referrals by service, then by project, then by worker, then breakdown"/>
          <where>uuid='00100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1321-update-age-to-demographics3" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="23"/>
            <column name="name" value="referrals by demographic - age at referral, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByAgeAtDateOfReferral&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by age&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;age at ref.&quot;, &quot;service&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }

            </column>
            <where>uuid='00300000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="ECCO-1321-std-report-defs-3" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00240000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="24"/>
            <column name="name" value="referrals by service, then by project, then by demographic - ethnicity, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByEthnicity&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by ethnicity&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00250000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="25"/>
            <column name="name" value="referrals by service, then by project, then by demographic - gender, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByGender&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by gender&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00260000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="26"/>
            <column name="name" value="referrals by service, then by project, then by demographic - religion, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByReligion&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by religions&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00270000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="27"/>
            <column name="name" value="referrals by service, then by project, then by demographic - sexual orientation then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsBySexualOrientation&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by sexual orientation&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <!-- TODO correct spelling of referralCountsByFirstLangauge? -->
        <insert tableName="reportdefinitions">
            <column name="uuid" value="00280000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="28"/>
            <column name="name" value="referrals by service, then by project, then by demographic - first language then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByFirstLangauge&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by first language&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <insert tableName="reportdefinitions">
            <column name="uuid" value="0028b000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="28"/>
            <column name="name" value="referrals by service, then by project, then by demographic - disability, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByDisability&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by first language&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <changeSet id="ECCO-1321-repDef-closed-rejected" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="02900000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="29"/>
            <column name="name" value="referrals (closed) by service, then by project, then by closed reason, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals closed this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.exitedDate&quot;,
                &quot;referralStatus&quot;: &quot;closed&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByExitReason&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals showing closed reasons&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;reason&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="30"/>
            <column name="name" value="referrals (rejected) by service, then by project, then by signposted reason, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals closed this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.decision&quot;,
                &quot;referralStatus&quot;: &quot;rejected&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsBySignpostReason&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals showing signposted reasons&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;reason&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;BAR&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <!-- remove repDef-closed-smartsteps if existed -->
    <changeSet id="ECCO-1321-remove-repDef-closed-smartsteps" author="adamjhamer">
        <delete tableName="reportdefinitions">
            <where>uuid in ('3100000-0000-babe-babe-dadafee1600d', '03100000-0000-babe-babe-dadafee1600d')</where>
        </delete>
    </changeSet>

    <!-- reinstate missing report -->
    <changeSet id="ECCO-1321-repDef-closed-smartsteps_3" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03100000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="31"/>
            <column name="name" value="referrals (closed) by service, then by project, then by support work, then breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals closed this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.exitedDate&quot;,
                &quot;referralStatus&quot;: &quot;closed&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefFromReferrals&quot; }
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefCountsAnalyser&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of work by smart steps&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ActionDefCount&quot;,
                        &quot;columns&quot;: [&quot;key&quot;, &quot;smart step&quot;, &quot;totalAchievedSmartSteps&quot;, &quot;totalOutstandingSmartSteps&quot;]
                        }
                },
                {
                    &quot;description&quot;: &quot;breakdown of work by smart steps&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ActionDef&quot;,
                        &quot;columns&quot;: [&quot;a-id&quot;, &quot;a-name&quot;, &quot;a-status&quot;, &quot;r-id&quot;, &quot;client&quot;, &quot;work date&quot;, &quot;time (mins)&quot;, &quot;type&quot;]
                        }
                }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <changeSet id="ECCO-1321-name-update" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="goals outstanding/achieved by worker, then referral breakdown"/>
            <where>uuid='00400000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <changeSet id="ECCO-1321-repDef-update-for-smart-step-breakdown" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="goals outstanding/achieved by worker, then breakdown of goals"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepFromReferrals&quot; }
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepWithRefCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;SmartStepWithRefCounts&quot;,
                        &quot;columns&quot;: [&quot;key&quot;, &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                },
                {
                    &quot;description&quot;: &quot;breakdown of smart steps&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;SmartStepWithRef&quot;,
                        &quot;columns&quot;: [&quot;a-id&quot;, &quot;a-name&quot;, &quot;a-status&quot;, &quot;r-id&quot;, &quot;client&quot;, &quot;work date&quot;, &quot;time (mins)&quot;, &quot;type&quot;]
                        }
                }
            ]
        }
            </column>
            <where>uuid='00600000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals by 'status now', then breakdown with goals outstanding/achieved -->
    <changeSet id="ECCO-1321-repDef-update-for-service-col-canSkip" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByStatus&quot; }
                },
                {
                    &quot;description&quot;: &quot;referrals by status&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;canSkip&quot;: &quot;true&quot;,
                    &quot;seriesDefs&quot;: [
                        {
                            &quot;label&quot;: &quot;count&quot;,
                            &quot;valuePath&quot;: &quot;count&quot;,
                            &quot;renderMode&quot;: &quot;PIE&quot;
                        }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;service&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                            &quot;achieved&quot;, &quot;outstanding&quot;]
                        }
                }
            ]
        }
            </column>
            <where>uuid='00200000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- update breakdown to get lengh of service (days) and service in it -->
    <changeSet id="ECCO-1321-repDef-update-for-lengthOnService-canSkip" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals closed this quarter&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.exitedDate&quot;,
        &quot;referralStatus&quot;: &quot;closed&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: []
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByService&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByProject&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByLengthOnService&quot;
            }
        },
        {
            &quot;description&quot;: &quot;referrals by length on service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;canSkip&quot;: &quot;true&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;length on service&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;BAR&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                    &quot;rid&quot;,
                    &quot;cid&quot;,
                    &quot;client&quot;,
                    &quot;service&quot;,
                    &quot;project&quot;,
                    &quot;from&quot;,
                    &quot;start&quot;,
                    &quot;exited&quot;,
                    &quot;length (days)&quot;,
                    &quot;status now&quot;,
                    &quot;worker&quot;
                ]
            }
        }
    ]
}
            </column>
            <where>uuid='02100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- demographics all -->
    <changeSet id="ECCO-1321-repDef-demog_all" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03200000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="32"/>
            <column name="name" value="referrals by service, then by project, then all demographic breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals this quarter&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;gender&quot;, &quot;ethnicity&quot;, &quot;age at ref.&quot;, &quot;religion&quot;, &quot;disability&quot;, &quot;first lang.&quot;, &quot;sex. orient.&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                }
        }
    ]
}
            </column>
        </insert>
    </changeSet>

    <!-- update 'final outcomes' report - to include exit reasons and region and better names for totalOutstanding and perc complete -->
    <changeSet id="ECCO-1321-repDef-closed-smartsteps_8" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals closed this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.exitedDate&quot;,
                &quot;referralStatus&quot;: &quot;closed&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefFromReferrals&quot; }
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefCountsAnalyser&quot; }
                },
                {
                    &quot;description&quot;: &quot;summary of work by smart steps&quot;,
                    &quot;canSkip&quot;: &quot;true&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ActionDefCount&quot;,
                        &quot;columns&quot;: [&quot;key&quot;, &quot;smart step&quot;, &quot;achieved&quot;, &quot;outstanding&quot;, &quot;success %&quot;]
                        }
                },
                {
                    &quot;description&quot;: &quot;breakdown of work by smart steps&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ActionDef&quot;,
                        &quot;columns&quot;: [&quot;a-id&quot;, &quot;a-name&quot;, &quot;a-status&quot;, &quot;r-id&quot;, &quot;service&quot;, &quot;project&quot;, &quot;region&quot;, &quot;worker&quot;, &quot;exit reason&quot;, &quot;client&quot;, &quot;work date&quot;, &quot;time (mins)&quot;, &quot;type&quot;]
                        }
                }
            ]
        }
            </column>
            <where>uuid='03100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- demographics all more columns -->
    <changeSet id="ECCO-1321-repDef-demog_all-more-cols" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals this quarter&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;from&quot;, &quot;received&quot;, &quot;decided&quot;, &quot;start&quot;, &quot;exited&quot;, &quot;rejected&quot;, &quot;gender&quot;, &quot;ethnicity&quot;, &quot;age at ref.&quot;, &quot;religion&quot;, &quot;disability&quot;, &quot;first lang.&quot;, &quot;sex. orient.&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                }
        }
    ]
}
            </column>
            <where>uuid='03200000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals (live at end of period) by service, then by project, then breakdown - as 00100000 but live only for caseload -->
    <!-- NB the 'name' of this report is changed later - its not demographics -->
    <changeSet id="ECCO-1321-repDef-caseload" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03300000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="32"/>
            <column name="name" value="referrals (live at end of period) by service, then by project, then all demographic breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;service&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;decided&quot;, &quot;start&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }

            </column>
        </insert>
    </changeSet>


    <!-- all smart steps -->
    <changeSet id="ECCO-1321-repDef-smartsteps-all" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03400000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="34"/>
            <column name="name" value="referrals with support work by service, then by project, then by support work, then smart step breakdown"/>
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this period showing number of entries&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefFromReferrals&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefCountsAnalyser&quot; }
        },
        {
            &quot;description&quot;: &quot;summary of work by smart steps&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ActionDefCount&quot;,
                &quot;columns&quot;: [&quot;key&quot;, &quot;smart step&quot;, &quot;totalAchievedSmartSteps&quot;, &quot;totalOutstandingSmartSteps&quot;]
                }
        },
        {
            &quot;description&quot;: &quot;breakdown of work by smart steps&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ActionDef&quot;,
                &quot;columns&quot;: [&quot;a-id&quot;, &quot;a-name&quot;, &quot;a-status&quot;, &quot;r-id&quot;, &quot;client&quot;, &quot;work date&quot;, &quot;time (mins)&quot;, &quot;type&quot;]
                }
        }
            ]
        }
            </column>
        </insert>
    </changeSet>

    <!-- change name, root entity is support: referrals with support work by service, then by project, then by support work, then smart step breakdown -->
    <changeSet id="ECCO-1321-repDef-smartsteps-all_nameChange" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="support work by service, then by project, then by support work, then smart step breakdown"/>
            <where>uuid='03400000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- support work by service, then by project, then by support work, then smart step breakdown -->
    <changeSet id="ECCO-1321-repDef-smartsteps-all_canSkipTable" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on support work this period showing number of entries&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
{
    &quot;description&quot;: &quot;-not visible-&quot;,
    &quot;stageType&quot;: &quot;ANALYSER&quot;,
    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefFromReferrals&quot; }
},
{
    &quot;description&quot;: &quot;-not visible-&quot;,
    &quot;stageType&quot;: &quot;ANALYSER&quot;,
    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefCountsAnalyser&quot; }
},
{
    &quot;description&quot;: &quot;summary of work by smart steps&quot;,
    &quot;stageType&quot;: &quot;TABLE&quot;,
    &quot;canSkip&quot;: &quot;true&quot;,
    &quot;tableRepresentation&quot;: {
        &quot;className&quot;: &quot;ActionDefCount&quot;,
        &quot;columns&quot;: [&quot;a-id&quot;, &quot;smart step&quot;, &quot;achieved&quot;, &quot;outstanding&quot;, &quot;success %&quot;]
        }
},
{
    &quot;description&quot;: &quot;breakdown of work by smart steps&quot;,
    &quot;stageType&quot;: &quot;TABLE&quot;,
    &quot;tableRepresentation&quot;: {
        &quot;className&quot;: &quot;ActionDef&quot;,
        &quot;columns&quot;: [&quot;a-id&quot;, &quot;a-name&quot;, &quot;a-status&quot;, &quot;r-id&quot;, &quot;c-id&quot;, &quot;client&quot;, &quot;status now&quot;, &quot;work date&quot;, &quot;time (mins)&quot;, &quot;type&quot;]
        }
}
    ]
}
            </column>
            <where>uuid='03400000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- support work by service, then by project, then by support work, then smart step breakdown -->
    <changeSet id="z1634-repDef-smartsteps-all_outcomeName" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
&quot;description&quot;: &quot;on support work this period showing number of entries&quot;,
&quot;selectionCriteria&quot;: {
&quot;selectionRootEntity&quot;: &quot;Referral&quot;,
&quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
&quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
&quot;relativeStartIndex&quot;: 0,
&quot;relativeEndIndex&quot;: 1,
&quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
},
&quot;stages&quot;: [
{
&quot;description&quot;: &quot;-not visible-&quot;,
&quot;stageType&quot;: &quot;ANALYSER&quot;,
&quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
},
{
&quot;description&quot;: &quot;by service&quot;,
&quot;stageType&quot;: &quot;CHART&quot;,
&quot;seriesDefs&quot;: [
{ &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
]
},
{
&quot;description&quot;: &quot;-not visible-&quot;,
&quot;stageType&quot;: &quot;ANALYSER&quot;,
&quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
},
{
&quot;description&quot;: &quot;by project&quot;,
&quot;stageType&quot;: &quot;CHART&quot;,
&quot;seriesDefs&quot;: [
{ &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
]
},
{
&quot;description&quot;: &quot;-not visible-&quot;,
&quot;stageType&quot;: &quot;ANALYSER&quot;,
&quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefFromReferrals&quot; }
},
{
&quot;description&quot;: &quot;-not visible-&quot;,
&quot;stageType&quot;: &quot;ANALYSER&quot;,
&quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;actionDefCountsAnalyser&quot; }
},
{
&quot;description&quot;: &quot;summary of work by smart steps&quot;,
&quot;stageType&quot;: &quot;TABLE&quot;,
&quot;canSkip&quot;: &quot;true&quot;,
&quot;tableRepresentation&quot;: {
&quot;className&quot;: &quot;ActionDefCount&quot;,
&quot;columns&quot;: [&quot;a-id&quot;, &quot;outcome&quot;, &quot;smart step&quot;, &quot;achieved&quot;, &quot;outstanding&quot;, &quot;success %&quot;]
}
},
{
&quot;description&quot;: &quot;breakdown of work by smart steps&quot;,
&quot;stageType&quot;: &quot;TABLE&quot;,
&quot;tableRepresentation&quot;: {
&quot;className&quot;: &quot;ActionDef&quot;,
&quot;columns&quot;: [&quot;a-id&quot;, &quot;a-name&quot;, &quot;a-status&quot;, &quot;r-id&quot;, &quot;c-id&quot;, &quot;client&quot;, &quot;status now&quot;, &quot;work date&quot;, &quot;time (mins)&quot;, &quot;type&quot;]
}
}
]
}
            </column>
            <where>uuid='03400000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- caseload name update -->
    <changeSet id="ECCO-1321-repDef-caseload-not-demographics3" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="referrals (live at end of period) by service, then by project, then breakdown"/>
            <where>uuid='03300000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>
    <!-- rename to reflect the change to byReferralStatus (which uses no dates) below -->
    <changeSet id="ECCO-1628-repDef-03300000-removeDates-rename" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="referrals (live) by service, then by project, then breakdown"/>
            <where>uuid='03300000-0000-babe-babe-dadafee1600d' and name='referrals (live at end of period) by service, then by project, then breakdown'</where>
        </update>
    </changeSet>
    <!-- referrals (live) by service, then by project, then breakdown -->
    <!-- remove dates, although a live report is capable of having dates, its confusing -->
    <changeSet id="ECCO-1321-repDef-03300000-removeDates" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
        {
            &quot;description&quot;: &quot;on referrals currently live&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
                &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;count&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;breakdown of referrals&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;ReferralReportItem&quot;,
                        &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;service&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;decided&quot;, &quot;start&quot;, &quot;status now&quot;, &quot;worker&quot;]
                        }
                }
            ]
        }

            </column>
            <where>uuid='03300000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- referrals (live at end of period) by service, then by project, then all demographic breakdown -->
    <changeSet id="ECCO-1321-repDef-demog_all-current" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03500000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="35"/>
            <column name="name" value="referrals (live at end of period) by service, then by project, then all demographic breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals this quarter&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
        &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;from&quot;, &quot;received&quot;, &quot;decided&quot;, &quot;start&quot;, &quot;exited&quot;, &quot;rejected&quot;, &quot;gender&quot;, &quot;ethnicity&quot;, &quot;age at ref.&quot;, &quot;religion&quot;, &quot;disability&quot;, &quot;first lang.&quot;, &quot;sex. orient.&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;]
                }
        }
    ]
}
            </column>
        </insert>
    </changeSet>

    <!-- 'current' overdue referrals accepted -->
    <changeSet id="ECCO-1321-repDef-overduereferrals-current" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03600000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="36"/>
            <column name="name" value="referrals overdue (no service decision) by project, then by worker, then breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on all referrals not decided in 2 weeks&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;&quot;,
        &quot;referralStatus&quot;: &quot;ongoing&quot;,
        &quot;fetchRelatedEntities&quot;: []
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralsNotAcceptedWithin2Weeks&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
        },
        {
            &quot;description&quot;: &quot;by worker&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;decided&quot;, &quot;status now&quot;, &quot;worker&quot;]
                }
        }
    ]
}
            </column>
        </insert>
    </changeSet>

    <!-- rename to be support work first - the root entity is actually support work -->
    <changeSet id="ECCO-1321-repDef-rename-suppWork1" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="support work by service, then by support work, then referral breakdown"/>
            <where>uuid='00700000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- rename to be support work first - the root entity is actually support work -->
    <changeSet id="ECCO-1321-repDef-rename-suppWork2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="support work by service, then by project, then by support work, then referral breakdown"/>
            <where>uuid='00800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- 'current' overdue needs -->
    <changeSet id="ECCO-1321-repDef-overdueneeds-current" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03700000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="37"/>
            <column name="name" value="referrals with overdue needs (no work) by project, then by worker, then breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on all referrals without support work within 2 weeks&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;&quot;,
        &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
        &quot;fetchRelatedEntities&quot;: [
            &quot;supportWork&quot;
        ]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralsAcceptedButNoWorkWithin2Weeks&quot;
            }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;visitCountsByProject&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;totalVisits&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;visitCountsByWorker&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by worker&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;totalVisits&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                    &quot;rid&quot;,
                    &quot;cid&quot;,
                    &quot;client&quot;,
                    &quot;service&quot;,
                    &quot;project&quot;,
                    &quot;from&quot;,
                    &quot;received&quot;,
                    &quot;decided&quot;,
                    &quot;status now&quot;,
                    &quot;worker&quot;
                ]
            }
        }
    ]
}
            </column>
        </insert>
    </changeSet>


    <!-- 'current' overdue referrals accepted -->
    <changeSet id="ECCO-1321-repDef-overduerisk-current" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03800000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="38"/>
            <column name="name" value="referrals with overdue risk management (no work) by project, then by worker, then breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on all referrals without risk management work within 2 weeks&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;&quot;,
        &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
        &quot;fetchRelatedEntities&quot;: [
            &quot;riskWork&quot;
        ]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralsWithoutRiskWorkNotAcceptedWithin2Weeks&quot;
            }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
        },
        {
            &quot;description&quot;: &quot;by worker&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                    &quot;rid&quot;,
                    &quot;cid&quot;,
                    &quot;client&quot;,
                    &quot;service&quot;,
                    &quot;project&quot;,
                    &quot;from&quot;,
                    &quot;received&quot;,
                    &quot;decided&quot;,
                    &quot;status now&quot;,
                    &quot;worker&quot;
                ]
            }
        }
    ]
}
            </column>
        </insert>
    </changeSet>

    <!-- 'current' overdue needs wants analysers by referral not visits, since there aren't any -->
    <changeSet id="ECCO-1321-repDef-overdueneeds-current3" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on all referrals without support work within 2 weeks&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;&quot;,
        &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
        &quot;fetchRelatedEntities&quot;: [
            &quot;supportWork&quot;
        ]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralsWithoutWorkNotAcceptedWithin2Weeks&quot;
            }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
        },
        {
            &quot;description&quot;: &quot;by worker&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                    &quot;rid&quot;,
                    &quot;cid&quot;,
                    &quot;client&quot;,
                    &quot;service&quot;,
                    &quot;project&quot;,
                    &quot;from&quot;,
                    &quot;received&quot;,
                    &quot;decided&quot;,
                    &quot;status now&quot;,
                    &quot;worker&quot;
                ]
            }
        }
    ]
}
            </column>
            <where>uuid='03700000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- 'current' ongoing referrals unsigned data protection -->
    <changeSet id="ECCO-1321-repDef-unsignedreferrals-current" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="03900000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="39"/>
            <column name="name" value="referrals with unsigned data protection (where configured) by project, then by worker, then breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on all referrals ongoing (not rejected/closed)&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;&quot;,
        &quot;referralStatus&quot;: &quot;ongoing&quot;,
        &quot;fetchRelatedEntities&quot;: []
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralsNotSignedDataProtection&quot;
            }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
        },
        {
            &quot;description&quot;: &quot;by worker&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                    &quot;rid&quot;,
                    &quot;cid&quot;,
                    &quot;client&quot;,
                    &quot;service&quot;,
                    &quot;project&quot;,
                    &quot;from&quot;,
                    &quot;received&quot;,
                    &quot;decided&quot;,
                    &quot;status now&quot;,
                    &quot;worker&quot;
                ]
            }
        }
    ]
}
            </column>
        </insert>
    </changeSet>


    <!-- clarify its by assigned worker, not author of work -->
    <changeSet id="ECCO-1321-repDef-supportWork2" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="support work entries by service, then by project, then by assigned worker, then breakdown of support work"/>
            <where>uuid='01100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals (live) with work (support and risk) by service, then by project, then by worker, then by support/risk, then by breakdown -->
    <changeSet id="ECCO-1321-repDef-unsignedwork-current" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="40"/>
            <column name="name" value="referrals (live) with work (support and risk) by service, then by project, then by worker, then by support/risk, then by breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on all referrals live&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;&quot;,
        &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
        &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;, &quot;riskWork&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitAllCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitAllCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitAllCountsByAuthor&quot; }
        },
        {
            &quot;description&quot;: &quot;inner ring: number of entries, outer ring: 'time (mins)'&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;mins spent&quot;,
                    &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                },
                {
                    &quot;label&quot;: &quot;number of entries&quot;,
                    &quot;valuePath&quot;: &quot;totalVisits&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workFromReferrals&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workBySourceTask&quot; }
        },
        {
            &quot;description&quot;: &quot;by support/work&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workByRid&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workGroupToVisitGroup&quot; }
        },
        {
            &quot;description&quot;: &quot;breakdown of visits&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;VisitsAnalysis&quot;,
                &quot;columns&quot;: [
                    &quot;client&quot;,
                    &quot;time spent&quot;,
                    &quot;visits&quot;,
                    &quot;average time spent&quot;,
                    &quot;last signed work&quot;,
                    &quot;last unsigned work&quot;
                ]
            }
        }
    ]
}
            </column>
        </insert>
    </changeSet>

    <!-- clarify its a work count -->
    <changeSet id="ECCO-1321-repDef-workCount" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="referrals (live) with work count (support and risk) by service, then by project, then by worker, then by support/risk, then by breakdown"/>
            <where>uuid='04000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals (live) with work count (support and risk) by service, then by project, then by worker, then by support/risk, then by breakdown -->
    <changeSet id="ECCO-1321-repDef-workCount_breakdown_rid" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on all referrals live&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;&quot;,
        &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
        &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;, &quot;riskWork&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitAllCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitAllCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitAllCountsByAuthor&quot; }
        },
        {
            &quot;description&quot;: &quot;inner ring: number of entries, outer ring: 'time (mins)'&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;canSkip&quot;: &quot;true&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;mins spent&quot;,
                    &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                },
                {
                    &quot;label&quot;: &quot;number of entries&quot;,
                    &quot;valuePath&quot;: &quot;totalVisits&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workAllFromReferrals&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workBySourceTask&quot; }
        },
        {
            &quot;description&quot;: &quot;by support/work&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;canSkip&quot;: &quot;true&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workByRid&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workGroupToVisitGroup&quot; }
        },
        {
            &quot;description&quot;: &quot;breakdown of visits&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;VisitsAnalysis&quot;,
                &quot;columns&quot;: [
                    &quot;r-id&quot;,
                    &quot;c-id&quot;,
                    &quot;time spent&quot;,
                    &quot;visits&quot;,
                    &quot;average time spent&quot;,
                    &quot;last signed work&quot;,
                    &quot;last unsigned work&quot;
                ]
            }
        }
    ]
}
            </column>
            <where>uuid='04000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- rename to reflect the change to byReferralStatus (which uses no dates) below -->
    <changeSet id="ECCO-1628-repDef-03500000-removeDates-rename" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="referrals (live) by service, then by project, then all demographic breakdown"/>
            <where>uuid='03500000-0000-babe-babe-dadafee1600d' and name='referrals (live at end of period) by service, then by project, then all demographic breakdown'</where>
        </update>
    </changeSet>
    <!-- referrals (live at end of period) by service, then by project, then all demographic breakdown -->
    <changeSet id="ECCO-1321-repDef-demog-all-rename" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals currently live&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byReferralStatus&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
        &quot;referralStatus&quot;: &quot;liveAtEnd&quot;,
        &quot;fetchRelatedEntities&quot;: [&quot;client&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;service&quot;, &quot;project&quot;, &quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;from&quot;, &quot;received&quot;, &quot;decided&quot;, &quot;start&quot;, &quot;exited&quot;, &quot;rejected&quot;, &quot;gender&quot;, &quot;ethnicity&quot;, &quot;birthdate&quot;, &quot;age at ref.&quot;, &quot;religion&quot;, &quot;disability&quot;, &quot;first lang.&quot;, &quot;sex. orient.&quot;, &quot;phone&quot;, &quot;mobile&quot;, &quot;email&quot;, &quot;status now&quot;, &quot;worker&quot;]
                }
        }
    ]
}
            </column>
            <where>uuid='03500000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- goals outstanding by worker, then by service, then by 'status now', then breakdown -->
    <!-- correct the analyser which was referralCounts-->
    <changeSet id="ECCO-1321-repDef-00500000-revert" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;on referrals this quarter&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByWorker&quot; }
                },
                {
                &quot;description&quot;: &quot;goals outstanding by worker&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;outstanding&quot;,
                &quot;valuePath&quot;: &quot;totalOutstandingSmartSteps&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByService&quot; }
                },
                {
                &quot;description&quot;: &quot;by service&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByStatus&quot; }
                },
                {
                &quot;description&quot;: &quot;by status&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                &quot;achieved&quot;, &quot;outstanding&quot;]
                }
                }
                ]
                }
            </column>
            <where>uuid='00500000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals with visits by service, by project, breakdown with type 'one-to-one' -->
    <!-- rename -->
    <changeSet id="ECCO-1321-repDef-01800000-rename" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="support work entries by service, then by project, then by assigned worker, then breakdown with type 'one-to-one'"/>
            <where>uuid='01800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- support work entries by service, then by project, then by assigned worker, then breakdown with type 'one-to-one' -->
    <!-- was counting random things - so count the visits correctly -->
    <changeSet id="ECCO-1321-repDef-01800000-correctedCount" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
        {
            &quot;description&quot;: &quot;on support work this period&quot;,
            &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
            },
            &quot;stages&quot;: [
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; }
                },
                {
                    &quot;description&quot;: &quot;by service&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByProject&quot; }
                },
                {
                    &quot;description&quot;: &quot;by project&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByWorker&quot; }
                },
                {
                    &quot;description&quot;: &quot;by worker&quot;,
                    &quot;stageType&quot;: &quot;CHART&quot;,
                    &quot;seriesDefs&quot;: [
                        { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                    ]
                },
                {
                    &quot;description&quot;: &quot;-not visible-&quot;,
                    &quot;stageType&quot;: &quot;ANALYSER&quot;,
                    &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByClient&quot; }
                },
                {
                    &quot;description&quot;: &quot;breakdown of visits&quot;,
                    &quot;stageType&quot;: &quot;TABLE&quot;,
                    &quot;tableRepresentation&quot;: {
                        &quot;className&quot;: &quot;VisitsAnalysis&quot;,
                        &quot;columns&quot;: [&quot;client&quot;, &quot;time spent&quot;, &quot;latest work&quot;,
                            {&quot;title&quot;: &quot;total visits&quot;,       &quot;representation&quot;: &quot;visits&quot;, &quot;path&quot;:[&quot;totalVisits&quot;]},
                            {&quot;title&quot;:&quot;visits : -&quot;,          &quot;representation&quot;: &quot;visits&quot;, &quot;path&quot;:[&quot;countByCommentType&quot;,&quot;-&quot;]},
                            {&quot;title&quot;:&quot;visits : one-to-one&quot;, &quot;representation&quot;: &quot;visits&quot;, &quot;path&quot;:[&quot;countByCommentType&quot;,&quot;one-to-one&quot;]},
                            &quot;breakdown&quot;
                        ]
                    }
                }
            ]
        }
            </column>
            <where>uuid='01800000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- referrals with support work by service, then by support work, then breakdown -->
    <!-- rename -->
    <changeSet id="ECCO-1321-repDef-00700000-rename" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="support work by service, then by author, then referral breakdown"/>
            <where>uuid='00700000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- support work by service, then by author, then referral breakdown -->
    <!-- correct count of visits first -->
    <changeSet id="ECCO-1321-repDef-00700000-correctedCount" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on support work this period showing time spent and number of entries&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;,    &quot;valuePath&quot;: &quot;totalVisits&quot;,  &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByAuthor&quot; }
        },
        {
            &quot;description&quot;: &quot;inner ring: number of entries, outer ring: 'time (mins)'&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;mins spent&quot;,
                    &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                },
                {
                    &quot;label&quot;: &quot;number of entries&quot;,
                    &quot;valuePath&quot;: &quot;totalVisits&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                    &quot;achieved&quot;, &quot;outstanding&quot;]
                }
        }
    ]
}
            </column>
            <where>uuid='00700000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- goals by service, then by project, then by worker, then breakdown -->
    <changeSet id="ECCO-1321-repDef-005b0000" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="005b0000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="5"/>
            <column name="name" value="goals by service, then by project, then by worker, then breakdown"/>
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals this period&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count of goals&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count of goals&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;smartStepCountsByWorker&quot; }
        },
        {
            &quot;description&quot;: &quot;by worker&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count of goals&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals with goals outstanding/achieved&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [&quot;rid&quot;, &quot;cid&quot;, &quot;client&quot;, &quot;project&quot;, &quot;from&quot;, &quot;received&quot;, &quot;status now&quot;, &quot;worker&quot;,
                    &quot;achieved&quot;, &quot;outstanding&quot;]
                }
        }
    ]
}
            </column>
        </insert>
    </changeSet>


    <!-- support work by service, then by author, then referral breakdown -->
    <changeSet id="ECCO-1321-repDef-deliveredTimeByClient" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04100000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="10"/>
            <column name="name" value="support work by service, then by author, then referral breakdown"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>

    <!-- rename -->
    <changeSet id="ECCO-1321-repDef-deliveredTimeByClient-rename" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="name" value="support work by service, then by project, then time by referral breakdown"/>
            <where>uuid='04100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- support work time by service, then by project, then referral breakdown -->
    <!-- correct count of visits first -->
    <changeSet id="ECCO-1321-repDef-deliveredTimeByClient-withWorkerWeekly" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on support work this period&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfWeekMonday&quot;,
        &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: [&quot;supportWork&quot;]
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByService&quot; }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;visitCountsByProject&quot; }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;totalVisits&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
            ]
        },
        {
           &quot;description&quot;: &quot;-not visible-&quot;,
           &quot;stageType&quot;: &quot;ANALYSER&quot;,
           &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workFromReferrals&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workByRid&quot; }
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;workGroupToVisitGroup&quot; }
        },
        {
            &quot;description&quot;: &quot;breakdown of visits&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;VisitsAnalysis&quot;,
                &quot;columns&quot;: [
                    &quot;r-id&quot;,
                    &quot;c-id&quot;,
                    &quot;client name&quot;,
                    &quot;worker&quot;,
                    &quot;time spent&quot;,
                    &quot;visits&quot;,
                    &quot;average time spent&quot;
                ]
            }
        }

    ]
}
            </column>
            <where>uuid='04100000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- 'onboarding' timings of a referral -->
    <changeSet id="ECCO-1933-repDef-timeToInterview" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04200000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2016-04-26T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="3"/>
            <column name="name" value="referrals by service, then by project, then by worker, then time to contact"/>
            <column name="body">{}</column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-1933-repDef-timeToInterview1" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
{
    &quot;description&quot;: &quot;on referrals this quarter&quot;,
    &quot;selectionCriteria&quot;: {
        &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
        &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
        &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
        &quot;relativeStartIndex&quot;: 0,
        &quot;relativeEndIndex&quot;: 1,
        &quot;fetchRelatedEntities&quot;: []
    },
    &quot;stages&quot;: [
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByService&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByProject&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByWorker&quot;
            }
        },
        {
            &quot;description&quot;: &quot;by worker&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
                {
                    &quot;label&quot;: &quot;count&quot;,
                    &quot;valuePath&quot;: &quot;count&quot;,
                    &quot;renderMode&quot;: &quot;PIE&quot;
                }
            ]
        },
        {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                    &quot;rid&quot;,
                    &quot;cid&quot;,
                    &quot;client&quot;,
                    &quot;service&quot;,
                    &quot;project&quot;,
                    &quot;from&quot;,
                    &quot;received&quot;,
                    &quot;first contact&quot;,
                    &quot;interview (offered)&quot;,
                    &quot;interview&quot;,
                    &quot;received to first contact&quot;,
                    &quot;received to interview (offered)&quot;,
                    &quot;received to interview&quot;,
                    &quot;decided&quot;,
                    &quot;start&quot;,
                    &quot;worker&quot;
                ]
            }
        }
    ]
}
            </column>
            <where>uuid='04200000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- referrals (rejected) by service, then by project, then by signposted reason, then breakdown -->
    <changeSet id="ECCO-1628-repDef-rejected-withSignpostTo" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;on referrals closed this quarter&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;referral.decision&quot;,
                &quot;referralStatus&quot;: &quot;rejected&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: []
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByService&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by service&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsByProject&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by project&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;referralCountsBySignpostReason&quot;
                }
                },
                {
                &quot;description&quot;: &quot;referrals showing signposted reasons&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;reason&quot;,
                &quot;valuePath&quot;: &quot;count&quot;,
                &quot;renderMode&quot;: &quot;BAR&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of referrals&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                &quot;rid&quot;,
                &quot;cid&quot;,
                &quot;client&quot;,
                &quot;project&quot;,
                &quot;from&quot;,
                &quot;received&quot;,
                &quot;status now&quot;,
                &quot;worker&quot;,
                &quot;rejected to&quot;
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='03000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <!-- referrals by service, then by project, then by worker, then time from need to support by referral breakdown -->
    <changeSet id="ECCO-1628-repDef-timeNeedsToSupport" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04300000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="36"/>
            <column name="name" value="referrals by service, then by project, then by worker, then time from need to support by referral breakdown"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-1628-repDef-timeNeedsToSupport-body" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;on all referrals received&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;fetchRelatedEntities&quot;: [
                &quot;supportWork&quot;
                ]
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByProject&quot; }
                },
                {
                &quot;description&quot;: &quot;by project&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: { &quot;analyserType&quot;: &quot;referralCountsByWorker&quot; }
                },
                {
                &quot;description&quot;: &quot;by worker&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                { &quot;label&quot;: &quot;count&quot;, &quot;valuePath&quot;: &quot;count&quot;, &quot;renderMode&quot;: &quot;PIE&quot; }
                ]
                },
                {
                &quot;description&quot;: &quot;breakdown of referrals&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                &quot;rid&quot;,
                &quot;cid&quot;,
                &quot;client&quot;,
                &quot;service&quot;,
                &quot;project&quot;,
                &quot;from&quot;,
                &quot;received&quot;,
                &quot;decided&quot;,
                &quot;needs to support&quot;,
                &quot;status now&quot;,
                &quot;worker&quot;
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='04300000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>


    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <!-- referrals by service, then by project, then by interviewer, then breakdown -->
    <changeSet id="ECCO-1628-repDef-byInterviewer1" author="adamjhamer">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="04400000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2015-05-01T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="5"/>
            <column name="name" value="referrals by service, then by project, then by interviewer, then breakdown"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-1628-repDef-byInterviewer1-body" author="adamjhamer">
        <update tableName="reportdefinitions">
        <column name="body">
            {
            &quot;description&quot;: &quot;on referrals this quarter&quot;,
            &quot;selectionCriteria&quot;: {
            &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
            &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
            &quot;selectionPropertyPath&quot;: &quot;referral.receivedDate&quot;,
            &quot;relativeStartIndex&quot;: 0,
            &quot;relativeEndIndex&quot;: 1,
            &quot;fetchRelatedEntities&quot;: []
            },
            &quot;stages&quot;: [
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;referralCountsByService&quot;
            }
            },
            {
            &quot;description&quot;: &quot;by service&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;count&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;referralCountsByProject&quot;
            }
            },
            {
            &quot;description&quot;: &quot;by project&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;count&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;-not visible-&quot;,
            &quot;stageType&quot;: &quot;ANALYSER&quot;,
            &quot;analyserConfig&quot;: {
            &quot;analyserType&quot;: &quot;referralCountsByInterviewer1&quot;
            }
            },
            {
            &quot;description&quot;: &quot;by interviewer&quot;,
            &quot;stageType&quot;: &quot;CHART&quot;,
            &quot;seriesDefs&quot;: [
            {
            &quot;label&quot;: &quot;count&quot;,
            &quot;valuePath&quot;: &quot;count&quot;,
            &quot;renderMode&quot;: &quot;PIE&quot;
            }
            ]
            },
            {
            &quot;description&quot;: &quot;breakdown of referrals&quot;,
            &quot;stageType&quot;: &quot;TABLE&quot;,
            &quot;tableRepresentation&quot;: {
            &quot;className&quot;: &quot;ReferralReportItem&quot;,
            &quot;columns&quot;: [
            &quot;rid&quot;,
            &quot;cid&quot;,
            &quot;client&quot;,
            &quot;service&quot;,
            &quot;project&quot;,
            &quot;from&quot;,
            &quot;received&quot;,
            &quot;decided&quot;,
            &quot;start&quot;,
            &quot;exited&quot;,
            &quot;rejected&quot;,
            &quot;status now&quot;,
            &quot;interviewer&quot;
            ]
            }
            }
            ]
            }
        </column>
        <where>uuid='04400000-0000-babe-babe-dadafee1600d'</where>
    </update>
    </changeSet>

    <!-- create a placeholder so we only have one definition in the file that we can keep updating -->
    <changeSet id="ECCO-2046-repDef-KPI1" author="nealeu">
        <insert tableName="reportdefinitions">
            <column name="uuid" value="10000000-0000-babe-babe-dadafee1600d"/>
            <column name="version" valueNumeric="0"/>
            <column name="created" valueDate="2016-07-12T07:00:00"/>
            <column name="userId" valueNumeric="1"/>
            <column name="orderby" valueNumeric="1"/>
            <column name="name" value="totals by service then KPIs"/>
            <column name="body">
                {}
            </column>
        </insert>
    </changeSet>
    <changeSet id="ECCO-2046-repDef-KPI1-body-3" author="nealeu">
        <update tableName="reportdefinitions">
            <column name="orderby" valueNumeric="-20"/>
            <column name="body">
                {
                "description": "client contact summary",
                "selectionCriteria": {
                "groupBy": "serviceRecipient.service",
                "selectionRootEntity": "GroupedWorkAnalysis",
                "selectorType": "byStartOfQuarter",
                "selectionPropertyPath": "workDate",

                "relativeStartIndex": 0,
                "relativeEndIndex": 1,
                "fetchRelatedEntities": []
                },
                "stages": [
                {
                "description": "by service",
                "stageType": "CHART",
                "selectionAnalyser": "single",
                "seriesDefs": [
                {
                "label": "count",
                "valuePath": "totalVisits",
                "renderMode": "PIE"
                }
                ]
                },
                {
                "description": "Time spent with clients in period",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comments",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "time spent"
                }
                },
                {
                "description": "average time per visit",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-comment",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "average time spent"
                }
                },
                {
                "description": "latest work",
                "stageType": "BADGE",
                "badgeRepresentation": {
                "badgeIconCssClasses": "fa fa-calendar",
                "recordRepresentationClassName": "VisitsAnalysis",
                "mainIndicatorValue": "latest work"
                }
                }
                ]
                }
            </column>
            <where>uuid='10000000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <changeSet id="z1083-table-to-table-fixes" author="adamjhamer">
        <update tableName="reportdefinitions">
            <column name="body">
                {
                &quot;description&quot;: &quot;on support work this quarter&quot;,
                &quot;selectionCriteria&quot;: {
                &quot;selectionRootEntity&quot;: &quot;Referral&quot;,
                &quot;selectorType&quot;: &quot;byStartOfQuarter&quot;,
                &quot;selectionPropertyPath&quot;: &quot;supportWork.workDate&quot;,
                &quot;relativeStartIndex&quot;: 0,
                &quot;relativeEndIndex&quot;: 1,
                &quot;fetchRelatedEntities&quot;: [
                &quot;supportWork&quot;
                ]
                },
                &quot;stages&quot;: [
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;visitCountsByService&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by service&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;visitCountsByProject&quot;
                }
                },
                {
                &quot;description&quot;: &quot;by project&quot;,
                &quot;stageType&quot;: &quot;CHART&quot;,
                &quot;seriesDefs&quot;: [
                {
                &quot;label&quot;: &quot;count&quot;,
                &quot;valuePath&quot;: &quot;totalTimeSpentMins&quot;,
                &quot;renderMode&quot;: &quot;PIE&quot;
                }
                ]
                },
                {
                &quot;description&quot;: &quot;by worker&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;selectionAnalyser&quot;: &quot;single&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;ReferralReportItem&quot;,
                &quot;columns&quot;: [
                &quot;rid&quot;,
                &quot;cid&quot;,
                &quot;client&quot;,
                &quot;service&quot;,
                &quot;project&quot;,
                &quot;from&quot;,
                &quot;received&quot;,
                &quot;status now&quot;,
                &quot;worker&quot;,
                &quot;achieved&quot;,
                &quot;outstanding&quot;
                ]
                }
                },
                {
                &quot;description&quot;: &quot;-not visible-&quot;,
                &quot;stageType&quot;: &quot;ANALYSER&quot;,
                &quot;analyserConfig&quot;: {
                &quot;analyserType&quot;: &quot;workFromReferrals&quot;
                }
                },
                {
                &quot;description&quot;: &quot;breakdown of support work&quot;,
                &quot;stageType&quot;: &quot;TABLE&quot;,
                &quot;tableRepresentation&quot;: {
                &quot;className&quot;: &quot;Work&quot;,
                &quot;columns&quot;: [
                &quot;r-id&quot;,
                &quot;client&quot;,
                &quot;created&quot;,
                &quot;worker&quot;,
                &quot;work date&quot;,
                &quot;type&quot;,
                &quot;time (mins)&quot;
                ]
                }
                }
                ]
                }
            </column>
            <where>uuid='01300000-0000-babe-babe-dadafee1600d'</where>
        </update>
    </changeSet>

    <!-- TAKE NOTE: ARE YOU SURE it goes here, and not in the configDomain or securityDomainChangeLog -->
</databaseChangeLog>
