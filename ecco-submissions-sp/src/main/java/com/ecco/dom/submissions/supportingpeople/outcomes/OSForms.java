//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2011.08.27 at 07:14:34 PM BST
//


package com.ecco.dom.submissions.supportingpeople.outcomes;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SubmissionDetails">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="Forename" type="{https://supportingpeople.st-andrews.ac.uk}StringData50"/>
 *                   &lt;element name="Surname" type="{https://supportingpeople.st-andrews.ac.uk}StringData50"/>
 *                   &lt;element name="Telephone" type="{https://supportingpeople.st-andrews.ac.uk}StringData12"/>
 *                   &lt;element name="Email" type="{https://supportingpeople.st-andrews.ac.uk}Email"/>
 *                   &lt;element name="DateCompleted" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *         &lt;element name="OS_Form" maxOccurs="unbounded">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="ProviderServiceDetails">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="NCRP_ID" type="{https://supportingpeople.st-andrews.ac.uk}ClientRecordID"/>
 *                             &lt;element name="National_ID" type="{https://supportingpeople.st-andrews.ac.uk}NationalID"/>
 *                             &lt;element name="ProviderName" type="{https://supportingpeople.st-andrews.ac.uk}StringData255"/>
 *                             &lt;element name="AdminAuth" type="{https://supportingpeople.st-andrews.ac.uk}StringData4"/>
 *                             &lt;element name="ServiceName" type="{https://supportingpeople.st-andrews.ac.uk}StringData255"/>
 *                             &lt;element name="SPServiceID" type="{https://supportingpeople.st-andrews.ac.uk}StringData30"/>
 *                             &lt;element name="SupportPlan" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="ClientDeath" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="ServiceType" type="{https://supportingpeople.st-andrews.ac.uk}ServiceTypeShort"/>
 *                             &lt;element name="Partnership" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
 *                             &lt;element name="PartnershipHealth" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipSocial" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipHousing" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipDrug" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipPolice" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipYOT" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipEducation" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipBenefits" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipDebt" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipEmployment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PartnershipOther" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="ServiceDuration">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Start_Date" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *                             &lt;element name="End_Date" type="{https://supportingpeople.st-andrews.ac.uk}ReportingYear1112"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="ClientDetails">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="ClientBudget" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
 *                             &lt;element name="ClientBudgetUse" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
 *                             &lt;element name="RentDepositScheme" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
 *                             &lt;element name="TenantCode" type="{https://supportingpeople.st-andrews.ac.uk}StringData12"/>
 *                             &lt;element name="ClientAge" type="{https://supportingpeople.st-andrews.ac.uk}AgeRange"/>
 *                             &lt;element name="ClientSex" type="{https://supportingpeople.st-andrews.ac.uk}Sex"/>
 *                             &lt;element name="ClientEcStat" type="{https://supportingpeople.st-andrews.ac.uk}EconomicStatus"/>
 *                             &lt;element name="EthnicOrigin" type="{https://supportingpeople.st-andrews.ac.uk}EthnicOrigin"/>
 *                             &lt;element name="Religion" type="{https://supportingpeople.st-andrews.ac.uk}Religion"/>
 *                             &lt;element name="DisabilityYesNo" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
 *                             &lt;element name="Mobility" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Visual" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Hearing" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Chronic" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Mental" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Learning" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Autistic" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Refused" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="Other" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                             &lt;element name="PrimaryClientGroup" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
 *                             &lt;element name="SecondaryClientGroup1" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
 *                             &lt;element name="SecondaryClientGroup2" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
 *                             &lt;element name="SecondaryClientGroup3" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
 *                             &lt;element name="NINumber">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;sequence>
 *                                       &lt;element name="NINumber" type="{https://supportingpeople.st-andrews.ac.uk}NINumber"/>
 *                                       &lt;element name="NIUnknown" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                                       &lt;element name="NIRefused" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                                       &lt;element name="NoNINumber" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *                                     &lt;/sequence>
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="AccomType" type="{https://supportingpeople.st-andrews.ac.uk}AccomType"/>
 *                             &lt;element name="LocalAuthONS" type="{https://supportingpeople.st-andrews.ac.uk}ONSCode"/>
 *                             &lt;element name="PlannedWay" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="GreaterIndependence" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="MaxIncome_1a">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="ManagingDebt_1b">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="PaidWork_1c">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
 *                             &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
 *                             &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
 *                             &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
 *                             &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
 *                             &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="Training_2a">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
 *                             &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
 *                             &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
 *                             &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
 *                             &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
 *                             &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
 *                             &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="Informal_2b">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="Worklike_2c">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="External_2d">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
 *                             &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
 *                             &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
 *                             &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
 *                             &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
 *                             &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
 *                             &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
 *                             &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="PrimaryCare_3a">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="MentalHealth_3b">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="Substance_3c">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="Adaptation_3d">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="MaintainAccom_4a">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="SettledAccom_4a">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="StatOrder_4b">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="SelfHarm_4c">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="HarmToOthers_4c">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="HarmFromOthers_4c">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                   &lt;element name="ChoiceControl_5">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
 *                             &lt;element name="ContLevel" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
 *                             &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
 *                             &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
 *                             &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "submissionDetails",
    "osForm"
})
@XmlRootElement(name = "OS_Forms")
public class OSForms
    implements Serializable
{

    private final static long serialVersionUID = 100L;
    @XmlElement(name = "SubmissionDetails", required = true)
    protected OSForms.SubmissionDetails submissionDetails;
    @XmlElement(name = "OS_Form", required = true)
    protected List<OSForms.OSForm> osForm;

    /**
     * Gets the value of the submissionDetails property.
     *
     * @return
     *     possible object is
     *     {@link OSForms.SubmissionDetails }
     *
     */
    public OSForms.SubmissionDetails getSubmissionDetails() {
        return submissionDetails;
    }

    /**
     * Sets the value of the submissionDetails property.
     *
     * @param value
     *     allowed object is
     *     {@link OSForms.SubmissionDetails }
     *
     */
    public void setSubmissionDetails(OSForms.SubmissionDetails value) {
        this.submissionDetails = value;
    }

    /**
     * Gets the value of the osForm property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the osForm property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOSForm().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OSForms.OSForm }
     *
     *
     */
    public List<OSForms.OSForm> getOSForm() {
        if (osForm == null) {
            osForm = new ArrayList<OSForms.OSForm>();
        }
        return this.osForm;
    }


    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="ProviderServiceDetails">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="NCRP_ID" type="{https://supportingpeople.st-andrews.ac.uk}ClientRecordID"/>
     *                   &lt;element name="National_ID" type="{https://supportingpeople.st-andrews.ac.uk}NationalID"/>
     *                   &lt;element name="ProviderName" type="{https://supportingpeople.st-andrews.ac.uk}StringData255"/>
     *                   &lt;element name="AdminAuth" type="{https://supportingpeople.st-andrews.ac.uk}StringData4"/>
     *                   &lt;element name="ServiceName" type="{https://supportingpeople.st-andrews.ac.uk}StringData255"/>
     *                   &lt;element name="SPServiceID" type="{https://supportingpeople.st-andrews.ac.uk}StringData30"/>
     *                   &lt;element name="SupportPlan" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="ClientDeath" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="ServiceType" type="{https://supportingpeople.st-andrews.ac.uk}ServiceTypeShort"/>
     *                   &lt;element name="Partnership" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
     *                   &lt;element name="PartnershipHealth" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipSocial" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipHousing" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipDrug" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipPolice" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipYOT" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipEducation" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipBenefits" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipDebt" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipEmployment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PartnershipOther" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="ServiceDuration">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Start_Date" type="{http://www.w3.org/2001/XMLSchema}date"/>
     *                   &lt;element name="End_Date" type="{https://supportingpeople.st-andrews.ac.uk}ReportingYear1112"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="ClientDetails">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="ClientBudget" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
     *                   &lt;element name="ClientBudgetUse" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
     *                   &lt;element name="RentDepositScheme" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
     *                   &lt;element name="TenantCode" type="{https://supportingpeople.st-andrews.ac.uk}StringData12"/>
     *                   &lt;element name="ClientAge" type="{https://supportingpeople.st-andrews.ac.uk}AgeRange"/>
     *                   &lt;element name="ClientSex" type="{https://supportingpeople.st-andrews.ac.uk}Sex"/>
     *                   &lt;element name="ClientEcStat" type="{https://supportingpeople.st-andrews.ac.uk}EconomicStatus"/>
     *                   &lt;element name="EthnicOrigin" type="{https://supportingpeople.st-andrews.ac.uk}EthnicOrigin"/>
     *                   &lt;element name="Religion" type="{https://supportingpeople.st-andrews.ac.uk}Religion"/>
     *                   &lt;element name="DisabilityYesNo" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
     *                   &lt;element name="Mobility" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Visual" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Hearing" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Chronic" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Mental" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Learning" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Autistic" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Refused" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="Other" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                   &lt;element name="PrimaryClientGroup" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
     *                   &lt;element name="SecondaryClientGroup1" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
     *                   &lt;element name="SecondaryClientGroup2" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
     *                   &lt;element name="SecondaryClientGroup3" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
     *                   &lt;element name="NINumber">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;sequence>
     *                             &lt;element name="NINumber" type="{https://supportingpeople.st-andrews.ac.uk}NINumber"/>
     *                             &lt;element name="NIUnknown" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                             &lt;element name="NIRefused" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                             &lt;element name="NoNINumber" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
     *                           &lt;/sequence>
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="AccomType" type="{https://supportingpeople.st-andrews.ac.uk}AccomType"/>
     *                   &lt;element name="LocalAuthONS" type="{https://supportingpeople.st-andrews.ac.uk}ONSCode"/>
     *                   &lt;element name="PlannedWay" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="GreaterIndependence" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="MaxIncome_1a">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="ManagingDebt_1b">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="PaidWork_1c">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
     *                   &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
     *                   &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
     *                   &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
     *                   &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
     *                   &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="Training_2a">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
     *                   &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
     *                   &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
     *                   &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
     *                   &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
     *                   &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
     *                   &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="Informal_2b">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="Worklike_2c">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="External_2d">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
     *                   &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
     *                   &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
     *                   &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
     *                   &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
     *                   &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
     *                   &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
     *                   &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="PrimaryCare_3a">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="MentalHealth_3b">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="Substance_3c">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="Adaptation_3d">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="MaintainAccom_4a">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="SettledAccom_4a">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="StatOrder_4b">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="SelfHarm_4c">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="HarmToOthers_4c">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="HarmFromOthers_4c">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *         &lt;element name="ChoiceControl_5">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
     *                   &lt;element name="ContLevel" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
     *                   &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
     *                   &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
     *                   &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     *
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "providerServiceDetails",
        "serviceDuration",
        "clientDetails",
        "maxIncome1A",
        "managingDebt1B",
        "paidWork1C",
        "training2A",
        "informal2B",
        "worklike2C",
        "external2D",
        "primaryCare3A",
        "mentalHealth3B",
        "substance3C",
        "adaptation3D",
        "maintainAccom4A",
        "settledAccom4A",
        "statOrder4B",
        "selfHarm4C",
        "harmToOthers4C",
        "harmFromOthers4C",
        "choiceControl5"
    })
    public static class OSForm
        implements Serializable
    {

        private final static long serialVersionUID = 100L;
        @XmlElement(name = "ProviderServiceDetails", required = true)
        protected OSForms.OSForm.ProviderServiceDetails providerServiceDetails;
        @XmlElement(name = "ServiceDuration", required = true)
        protected OSForms.OSForm.ServiceDuration serviceDuration;
        @XmlElement(name = "ClientDetails", required = true)
        protected OSForms.OSForm.ClientDetails clientDetails;
        @XmlElement(name = "MaxIncome_1a", required = true)
        protected OSForms.OSForm.MaxIncome1A maxIncome1A;
        @XmlElement(name = "ManagingDebt_1b", required = true)
        protected OSForms.OSForm.ManagingDebt1B managingDebt1B;
        @XmlElement(name = "PaidWork_1c", required = true)
        protected OSForms.OSForm.PaidWork1C paidWork1C;
        @XmlElement(name = "Training_2a", required = true)
        protected OSForms.OSForm.Training2A training2A;
        @XmlElement(name = "Informal_2b", required = true)
        protected OSForms.OSForm.Informal2B informal2B;
        @XmlElement(name = "Worklike_2c", required = true)
        protected OSForms.OSForm.Worklike2C worklike2C;
        @XmlElement(name = "External_2d", required = true)
        protected OSForms.OSForm.External2D external2D;
        @XmlElement(name = "PrimaryCare_3a", required = true)
        protected OSForms.OSForm.PrimaryCare3A primaryCare3A;
        @XmlElement(name = "MentalHealth_3b", required = true)
        protected OSForms.OSForm.MentalHealth3B mentalHealth3B;
        @XmlElement(name = "Substance_3c", required = true)
        protected OSForms.OSForm.Substance3C substance3C;
        @XmlElement(name = "Adaptation_3d", required = true)
        protected OSForms.OSForm.Adaptation3D adaptation3D;
        @XmlElement(name = "MaintainAccom_4a", required = true)
        protected OSForms.OSForm.MaintainAccom4A maintainAccom4A;
        @XmlElement(name = "SettledAccom_4a", required = true)
        protected OSForms.OSForm.SettledAccom4A settledAccom4A;
        @XmlElement(name = "StatOrder_4b", required = true)
        protected OSForms.OSForm.StatOrder4B statOrder4B;
        @XmlElement(name = "SelfHarm_4c", required = true)
        protected OSForms.OSForm.SelfHarm4C selfHarm4C;
        @XmlElement(name = "HarmToOthers_4c", required = true)
        protected OSForms.OSForm.HarmToOthers4C harmToOthers4C;
        @XmlElement(name = "HarmFromOthers_4c", required = true)
        protected OSForms.OSForm.HarmFromOthers4C harmFromOthers4C;
        @XmlElement(name = "ChoiceControl_5", required = true)
        protected OSForms.OSForm.ChoiceControl5 choiceControl5;

        /**
         * Gets the value of the providerServiceDetails property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.ProviderServiceDetails }
         *
         */
        public OSForms.OSForm.ProviderServiceDetails getProviderServiceDetails() {
            return providerServiceDetails;
        }

        /**
         * Sets the value of the providerServiceDetails property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.ProviderServiceDetails }
         *
         */
        public void setProviderServiceDetails(OSForms.OSForm.ProviderServiceDetails value) {
            this.providerServiceDetails = value;
        }

        /**
         * Gets the value of the serviceDuration property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.ServiceDuration }
         *
         */
        public OSForms.OSForm.ServiceDuration getServiceDuration() {
            return serviceDuration;
        }

        /**
         * Sets the value of the serviceDuration property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.ServiceDuration }
         *
         */
        public void setServiceDuration(OSForms.OSForm.ServiceDuration value) {
            this.serviceDuration = value;
        }

        /**
         * Gets the value of the clientDetails property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.ClientDetails }
         *
         */
        public OSForms.OSForm.ClientDetails getClientDetails() {
            return clientDetails;
        }

        /**
         * Sets the value of the clientDetails property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.ClientDetails }
         *
         */
        public void setClientDetails(OSForms.OSForm.ClientDetails value) {
            this.clientDetails = value;
        }

        /**
         * Gets the value of the maxIncome1A property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.MaxIncome1A }
         *
         */
        public OSForms.OSForm.MaxIncome1A getMaxIncome1A() {
            return maxIncome1A;
        }

        /**
         * Sets the value of the maxIncome1A property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.MaxIncome1A }
         *
         */
        public void setMaxIncome1A(OSForms.OSForm.MaxIncome1A value) {
            this.maxIncome1A = value;
        }

        /**
         * Gets the value of the managingDebt1B property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.ManagingDebt1B }
         *
         */
        public OSForms.OSForm.ManagingDebt1B getManagingDebt1B() {
            return managingDebt1B;
        }

        /**
         * Sets the value of the managingDebt1B property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.ManagingDebt1B }
         *
         */
        public void setManagingDebt1B(OSForms.OSForm.ManagingDebt1B value) {
            this.managingDebt1B = value;
        }

        /**
         * Gets the value of the paidWork1C property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.PaidWork1C }
         *
         */
        public OSForms.OSForm.PaidWork1C getPaidWork1C() {
            return paidWork1C;
        }

        /**
         * Sets the value of the paidWork1C property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.PaidWork1C }
         *
         */
        public void setPaidWork1C(OSForms.OSForm.PaidWork1C value) {
            this.paidWork1C = value;
        }

        /**
         * Gets the value of the training2A property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.Training2A }
         *
         */
        public OSForms.OSForm.Training2A getTraining2A() {
            return training2A;
        }

        /**
         * Sets the value of the training2A property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.Training2A }
         *
         */
        public void setTraining2A(OSForms.OSForm.Training2A value) {
            this.training2A = value;
        }

        /**
         * Gets the value of the informal2B property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.Informal2B }
         *
         */
        public OSForms.OSForm.Informal2B getInformal2B() {
            return informal2B;
        }

        /**
         * Sets the value of the informal2B property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.Informal2B }
         *
         */
        public void setInformal2B(OSForms.OSForm.Informal2B value) {
            this.informal2B = value;
        }

        /**
         * Gets the value of the worklike2C property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.Worklike2C }
         *
         */
        public OSForms.OSForm.Worklike2C getWorklike2C() {
            return worklike2C;
        }

        /**
         * Sets the value of the worklike2C property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.Worklike2C }
         *
         */
        public void setWorklike2C(OSForms.OSForm.Worklike2C value) {
            this.worklike2C = value;
        }

        /**
         * Gets the value of the external2D property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.External2D }
         *
         */
        public OSForms.OSForm.External2D getExternal2D() {
            return external2D;
        }

        /**
         * Sets the value of the external2D property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.External2D }
         *
         */
        public void setExternal2D(OSForms.OSForm.External2D value) {
            this.external2D = value;
        }

        /**
         * Gets the value of the primaryCare3A property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.PrimaryCare3A }
         *
         */
        public OSForms.OSForm.PrimaryCare3A getPrimaryCare3A() {
            return primaryCare3A;
        }

        /**
         * Sets the value of the primaryCare3A property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.PrimaryCare3A }
         *
         */
        public void setPrimaryCare3A(OSForms.OSForm.PrimaryCare3A value) {
            this.primaryCare3A = value;
        }

        /**
         * Gets the value of the mentalHealth3B property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.MentalHealth3B }
         *
         */
        public OSForms.OSForm.MentalHealth3B getMentalHealth3B() {
            return mentalHealth3B;
        }

        /**
         * Sets the value of the mentalHealth3B property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.MentalHealth3B }
         *
         */
        public void setMentalHealth3B(OSForms.OSForm.MentalHealth3B value) {
            this.mentalHealth3B = value;
        }

        /**
         * Gets the value of the substance3C property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.Substance3C }
         *
         */
        public OSForms.OSForm.Substance3C getSubstance3C() {
            return substance3C;
        }

        /**
         * Sets the value of the substance3C property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.Substance3C }
         *
         */
        public void setSubstance3C(OSForms.OSForm.Substance3C value) {
            this.substance3C = value;
        }

        /**
         * Gets the value of the adaptation3D property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.Adaptation3D }
         *
         */
        public OSForms.OSForm.Adaptation3D getAdaptation3D() {
            return adaptation3D;
        }

        /**
         * Sets the value of the adaptation3D property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.Adaptation3D }
         *
         */
        public void setAdaptation3D(OSForms.OSForm.Adaptation3D value) {
            this.adaptation3D = value;
        }

        /**
         * Gets the value of the maintainAccom4A property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.MaintainAccom4A }
         *
         */
        public OSForms.OSForm.MaintainAccom4A getMaintainAccom4A() {
            return maintainAccom4A;
        }

        /**
         * Sets the value of the maintainAccom4A property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.MaintainAccom4A }
         *
         */
        public void setMaintainAccom4A(OSForms.OSForm.MaintainAccom4A value) {
            this.maintainAccom4A = value;
        }

        /**
         * Gets the value of the settledAccom4A property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.SettledAccom4A }
         *
         */
        public OSForms.OSForm.SettledAccom4A getSettledAccom4A() {
            return settledAccom4A;
        }

        /**
         * Sets the value of the settledAccom4A property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.SettledAccom4A }
         *
         */
        public void setSettledAccom4A(OSForms.OSForm.SettledAccom4A value) {
            this.settledAccom4A = value;
        }

        /**
         * Gets the value of the statOrder4B property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.StatOrder4B }
         *
         */
        public OSForms.OSForm.StatOrder4B getStatOrder4B() {
            return statOrder4B;
        }

        /**
         * Sets the value of the statOrder4B property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.StatOrder4B }
         *
         */
        public void setStatOrder4B(OSForms.OSForm.StatOrder4B value) {
            this.statOrder4B = value;
        }

        /**
         * Gets the value of the selfHarm4C property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.SelfHarm4C }
         *
         */
        public OSForms.OSForm.SelfHarm4C getSelfHarm4C() {
            return selfHarm4C;
        }

        /**
         * Sets the value of the selfHarm4C property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.SelfHarm4C }
         *
         */
        public void setSelfHarm4C(OSForms.OSForm.SelfHarm4C value) {
            this.selfHarm4C = value;
        }

        /**
         * Gets the value of the harmToOthers4C property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.HarmToOthers4C }
         *
         */
        public OSForms.OSForm.HarmToOthers4C getHarmToOthers4C() {
            return harmToOthers4C;
        }

        /**
         * Sets the value of the harmToOthers4C property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.HarmToOthers4C }
         *
         */
        public void setHarmToOthers4C(OSForms.OSForm.HarmToOthers4C value) {
            this.harmToOthers4C = value;
        }

        /**
         * Gets the value of the harmFromOthers4C property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.HarmFromOthers4C }
         *
         */
        public OSForms.OSForm.HarmFromOthers4C getHarmFromOthers4C() {
            return harmFromOthers4C;
        }

        /**
         * Sets the value of the harmFromOthers4C property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.HarmFromOthers4C }
         *
         */
        public void setHarmFromOthers4C(OSForms.OSForm.HarmFromOthers4C value) {
            this.harmFromOthers4C = value;
        }

        /**
         * Gets the value of the choiceControl5 property.
         *
         * @return
         *     possible object is
         *     {@link OSForms.OSForm.ChoiceControl5 }
         *
         */
        public OSForms.OSForm.ChoiceControl5 getChoiceControl5() {
            return choiceControl5;
        }

        /**
         * Sets the value of the choiceControl5 property.
         *
         * @param value
         *     allowed object is
         *     {@link OSForms.OSForm.ChoiceControl5 }
         *
         */
        public void setChoiceControl5(OSForms.OSForm.ChoiceControl5 value) {
            this.choiceControl5 = value;
        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class Adaptation3D
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="ContLevel" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_5"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "contLevel",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class ChoiceControl5
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "ContLevel")
            protected int contLevel;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the contLevel property.
             *
             */
            public int getContLevel() {
                return contLevel;
            }

            /**
             * Sets the value of the contLevel property.
             *
             */
            public void setContLevel(int value) {
                this.contLevel = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="ClientBudget" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
         *         &lt;element name="ClientBudgetUse" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
         *         &lt;element name="RentDepositScheme" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
         *         &lt;element name="TenantCode" type="{https://supportingpeople.st-andrews.ac.uk}StringData12"/>
         *         &lt;element name="ClientAge" type="{https://supportingpeople.st-andrews.ac.uk}AgeRange"/>
         *         &lt;element name="ClientSex" type="{https://supportingpeople.st-andrews.ac.uk}Sex"/>
         *         &lt;element name="ClientEcStat" type="{https://supportingpeople.st-andrews.ac.uk}EconomicStatus"/>
         *         &lt;element name="EthnicOrigin" type="{https://supportingpeople.st-andrews.ac.uk}EthnicOrigin"/>
         *         &lt;element name="Religion" type="{https://supportingpeople.st-andrews.ac.uk}Religion"/>
         *         &lt;element name="DisabilityYesNo" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
         *         &lt;element name="Mobility" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Visual" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Hearing" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Chronic" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Mental" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Learning" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Autistic" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Refused" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="Other" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PrimaryClientGroup" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
         *         &lt;element name="SecondaryClientGroup1" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
         *         &lt;element name="SecondaryClientGroup2" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
         *         &lt;element name="SecondaryClientGroup3" type="{https://supportingpeople.st-andrews.ac.uk}ClientGroups"/>
         *         &lt;element name="NINumber">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;sequence>
         *                   &lt;element name="NINumber" type="{https://supportingpeople.st-andrews.ac.uk}NINumber"/>
         *                   &lt;element name="NIUnknown" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *                   &lt;element name="NIRefused" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *                   &lt;element name="NoNINumber" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *                 &lt;/sequence>
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="AccomType" type="{https://supportingpeople.st-andrews.ac.uk}AccomType"/>
         *         &lt;element name="LocalAuthONS" type="{https://supportingpeople.st-andrews.ac.uk}ONSCode"/>
         *         &lt;element name="PlannedWay" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="GreaterIndependence" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "clientBudget",
            "clientBudgetUse",
            "rentDepositScheme",
            "tenantCode",
            "clientAge",
            "clientSex",
            "clientEcStat",
            "ethnicOrigin",
            "religion",
            "disabilityYesNo",
            "mobility",
            "visual",
            "hearing",
            "chronic",
            "mental",
            "learning",
            "autistic",
            "refused",
            "other",
            "primaryClientGroup",
            "secondaryClientGroup1",
            "secondaryClientGroup2",
            "secondaryClientGroup3",
            "niNumber",
            "accomType",
            "localAuthONS",
            "plannedWay",
            "greaterIndependence"
        })
        public static class ClientDetails
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "ClientBudget")
            protected int clientBudget;
            @XmlElement(name = "ClientBudgetUse")
            protected int clientBudgetUse;
            @XmlElement(name = "RentDepositScheme")
            protected int rentDepositScheme;
            @XmlElement(name = "TenantCode", required = true)
            protected String tenantCode;
            @XmlElement(name = "ClientAge")
            protected int clientAge;
            @XmlElement(name = "ClientSex", required = true)
            protected String clientSex;
            @XmlElement(name = "ClientEcStat")
            protected int clientEcStat;
            @XmlElement(name = "EthnicOrigin")
            protected int ethnicOrigin;
            @XmlElement(name = "Religion")
            protected int religion;
            @XmlElement(name = "DisabilityYesNo")
            protected int disabilityYesNo;
            @XmlElement(name = "Mobility")
            protected boolean mobility;
            @XmlElement(name = "Visual")
            protected boolean visual;
            @XmlElement(name = "Hearing")
            protected boolean hearing;
            @XmlElement(name = "Chronic")
            protected boolean chronic;
            @XmlElement(name = "Mental")
            protected boolean mental;
            @XmlElement(name = "Learning")
            protected boolean learning;
            @XmlElement(name = "Autistic")
            protected boolean autistic;
            @XmlElement(name = "Refused")
            protected boolean refused;
            @XmlElement(name = "Other")
            protected boolean other;
            @XmlElement(name = "PrimaryClientGroup", required = true)
            protected BigInteger primaryClientGroup;
            @XmlElement(name = "SecondaryClientGroup1", required = true)
            protected BigInteger secondaryClientGroup1;
            @XmlElement(name = "SecondaryClientGroup2", required = true)
            protected BigInteger secondaryClientGroup2;
            @XmlElement(name = "SecondaryClientGroup3", required = true)
            protected BigInteger secondaryClientGroup3;
            @XmlElement(name = "NINumber", required = true)
            protected OSForms.OSForm.ClientDetails.NINumber niNumber;
            @XmlElement(name = "AccomType")
            protected int accomType;
            @XmlElement(name = "LocalAuthONS", required = true)
            protected String localAuthONS;
            @XmlElement(name = "PlannedWay", defaultValue = "0")
            protected int plannedWay;
            @XmlElement(name = "GreaterIndependence")
            protected int greaterIndependence;

            /**
             * Gets the value of the clientBudget property.
             *
             */
            public int getClientBudget() {
                return clientBudget;
            }

            /**
             * Sets the value of the clientBudget property.
             *
             */
            public void setClientBudget(int value) {
                this.clientBudget = value;
            }

            /**
             * Gets the value of the clientBudgetUse property.
             *
             */
            public int getClientBudgetUse() {
                return clientBudgetUse;
            }

            /**
             * Sets the value of the clientBudgetUse property.
             *
             */
            public void setClientBudgetUse(int value) {
                this.clientBudgetUse = value;
            }

            /**
             * Gets the value of the rentDepositScheme property.
             *
             */
            public int getRentDepositScheme() {
                return rentDepositScheme;
            }

            /**
             * Sets the value of the rentDepositScheme property.
             *
             */
            public void setRentDepositScheme(int value) {
                this.rentDepositScheme = value;
            }

            /**
             * Gets the value of the tenantCode property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getTenantCode() {
                return tenantCode;
            }

            /**
             * Sets the value of the tenantCode property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setTenantCode(String value) {
                this.tenantCode = value;
            }

            /**
             * Gets the value of the clientAge property.
             *
             */
            public int getClientAge() {
                return clientAge;
            }

            /**
             * Sets the value of the clientAge property.
             *
             */
            public void setClientAge(int value) {
                this.clientAge = value;
            }

            /**
             * Gets the value of the clientSex property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getClientSex() {
                return clientSex;
            }

            /**
             * Sets the value of the clientSex property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setClientSex(String value) {
                this.clientSex = value;
            }

            /**
             * Gets the value of the clientEcStat property.
             *
             */
            public int getClientEcStat() {
                return clientEcStat;
            }

            /**
             * Sets the value of the clientEcStat property.
             *
             */
            public void setClientEcStat(int value) {
                this.clientEcStat = value;
            }

            /**
             * Gets the value of the ethnicOrigin property.
             *
             */
            public int getEthnicOrigin() {
                return ethnicOrigin;
            }

            /**
             * Sets the value of the ethnicOrigin property.
             *
             */
            public void setEthnicOrigin(int value) {
                this.ethnicOrigin = value;
            }

            /**
             * Gets the value of the religion property.
             *
             */
            public int getReligion() {
                return religion;
            }

            /**
             * Sets the value of the religion property.
             *
             */
            public void setReligion(int value) {
                this.religion = value;
            }

            /**
             * Gets the value of the disabilityYesNo property.
             *
             */
            public int getDisabilityYesNo() {
                return disabilityYesNo;
            }

            /**
             * Sets the value of the disabilityYesNo property.
             *
             */
            public void setDisabilityYesNo(int value) {
                this.disabilityYesNo = value;
            }

            /**
             * Gets the value of the mobility property.
             *
             */
            public boolean isMobility() {
                return mobility;
            }

            /**
             * Sets the value of the mobility property.
             *
             */
            public void setMobility(boolean value) {
                this.mobility = value;
            }

            /**
             * Gets the value of the visual property.
             *
             */
            public boolean isVisual() {
                return visual;
            }

            /**
             * Sets the value of the visual property.
             *
             */
            public void setVisual(boolean value) {
                this.visual = value;
            }

            /**
             * Gets the value of the hearing property.
             *
             */
            public boolean isHearing() {
                return hearing;
            }

            /**
             * Sets the value of the hearing property.
             *
             */
            public void setHearing(boolean value) {
                this.hearing = value;
            }

            /**
             * Gets the value of the chronic property.
             *
             */
            public boolean isChronic() {
                return chronic;
            }

            /**
             * Sets the value of the chronic property.
             *
             */
            public void setChronic(boolean value) {
                this.chronic = value;
            }

            /**
             * Gets the value of the mental property.
             *
             */
            public boolean isMental() {
                return mental;
            }

            /**
             * Sets the value of the mental property.
             *
             */
            public void setMental(boolean value) {
                this.mental = value;
            }

            /**
             * Gets the value of the learning property.
             *
             */
            public boolean isLearning() {
                return learning;
            }

            /**
             * Sets the value of the learning property.
             *
             */
            public void setLearning(boolean value) {
                this.learning = value;
            }

            /**
             * Gets the value of the autistic property.
             *
             */
            public boolean isAutistic() {
                return autistic;
            }

            /**
             * Sets the value of the autistic property.
             *
             */
            public void setAutistic(boolean value) {
                this.autistic = value;
            }

            /**
             * Gets the value of the refused property.
             *
             */
            public boolean isRefused() {
                return refused;
            }

            /**
             * Sets the value of the refused property.
             *
             */
            public void setRefused(boolean value) {
                this.refused = value;
            }

            /**
             * Gets the value of the other property.
             *
             */
            public boolean isOther() {
                return other;
            }

            /**
             * Sets the value of the other property.
             *
             */
            public void setOther(boolean value) {
                this.other = value;
            }

            /**
             * Gets the value of the primaryClientGroup property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getPrimaryClientGroup() {
                return primaryClientGroup;
            }

            /**
             * Sets the value of the primaryClientGroup property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setPrimaryClientGroup(BigInteger value) {
                this.primaryClientGroup = value;
            }

            /**
             * Gets the value of the secondaryClientGroup1 property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getSecondaryClientGroup1() {
                return secondaryClientGroup1;
            }

            /**
             * Sets the value of the secondaryClientGroup1 property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setSecondaryClientGroup1(BigInteger value) {
                this.secondaryClientGroup1 = value;
            }

            /**
             * Gets the value of the secondaryClientGroup2 property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getSecondaryClientGroup2() {
                return secondaryClientGroup2;
            }

            /**
             * Sets the value of the secondaryClientGroup2 property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setSecondaryClientGroup2(BigInteger value) {
                this.secondaryClientGroup2 = value;
            }

            /**
             * Gets the value of the secondaryClientGroup3 property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getSecondaryClientGroup3() {
                return secondaryClientGroup3;
            }

            /**
             * Sets the value of the secondaryClientGroup3 property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setSecondaryClientGroup3(BigInteger value) {
                this.secondaryClientGroup3 = value;
            }

            /**
             * Gets the value of the niNumber property.
             *
             * @return
             *     possible object is
             *     {@link OSForms.OSForm.ClientDetails.NINumber }
             *
             */
            public OSForms.OSForm.ClientDetails.NINumber getNINumber() {
                return niNumber;
            }

            /**
             * Sets the value of the niNumber property.
             *
             * @param value
             *     allowed object is
             *     {@link OSForms.OSForm.ClientDetails.NINumber }
             *
             */
            public void setNINumber(OSForms.OSForm.ClientDetails.NINumber value) {
                this.niNumber = value;
            }

            /**
             * Gets the value of the accomType property.
             *
             */
            public int getAccomType() {
                return accomType;
            }

            /**
             * Sets the value of the accomType property.
             *
             */
            public void setAccomType(int value) {
                this.accomType = value;
            }

            /**
             * Gets the value of the localAuthONS property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getLocalAuthONS() {
                return localAuthONS;
            }

            /**
             * Sets the value of the localAuthONS property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setLocalAuthONS(String value) {
                this.localAuthONS = value;
            }

            /**
             * Gets the value of the plannedWay property.
             *
             */
            public int getPlannedWay() {
                return plannedWay;
            }

            /**
             * Sets the value of the plannedWay property.
             *
             */
            public void setPlannedWay(int value) {
                this.plannedWay = value;
            }

            /**
             * Gets the value of the greaterIndependence property.
             *
             */
            public int getGreaterIndependence() {
                return greaterIndependence;
            }

            /**
             * Sets the value of the greaterIndependence property.
             *
             */
            public void setGreaterIndependence(int value) {
                this.greaterIndependence = value;
            }


            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;sequence>
             *         &lt;element name="NINumber" type="{https://supportingpeople.st-andrews.ac.uk}NINumber"/>
             *         &lt;element name="NIUnknown" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
             *         &lt;element name="NIRefused" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
             *         &lt;element name="NoNINumber" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
             *       &lt;/sequence>
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                "niNumber",
                "niUnknown",
                "niRefused",
                "noNINumber"
            })
            public static class NINumber
                implements Serializable
            {

                private final static long serialVersionUID = 100L;
                @XmlElement(name = "NINumber", required = true)
                protected String niNumber;
                @XmlElement(name = "NIUnknown")
                protected boolean niUnknown;
                @XmlElement(name = "NIRefused")
                protected boolean niRefused;
                @XmlElement(name = "NoNINumber")
                protected boolean noNINumber;

                /**
                 * Gets the value of the niNumber property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getNINumber() {
                    return niNumber;
                }

                /**
                 * Sets the value of the niNumber property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setNINumber(String value) {
                    this.niNumber = value;
                }

                /**
                 * Gets the value of the niUnknown property.
                 *
                 */
                public boolean isNIUnknown() {
                    return niUnknown;
                }

                /**
                 * Sets the value of the niUnknown property.
                 *
                 */
                public void setNIUnknown(boolean value) {
                    this.niUnknown = value;
                }

                /**
                 * Gets the value of the niRefused property.
                 *
                 */
                public boolean isNIRefused() {
                    return niRefused;
                }

                /**
                 * Sets the value of the niRefused property.
                 *
                 */
                public void setNIRefused(boolean value) {
                    this.niRefused = value;
                }

                /**
                 * Gets the value of the noNINumber property.
                 *
                 */
                public boolean isNoNINumber() {
                    return noNINumber;
                }

                /**
                 * Sets the value of the noNINumber property.
                 *
                 */
                public void setNoNINumber(boolean value) {
                    this.noNINumber = value;
                }

            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
         *         &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
         *         &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
         *         &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_i"/>
         *         &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
         *         &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
         *         &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
         *         &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2d_ii"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcomeI",
            "reasonPrimaryI",
            "reasonSecondI",
            "reasonThirdI",
            "outcomeIi",
            "reasonPrimaryIi",
            "reasonSecondIi",
            "reasonThirdIi"
        })
        public static class External2D
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome_i")
            protected int outcomeI;
            @XmlElement(name = "Reason_Primary_i", required = true)
            protected BigInteger reasonPrimaryI;
            @XmlElement(name = "Reason_Second_i", required = true)
            protected BigInteger reasonSecondI;
            @XmlElement(name = "Reason_Third_i", required = true)
            protected BigInteger reasonThirdI;
            @XmlElement(name = "Outcome_ii")
            protected int outcomeIi;
            @XmlElement(name = "Reason_Primary_ii", required = true)
            protected BigInteger reasonPrimaryIi;
            @XmlElement(name = "Reason_Second_ii", required = true)
            protected BigInteger reasonSecondIi;
            @XmlElement(name = "Reason_Third_ii", required = true)
            protected BigInteger reasonThirdIi;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcomeI property.
             *
             */
            public int getOutcomeI() {
                return outcomeI;
            }

            /**
             * Sets the value of the outcomeI property.
             *
             */
            public void setOutcomeI(int value) {
                this.outcomeI = value;
            }

            /**
             * Gets the value of the reasonPrimaryI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimaryI() {
                return reasonPrimaryI;
            }

            /**
             * Sets the value of the reasonPrimaryI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimaryI(BigInteger value) {
                this.reasonPrimaryI = value;
            }

            /**
             * Gets the value of the reasonSecondI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecondI() {
                return reasonSecondI;
            }

            /**
             * Sets the value of the reasonSecondI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecondI(BigInteger value) {
                this.reasonSecondI = value;
            }

            /**
             * Gets the value of the reasonThirdI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThirdI() {
                return reasonThirdI;
            }

            /**
             * Sets the value of the reasonThirdI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThirdI(BigInteger value) {
                this.reasonThirdI = value;
            }

            /**
             * Gets the value of the outcomeIi property.
             *
             */
            public int getOutcomeIi() {
                return outcomeIi;
            }

            /**
             * Sets the value of the outcomeIi property.
             *
             */
            public void setOutcomeIi(int value) {
                this.outcomeIi = value;
            }

            /**
             * Gets the value of the reasonPrimaryIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimaryIi() {
                return reasonPrimaryIi;
            }

            /**
             * Sets the value of the reasonPrimaryIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimaryIi(BigInteger value) {
                this.reasonPrimaryIi = value;
            }

            /**
             * Gets the value of the reasonSecondIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecondIi() {
                return reasonSecondIi;
            }

            /**
             * Sets the value of the reasonSecondIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecondIi(BigInteger value) {
                this.reasonSecondIi = value;
            }

            /**
             * Gets the value of the reasonThirdIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThirdIi() {
                return reasonThirdIi;
            }

            /**
             * Sets the value of the reasonThirdIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThirdIi(BigInteger value) {
                this.reasonThirdIi = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class HarmFromOthers4C
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_ii"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class HarmToOthers4C
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2b"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class Informal2B
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class MaintainAccom4A
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1b"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class ManagingDebt1B
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1a"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class MaxIncome1A
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class MentalHealth3B
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
         *         &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
         *         &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
         *         &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
         *         &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
         *         &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_1c"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcomeI",
            "reasonPrimaryI",
            "reasonSecondI",
            "reasonThirdI",
            "outcomeIi",
            "reasonPrimaryIi",
            "reasonSecondIi",
            "reasonThirdIi"
        })
        public static class PaidWork1C
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome_i")
            protected int outcomeI;
            @XmlElement(name = "Reason_Primary_i", required = true)
            protected BigInteger reasonPrimaryI;
            @XmlElement(name = "Reason_Second_i", required = true)
            protected BigInteger reasonSecondI;
            @XmlElement(name = "Reason_Third_i", required = true)
            protected BigInteger reasonThirdI;
            @XmlElement(name = "Outcome_ii")
            protected int outcomeIi;
            @XmlElement(name = "Reason_Primary_ii", required = true)
            protected BigInteger reasonPrimaryIi;
            @XmlElement(name = "Reason_Second_ii", required = true)
            protected BigInteger reasonSecondIi;
            @XmlElement(name = "Reason_Third_ii", required = true)
            protected BigInteger reasonThirdIi;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcomeI property.
             *
             */
            public int getOutcomeI() {
                return outcomeI;
            }

            /**
             * Sets the value of the outcomeI property.
             *
             */
            public void setOutcomeI(int value) {
                this.outcomeI = value;
            }

            /**
             * Gets the value of the reasonPrimaryI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimaryI() {
                return reasonPrimaryI;
            }

            /**
             * Sets the value of the reasonPrimaryI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimaryI(BigInteger value) {
                this.reasonPrimaryI = value;
            }

            /**
             * Gets the value of the reasonSecondI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecondI() {
                return reasonSecondI;
            }

            /**
             * Sets the value of the reasonSecondI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecondI(BigInteger value) {
                this.reasonSecondI = value;
            }

            /**
             * Gets the value of the reasonThirdI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThirdI() {
                return reasonThirdI;
            }

            /**
             * Sets the value of the reasonThirdI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThirdI(BigInteger value) {
                this.reasonThirdI = value;
            }

            /**
             * Gets the value of the outcomeIi property.
             *
             */
            public int getOutcomeIi() {
                return outcomeIi;
            }

            /**
             * Sets the value of the outcomeIi property.
             *
             */
            public void setOutcomeIi(int value) {
                this.outcomeIi = value;
            }

            /**
             * Gets the value of the reasonPrimaryIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimaryIi() {
                return reasonPrimaryIi;
            }

            /**
             * Sets the value of the reasonPrimaryIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimaryIi(BigInteger value) {
                this.reasonPrimaryIi = value;
            }

            /**
             * Gets the value of the reasonSecondIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecondIi() {
                return reasonSecondIi;
            }

            /**
             * Sets the value of the reasonSecondIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecondIi(BigInteger value) {
                this.reasonSecondIi = value;
            }

            /**
             * Gets the value of the reasonThirdIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThirdIi() {
                return reasonThirdIi;
            }

            /**
             * Sets the value of the reasonThirdIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThirdIi(BigInteger value) {
                this.reasonThirdIi = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_i"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class PrimaryCare3A
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="NCRP_ID" type="{https://supportingpeople.st-andrews.ac.uk}ClientRecordID"/>
         *         &lt;element name="National_ID" type="{https://supportingpeople.st-andrews.ac.uk}NationalID"/>
         *         &lt;element name="ProviderName" type="{https://supportingpeople.st-andrews.ac.uk}StringData255"/>
         *         &lt;element name="AdminAuth" type="{https://supportingpeople.st-andrews.ac.uk}StringData4"/>
         *         &lt;element name="ServiceName" type="{https://supportingpeople.st-andrews.ac.uk}StringData255"/>
         *         &lt;element name="SPServiceID" type="{https://supportingpeople.st-andrews.ac.uk}StringData30"/>
         *         &lt;element name="SupportPlan" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="ClientDeath" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="ServiceType" type="{https://supportingpeople.st-andrews.ac.uk}ServiceTypeShort"/>
         *         &lt;element name="Partnership" type="{https://supportingpeople.st-andrews.ac.uk}YesNoDK"/>
         *         &lt;element name="PartnershipHealth" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipSocial" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipHousing" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipDrug" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipPolice" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipYOT" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipEducation" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipBenefits" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipDebt" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipEmployment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *         &lt;element name="PartnershipOther" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "ncrpid",
            "nationalID",
            "providerName",
            "adminAuth",
            "serviceName",
            "spServiceID",
            "supportPlan",
            "clientDeath",
            "serviceType",
            "partnership",
            "partnershipHealth",
            "partnershipSocial",
            "partnershipHousing",
            "partnershipDrug",
            "partnershipPolice",
            "partnershipYOT",
            "partnershipEducation",
            "partnershipBenefits",
            "partnershipDebt",
            "partnershipEmployment",
            "partnershipOther"
        })
        public static class ProviderServiceDetails
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "NCRP_ID")
            protected int ncrpid;
            @XmlElement(name = "National_ID")
            protected int nationalID;
            @XmlElement(name = "ProviderName", required = true)
            protected String providerName;
            @XmlElement(name = "AdminAuth", required = true)
            protected String adminAuth;
            @XmlElement(name = "ServiceName", required = true)
            protected String serviceName;
            @XmlElement(name = "SPServiceID", required = true)
            protected String spServiceID;
            @XmlElement(name = "SupportPlan")
            protected int supportPlan;
            @XmlElement(name = "ClientDeath")
            protected int clientDeath;
            @XmlElement(name = "ServiceType", required = true)
            protected BigInteger serviceType;
            @XmlElement(name = "Partnership")
            protected int partnership;
            @XmlElement(name = "PartnershipHealth")
            protected boolean partnershipHealth;
            @XmlElement(name = "PartnershipSocial")
            protected boolean partnershipSocial;
            @XmlElement(name = "PartnershipHousing")
            protected boolean partnershipHousing;
            @XmlElement(name = "PartnershipDrug")
            protected boolean partnershipDrug;
            @XmlElement(name = "PartnershipPolice")
            protected boolean partnershipPolice;
            @XmlElement(name = "PartnershipYOT")
            protected boolean partnershipYOT;
            @XmlElement(name = "PartnershipEducation")
            protected boolean partnershipEducation;
            @XmlElement(name = "PartnershipBenefits")
            protected boolean partnershipBenefits;
            @XmlElement(name = "PartnershipDebt")
            protected boolean partnershipDebt;
            @XmlElement(name = "PartnershipEmployment")
            protected boolean partnershipEmployment;
            @XmlElement(name = "PartnershipOther")
            protected boolean partnershipOther;

            /**
             * Gets the value of the ncrpid property.
             *
             */
            public int getNCRPID() {
                return ncrpid;
            }

            /**
             * Sets the value of the ncrpid property.
             *
             */
            public void setNCRPID(int value) {
                this.ncrpid = value;
            }

            /**
             * Gets the value of the nationalID property.
             *
             */
            public int getNationalID() {
                return nationalID;
            }

            /**
             * Sets the value of the nationalID property.
             *
             */
            public void setNationalID(int value) {
                this.nationalID = value;
            }

            /**
             * Gets the value of the providerName property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getProviderName() {
                return providerName;
            }

            /**
             * Sets the value of the providerName property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setProviderName(String value) {
                this.providerName = value;
            }

            /**
             * Gets the value of the adminAuth property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getAdminAuth() {
                return adminAuth;
            }

            /**
             * Sets the value of the adminAuth property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setAdminAuth(String value) {
                this.adminAuth = value;
            }

            /**
             * Gets the value of the serviceName property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getServiceName() {
                return serviceName;
            }

            /**
             * Sets the value of the serviceName property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setServiceName(String value) {
                this.serviceName = value;
            }

            /**
             * Gets the value of the spServiceID property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getSPServiceID() {
                return spServiceID;
            }

            /**
             * Sets the value of the spServiceID property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setSPServiceID(String value) {
                this.spServiceID = value;
            }

            /**
             * Gets the value of the supportPlan property.
             *
             */
            public int getSupportPlan() {
                return supportPlan;
            }

            /**
             * Sets the value of the supportPlan property.
             *
             */
            public void setSupportPlan(int value) {
                this.supportPlan = value;
            }

            /**
             * Gets the value of the clientDeath property.
             *
             */
            public int getClientDeath() {
                return clientDeath;
            }

            /**
             * Sets the value of the clientDeath property.
             *
             */
            public void setClientDeath(int value) {
                this.clientDeath = value;
            }

            /**
             * Gets the value of the serviceType property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getServiceType() {
                return serviceType;
            }

            /**
             * Sets the value of the serviceType property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setServiceType(BigInteger value) {
                this.serviceType = value;
            }

            /**
             * Gets the value of the partnership property.
             *
             */
            public int getPartnership() {
                return partnership;
            }

            /**
             * Sets the value of the partnership property.
             *
             */
            public void setPartnership(int value) {
                this.partnership = value;
            }

            /**
             * Gets the value of the partnershipHealth property.
             *
             */
            public boolean isPartnershipHealth() {
                return partnershipHealth;
            }

            /**
             * Sets the value of the partnershipHealth property.
             *
             */
            public void setPartnershipHealth(boolean value) {
                this.partnershipHealth = value;
            }

            /**
             * Gets the value of the partnershipSocial property.
             *
             */
            public boolean isPartnershipSocial() {
                return partnershipSocial;
            }

            /**
             * Sets the value of the partnershipSocial property.
             *
             */
            public void setPartnershipSocial(boolean value) {
                this.partnershipSocial = value;
            }

            /**
             * Gets the value of the partnershipHousing property.
             *
             */
            public boolean isPartnershipHousing() {
                return partnershipHousing;
            }

            /**
             * Sets the value of the partnershipHousing property.
             *
             */
            public void setPartnershipHousing(boolean value) {
                this.partnershipHousing = value;
            }

            /**
             * Gets the value of the partnershipDrug property.
             *
             */
            public boolean isPartnershipDrug() {
                return partnershipDrug;
            }

            /**
             * Sets the value of the partnershipDrug property.
             *
             */
            public void setPartnershipDrug(boolean value) {
                this.partnershipDrug = value;
            }

            /**
             * Gets the value of the partnershipPolice property.
             *
             */
            public boolean isPartnershipPolice() {
                return partnershipPolice;
            }

            /**
             * Sets the value of the partnershipPolice property.
             *
             */
            public void setPartnershipPolice(boolean value) {
                this.partnershipPolice = value;
            }

            /**
             * Gets the value of the partnershipYOT property.
             *
             */
            public boolean isPartnershipYOT() {
                return partnershipYOT;
            }

            /**
             * Sets the value of the partnershipYOT property.
             *
             */
            public void setPartnershipYOT(boolean value) {
                this.partnershipYOT = value;
            }

            /**
             * Gets the value of the partnershipEducation property.
             *
             */
            public boolean isPartnershipEducation() {
                return partnershipEducation;
            }

            /**
             * Sets the value of the partnershipEducation property.
             *
             */
            public void setPartnershipEducation(boolean value) {
                this.partnershipEducation = value;
            }

            /**
             * Gets the value of the partnershipBenefits property.
             *
             */
            public boolean isPartnershipBenefits() {
                return partnershipBenefits;
            }

            /**
             * Sets the value of the partnershipBenefits property.
             *
             */
            public void setPartnershipBenefits(boolean value) {
                this.partnershipBenefits = value;
            }

            /**
             * Gets the value of the partnershipDebt property.
             *
             */
            public boolean isPartnershipDebt() {
                return partnershipDebt;
            }

            /**
             * Sets the value of the partnershipDebt property.
             *
             */
            public void setPartnershipDebt(boolean value) {
                this.partnershipDebt = value;
            }

            /**
             * Gets the value of the partnershipEmployment property.
             *
             */
            public boolean isPartnershipEmployment() {
                return partnershipEmployment;
            }

            /**
             * Sets the value of the partnershipEmployment property.
             *
             */
            public void setPartnershipEmployment(boolean value) {
                this.partnershipEmployment = value;
            }

            /**
             * Gets the value of the partnershipOther property.
             *
             */
            public boolean isPartnershipOther() {
                return partnershipOther;
            }

            /**
             * Sets the value of the partnershipOther property.
             *
             */
            public void setPartnershipOther(boolean value) {
                this.partnershipOther = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4c_i"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class SelfHarm4C
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Start_Date" type="{http://www.w3.org/2001/XMLSchema}date"/>
         *         &lt;element name="End_Date" type="{https://supportingpeople.st-andrews.ac.uk}ReportingYear1112"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "startDate",
            "endDate"
        })
        public static class ServiceDuration
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Start_Date", required = true)
            @XmlSchemaType(name = "date")
            protected XMLGregorianCalendar startDate;
            @XmlElement(name = "End_Date", required = true)
            protected XMLGregorianCalendar endDate;

            /**
             * Gets the value of the startDate property.
             *
             * @return
             *     possible object is
             *     {@link XMLGregorianCalendar }
             *
             */
            public XMLGregorianCalendar getStartDate() {
                return startDate;
            }

            /**
             * Sets the value of the startDate property.
             *
             * @param value
             *     allowed object is
             *     {@link XMLGregorianCalendar }
             *
             */
            public void setStartDate(XMLGregorianCalendar value) {
                this.startDate = value;
            }

            /**
             * Gets the value of the endDate property.
             *
             * @return
             *     possible object is
             *     {@link XMLGregorianCalendar }
             *
             */
            public XMLGregorianCalendar getEndDate() {
                return endDate;
            }

            /**
             * Sets the value of the endDate property.
             *
             * @param value
             *     allowed object is
             *     {@link XMLGregorianCalendar }
             *
             */
            public void setEndDate(XMLGregorianCalendar value) {
                this.endDate = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4a_ii"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class SettledAccom4A
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_4b"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class StatOrder4B
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_3_ii"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class Substance3C
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome_i" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
         *         &lt;element name="Reason_Second_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
         *         &lt;element name="Reason_Third_i" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_i"/>
         *         &lt;element name="Outcome_ii" type="{https://supportingpeople.st-andrews.ac.uk}YesNoNAMissing"/>
         *         &lt;element name="Reason_Primary_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
         *         &lt;element name="Reason_Second_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
         *         &lt;element name="Reason_Third_ii" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2a_ii"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcomeI",
            "reasonPrimaryI",
            "reasonSecondI",
            "reasonThirdI",
            "outcomeIi",
            "reasonPrimaryIi",
            "reasonSecondIi",
            "reasonThirdIi"
        })
        public static class Training2A
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome_i")
            protected int outcomeI;
            @XmlElement(name = "Reason_Primary_i", required = true)
            protected BigInteger reasonPrimaryI;
            @XmlElement(name = "Reason_Second_i", required = true)
            protected BigInteger reasonSecondI;
            @XmlElement(name = "Reason_Third_i", required = true)
            protected BigInteger reasonThirdI;
            @XmlElement(name = "Outcome_ii")
            protected int outcomeIi;
            @XmlElement(name = "Reason_Primary_ii", required = true)
            protected BigInteger reasonPrimaryIi;
            @XmlElement(name = "Reason_Second_ii", required = true)
            protected BigInteger reasonSecondIi;
            @XmlElement(name = "Reason_Third_ii", required = true)
            protected BigInteger reasonThirdIi;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcomeI property.
             *
             */
            public int getOutcomeI() {
                return outcomeI;
            }

            /**
             * Sets the value of the outcomeI property.
             *
             */
            public void setOutcomeI(int value) {
                this.outcomeI = value;
            }

            /**
             * Gets the value of the reasonPrimaryI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimaryI() {
                return reasonPrimaryI;
            }

            /**
             * Sets the value of the reasonPrimaryI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimaryI(BigInteger value) {
                this.reasonPrimaryI = value;
            }

            /**
             * Gets the value of the reasonSecondI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecondI() {
                return reasonSecondI;
            }

            /**
             * Sets the value of the reasonSecondI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecondI(BigInteger value) {
                this.reasonSecondI = value;
            }

            /**
             * Gets the value of the reasonThirdI property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThirdI() {
                return reasonThirdI;
            }

            /**
             * Sets the value of the reasonThirdI property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThirdI(BigInteger value) {
                this.reasonThirdI = value;
            }

            /**
             * Gets the value of the outcomeIi property.
             *
             */
            public int getOutcomeIi() {
                return outcomeIi;
            }

            /**
             * Sets the value of the outcomeIi property.
             *
             */
            public void setOutcomeIi(int value) {
                this.outcomeIi = value;
            }

            /**
             * Gets the value of the reasonPrimaryIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimaryIi() {
                return reasonPrimaryIi;
            }

            /**
             * Sets the value of the reasonPrimaryIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimaryIi(BigInteger value) {
                this.reasonPrimaryIi = value;
            }

            /**
             * Gets the value of the reasonSecondIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecondIi() {
                return reasonSecondIi;
            }

            /**
             * Sets the value of the reasonSecondIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecondIi(BigInteger value) {
                this.reasonSecondIi = value;
            }

            /**
             * Gets the value of the reasonThirdIi property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThirdIi() {
                return reasonThirdIi;
            }

            /**
             * Sets the value of the reasonThirdIi property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThirdIi(BigInteger value) {
                this.reasonThirdIi = value;
            }

        }


        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Need" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Outcome" type="{https://supportingpeople.st-andrews.ac.uk}YesNoMissing"/>
         *         &lt;element name="Reason_Primary" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
         *         &lt;element name="Reason_Second" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
         *         &lt;element name="Reason_Third" type="{https://supportingpeople.st-andrews.ac.uk}Reason_2c"/>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
            "need",
            "outcome",
            "reasonPrimary",
            "reasonSecond",
            "reasonThird"
        })
        public static class Worklike2C
            implements Serializable
        {

            private final static long serialVersionUID = 100L;
            @XmlElement(name = "Need")
            protected int need;
            @XmlElement(name = "Outcome")
            protected int outcome;
            @XmlElement(name = "Reason_Primary", required = true)
            protected BigInteger reasonPrimary;
            @XmlElement(name = "Reason_Second", required = true)
            protected BigInteger reasonSecond;
            @XmlElement(name = "Reason_Third", required = true)
            protected BigInteger reasonThird;

            /**
             * Gets the value of the need property.
             *
             */
            public int getNeed() {
                return need;
            }

            /**
             * Sets the value of the need property.
             *
             */
            public void setNeed(int value) {
                this.need = value;
            }

            /**
             * Gets the value of the outcome property.
             *
             */
            public int getOutcome() {
                return outcome;
            }

            /**
             * Sets the value of the outcome property.
             *
             */
            public void setOutcome(int value) {
                this.outcome = value;
            }

            /**
             * Gets the value of the reasonPrimary property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonPrimary() {
                return reasonPrimary;
            }

            /**
             * Sets the value of the reasonPrimary property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonPrimary(BigInteger value) {
                this.reasonPrimary = value;
            }

            /**
             * Gets the value of the reasonSecond property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonSecond() {
                return reasonSecond;
            }

            /**
             * Sets the value of the reasonSecond property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonSecond(BigInteger value) {
                this.reasonSecond = value;
            }

            /**
             * Gets the value of the reasonThird property.
             *
             * @return
             *     possible object is
             *     {@link BigInteger }
             *
             */
            public BigInteger getReasonThird() {
                return reasonThird;
            }

            /**
             * Sets the value of the reasonThird property.
             *
             * @param value
             *     allowed object is
             *     {@link BigInteger }
             *
             */
            public void setReasonThird(BigInteger value) {
                this.reasonThird = value;
            }

        }

    }


    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="Forename" type="{https://supportingpeople.st-andrews.ac.uk}StringData50"/>
     *         &lt;element name="Surname" type="{https://supportingpeople.st-andrews.ac.uk}StringData50"/>
     *         &lt;element name="Telephone" type="{https://supportingpeople.st-andrews.ac.uk}StringData12"/>
     *         &lt;element name="Email" type="{https://supportingpeople.st-andrews.ac.uk}Email"/>
     *         &lt;element name="DateCompleted" type="{http://www.w3.org/2001/XMLSchema}date"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     *
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "forename",
        "surname",
        "telephone",
        "email",
        "dateCompleted"
    })
    public static class SubmissionDetails
        implements Serializable
    {

        private final static long serialVersionUID = 100L;
        @XmlElement(name = "Forename", required = true)
        protected String forename;
        @XmlElement(name = "Surname", required = true)
        protected String surname;
        @XmlElement(name = "Telephone", required = true)
        protected String telephone;
        @XmlElement(name = "Email", required = true)
        protected String email;
        @XmlElement(name = "DateCompleted", required = true)
        @XmlSchemaType(name = "date")
        protected XMLGregorianCalendar dateCompleted;

        /**
         * Gets the value of the forename property.
         *
         * @return
         *     possible object is
         *     {@link String }
         *
         */
        public String getForename() {
            return forename;
        }

        /**
         * Sets the value of the forename property.
         *
         * @param value
         *     allowed object is
         *     {@link String }
         *
         */
        public void setForename(String value) {
            this.forename = value;
        }

        /**
         * Gets the value of the surname property.
         *
         * @return
         *     possible object is
         *     {@link String }
         *
         */
        public String getSurname() {
            return surname;
        }

        /**
         * Sets the value of the surname property.
         *
         * @param value
         *     allowed object is
         *     {@link String }
         *
         */
        public void setSurname(String value) {
            this.surname = value;
        }

        /**
         * Gets the value of the telephone property.
         *
         * @return
         *     possible object is
         *     {@link String }
         *
         */
        public String getTelephone() {
            return telephone;
        }

        /**
         * Sets the value of the telephone property.
         *
         * @param value
         *     allowed object is
         *     {@link String }
         *
         */
        public void setTelephone(String value) {
            this.telephone = value;
        }

        /**
         * Gets the value of the email property.
         *
         * @return
         *     possible object is
         *     {@link String }
         *
         */
        public String getEmail() {
            return email;
        }

        /**
         * Sets the value of the email property.
         *
         * @param value
         *     allowed object is
         *     {@link String }
         *
         */
        public void setEmail(String value) {
            this.email = value;
        }

        /**
         * Gets the value of the dateCompleted property.
         *
         * @return
         *     possible object is
         *     {@link XMLGregorianCalendar }
         *
         */
        public XMLGregorianCalendar getDateCompleted() {
            return dateCompleted;
        }

        /**
         * Sets the value of the dateCompleted property.
         *
         * @param value
         *     allowed object is
         *     {@link XMLGregorianCalendar }
         *
         */
        public void setDateCompleted(XMLGregorianCalendar value) {
            this.dateCompleted = value;
        }

    }

}
