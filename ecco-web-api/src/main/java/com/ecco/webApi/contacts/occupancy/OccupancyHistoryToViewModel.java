package com.ecco.webApi.contacts.occupancy;

import com.ecco.dom.contacts.AddressHistory;
import com.ecco.webApi.contacts.address.BaseHistoryToViewModel;

public final class OccupancyHistoryToViewModel extends BaseHistoryToViewModel<OccupancyHistoryViewModel> {

    public OccupancyHistoryToViewModel() {
        super(OccupancyHistoryViewModel::new);
    }

    @Override
    protected void customise(AddressHistory input, OccupancyHistoryViewModel result) {
    }

}
