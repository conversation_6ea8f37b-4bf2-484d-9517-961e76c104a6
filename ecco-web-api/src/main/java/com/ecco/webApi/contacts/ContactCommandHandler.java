package com.ecco.webApi.contacts;

import com.ecco.dom.commands.QContactCommand;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.DraftableCommandHandler;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.Authentication;

import com.ecco.dao.commands.ContactsCommandRepository;
import com.ecco.dom.Agency;
import com.ecco.dom.ContactImpl;
import com.ecco.dom.Individual;
import com.ecco.dom.commands.ContactCommand;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.Serializable;
import java.time.LocalDate;

public class ContactCommandHandler
    extends BaseCommandHandler<ContactCommandViewModel, Integer, ContactCommand, Integer>
    implements DraftableCommandHandler<ContactCommandViewModel, Integer, ContactCommand, Integer> { //params is just contactId

    private final ContactRepository contactRepository;
    private final ContactsCommandRepository contactsCommandRepository;

    public ContactCommandHandler(ObjectMapper objectMapper, ContactsCommandRepository commandRepository,
            Class<ContactCommandViewModel> vmClass, ContactRepository contactRepository) {
        super(objectMapper, commandRepository, vmClass);
        this.contactRepository = contactRepository;
        this.contactsCommandRepository = commandRepository;
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, Integer params, @NotNull ContactCommandViewModel viewModel) {
        ContactImpl contact = contactRepository.findById((long) viewModel.contactId).orElseThrow(NullPointerException::new);

        if (contact instanceof Agency) {
            handleAgencyFields(viewModel, (Agency) contact);
        }
        else if (contact instanceof Individual) {
            handleIndividualFields(viewModel, (Individual) contact);
        }

        if (viewModel.mobileNumber != null) {
            contact.setMobileNumber(viewModel.mobileNumber.to);
        }
        if (viewModel.phoneNumber != null) {
            contact.setPhoneNumber(viewModel.phoneNumber.to);
        }
        if (viewModel.archived != null) {
            LocalDate archiveNow = viewModel.archived.to == null
                    ? null
                    : LocalDate.parse(viewModel.archived.to);
            contact.setArchived(archiveNow);
        }
        contactRepository.save(contact);
        return null;
    }

    private void handleAgencyFields(ContactCommandViewModel viewModel, Agency agency) {
        if (viewModel.companyName != null) {
            agency.setCompanyName(viewModel.companyName.to);
        }
    }

    private void handleIndividualFields(ContactCommandViewModel viewModel, Individual individual) {
        if (viewModel.firstName != null) {
            // TODO: make this more gen purpose           warnIfPrevValueDoesntMatch(referralId, actionDefId, command, prevValue, field);
            individual.setFirstName(viewModel.firstName.to);
        }
        if (viewModel.lastName != null) {
            individual.setLastName(viewModel.lastName.to);
        }
        if (viewModel.jobTitle != null) {
            individual.setJobTitle(viewModel.jobTitle.to);
        }
        if (viewModel.title != null) {
            individual.setTitle(viewModel.title.to);
        }
    }

    @Override
    protected ContactCommand createCommand(Serializable targetId, Integer params, String requestBody, ContactCommandViewModel viewModel,
                                           long userId) {
        return new ContactCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params);
    }

    @Override
    public void clearDrafts(Authentication authentication, Integer integer, String requestBody, ContactCommandViewModel viewModel, ContactCommand command, long userId) {
        // NB could use this.commandRepository if we had more generics in all the handlers - when we have more draft handlers, perhaps
        // we delete this user's entity
        // index "DEV-2694-cont_commands-index-draft"
        QContactCommand cmd = QContactCommand.contactCommand;
        var thisUsersDrafts = contactsCommandRepository.findAll(cmd.draft.isTrue().and(cmd.userId.eq(userId).and(cmd.contactId.eq(integer))));
        contactsCommandRepository.deleteAllInBatch(thisUsersDrafts);
    }
}
