package com.ecco.webApi.featureConfig;

/**
 * Exposes a subset of Spring messages
 */
public interface Messages {
    // The message keys to export are listed here explicitly since there is no
    // way to iterate over all Spring messages.
    String[] EXPOSED_MESSAGE_KEYS = {
            "invalidNewPassword_ONE_DIGIT_ONE_UPPER",
            "invalidNewPassword_ONE_DIGIT_ONE_UPPER_ONE_SPECIAL_MIN_8",
            "invalidNewPassword_SIMPLE",
            "invalidNewPassword_COMPLEX",
            "invalidNewPassword_PREVIOUS",
            "status.forAssessment",
            "status.pending",
            "status.started",
            "status.toStart",
            "status.signposted",
            "status.incomplete",
            "status.exited",
            "status.hidden",
            "referralBreadcrumb.region",
            "referralBreadcrumb.project",
            "referralBreadcrumb.clientWithContact",
            "referralBreadcrumb.staffDetail",
            "referralBreadcrumb.relationshipToPrimary",
            "referralBreadcrumb.dataProtection",
            "referralBreadcrumb.consent",
            "referralBreadcrumb.source",
            "referralBreadcrumb.referralDetails",
            "referralBreadcrumb.referralView",

            "referralView.allocateWorker",
            "referralView.staffDetail",
            "referralView.staffLocation",
            "referralView.clientWithContact",
            "referralView.referralAccepted",
            "referralView.decideFinal",
            "referralView.assessmentAccepted",
            "referralView.sourceWithIndividual",
            "referralView.scheduleReviews",
            "referralView.assessmentDate",
            "referralView.start",
            "referralView.startAccommodation",
            "referralView.referralDetails",
            "referralView.funding",
            "referralView.pendingStatus",
            "referralView.waitingListCriteria",
            "referralView.close",
            "referralView.assessmentDetails",
            "referralView.project",
            "referralView.projectRegion",
            "referralView.projectAsAccommodation",
            "referralView.dataProtection",
            "referralView.consent",
            "referralView.newMultipleReferral",
            "referralView.emergencyDetails",
            "referralView.iaptInitialAssessment",
            "referralView.iaptAttendance",
            "referralView.iaptCurrentView",
            "referralView.iaptSessions",
            "referralView.iaptGoals",
            "referralView.iaptImpact",
            "referralView.iaptFeedback",
            "referralView.nextMeeting",
            "referralView.agreementOfAppointments",
            "referralView.scheduleMeetings",
            "referralView.allocateToServices",
            "referralView.initial-sp_data",
            "referralView.exit-sp_data",
            "referralView.engagementComments",
            "referralView.feedbackQuestionnaire",
            "referralView.groupActivities",
            "referralView.generalQuestionnaire",
            "referralView.hactQuestionnaire",
            "referralView.managerNotes",
            "referralView.needsAssessment",
            "referralView.needsReduction",
            "referralView.needsReductionSP",
            "referralView.needsAssessmentReduction",
            "referralView.needsAssessmentReductionReview",
            "referralView.rotaVisit",
            "referralView.supportStaffNotes",
            "referralView.threatAssessment",
            "referralView.threatReduction",
            "referralView.threatAssessmentReduction",
            "referralView.dailyRoutines",
            "referralView.dietaryIntake",
            //    "referralView.customFormMgr": string;
            //    "workerJobView.jobDetails": string;
            "referralView.tabs.overview",
            "referralView.tabs.risk",
            "referralView.tabs.support",
            "referralView.tabs.supportHistory",
            "referralView.tabs.visitHistory",
            "referralView.tabs.checklistHistory",
            "referralView.tabs.riskHistory",
            "referralView.tabs.formHistory",
            "referralView.tabs.forwardPlan",
            "referralView.tabs.forwardRiskPlan",
            "referralView.tabs.appointments",
            "referralView.tabs.relationshipStar",
            "referralView.tabs.services",
            "listDef.nullText.riskMatrixSeverity",
            "listDef.nullText.riskMatrixLikelihood",
            "reportEntity.Referral",
            "reportEntity.ReferralFull",
            "reportEntity.ReferralSummary",
            "reportEntity.SupportWork",
            "reportEntity.RiskWork",
            "reportEntity.SupportRiskWork",
            "reportEntity.RiskFlags",
            "reportEntity.SupportFlags",
            "reportEntity.RiskRatings",
            "reportEntity.ActivityAttendance",
            "reportEntity.Questionnaire",
            "reportEntity.QuestionnaireSnapshot",
            "reportEntity.QuestionnaireMultiSnapshot",
            "reportEntity.QuestionnaireSingleSnapshot",
            "reportEntity.RotaAgreement",
            "reportEntity.RotaDemand",
            "reportEntity.RotaSchedule",
            "reportEntity.EventResource",
            "reportEntity.SmartStepSingleSnapshot",
            "reportEntity.SmartStepMultiSnapshot",
            "reportEntity.ServiceRecipient",
            "reportEntity.ServiceRecipientCommand",
            "reportEntity.GroupedWorkAnalysis",
            "reportEntity.ReferralsByMonth",
            "reportEntity.ReferralsByService",
            "reportEntity.ReferralsBySource",
            "reportEntity.ReferralsByEthnicity",
            "reportEntity.ReferralsBySexualOrientation",
            "reportEntity.ReferralsByDisability",
            "reportEntity.TasksByMonth",
            "reportEntity.AnswersByQuestion",
            "reportEntity.Client",
            "reportEntity.Agency",
            "reportEntity.Professional",
            "reportEntity.TaskStatus",
            "reportEntity.User",
            "reportEntity.Review",
            "reportEntity.ServiceType",
            "reportEntity.AddressHistory",
            "reportEntity.OccupyHistory",
            "reportEntity.FinanceCharge",
            "reportEntity.FinanceReceipt",
            "reportEntity.SupportWorkSnapshot",
            "reportEntity.RiskWorkSnapshot",
            "reportEntity.Building",
            "reportEntity.Repair",
            "reportEntity.AssociatedContact",
            "reportEntity.CustomFormSnapshot",

            "rota.demandType.bookableResource.displayName",
            "rota.demandType.carerun.displayName",
            "rota.demandType.worker.displayName",

            "form.funding.reviewDate",
            "form.funding.paymentReference",
            "form.assessmentDate.firstOfferedInterviewDate",
            "form.agreements.signature",
            "form.emergencyDetails.description",
            "form.emergencyDetails.communicationNeeds",
            "form.emergencyDetails.communicationKeyword",
            "form.emergencyDetails.emergencyDetails",
            "form.emergencyDetails.medicationDetails",
            "form.emergencyDetails.doctorsDetails",
            "form.emergencyDetails.dentistDetails",
            "form.emergencyDetails.risksAndConcerns",
            "form.evidenceComment.location",
            "form.evidenceComment.meetingStatus",
            "form.evidenceComment.clientStatus",

            "terminology.smartstep",
            "project",
            "projects",
            "signpost",
            "signpostBack",
            "signpostComment",

            "goalName.label",
            "needsAssessment.goalName.label",
            "needsReduction.goalName.label",
            "needsAssessmentReduction.goalName.label",
            "needsAssessmentReductionReview.goalName.label",
            "threatAssessment.goalName.label",
            "threatAssessmentReduction.goalName.label",
            "threatReduction.goalName.label",
            "goalPlan.label",
            "needsAssessment.goalPlan.label",
            "needsReduction.goalPlan.label",
            "needsAssessmentReduction.goalPlan.label",
            "needsAssessmentReductionReview.goalPlan.label",
            "threatAssessment.goalPlan.label",
            "threatAssessmentReduction.goalPlan.label",
            "threatReduction.goalPlan.label",

            "threat.triggerControl.hazard.label",
            "threat.triggerControl.intervention.label",
            "threat.goal.new.label",
            "threat.subGoal.new.label",
            "support.goal.new.label",
            "support.subGoal.new.label"
    };
}
