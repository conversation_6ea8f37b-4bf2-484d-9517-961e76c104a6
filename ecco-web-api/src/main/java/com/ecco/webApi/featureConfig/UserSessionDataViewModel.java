package com.ecco.webApi.featureConfig;

import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import com.ecco.webApi.viewModels.ServicesViewModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
public class UserSessionDataViewModel implements UserSessionData {

    /** e.g. fnightingale */
    private String username;

    private Long userId;

    /** CalendarId for this user so we can request nearby appointments */
    private String calendarId;

    private String calendarIdUserReferenceUri;

    private IndividualUserSummaryViewModel individualUserSummary;

    /** e.g. ROLE_STAFF */
    private String[] roles;
    private Set<String> groups;

    ///** The users restricted ServicesProjects */
    //private ServicesViewModel restrictedServicesProjects;

    /** The users restricted ServicesProjects but this excludes null projects */
    private Iterable<ServiceCategorisationViewModel> restrictedServiceCategorisations;
}
