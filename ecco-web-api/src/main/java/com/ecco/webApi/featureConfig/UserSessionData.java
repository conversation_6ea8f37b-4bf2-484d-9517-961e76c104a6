package com.ecco.webApi.featureConfig;

import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import com.ecco.webApi.viewModels.ServicesViewModel;

import java.util.Set;

public interface UserSessionData {
    String[] getRoles();

    void setRoles(String[] roles);

    Set<String> getGroups();
    void setGroups(Set<String> groups);

    Long getUserId();

    void setUserId(Long userId);

    String getUsername();

    void setUsername(String username);

    String getCalendarId();

    void setCalendarId(String calendarId);

    IndividualUserSummaryViewModel getIndividualUserSummary();

    void setIndividualUserSummary(IndividualUserSummaryViewModel ius);

    /**
     * Unique reference to the native object of the user (calendarId).
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     * @return value as set by {@link com.ecco.infrastructure.hibernate.EntityUriMapper}
     */
    String getCalendarIdUserReferenceUri();

    void setCalendarIdUserReferenceUri(String calendarIdUserRef);

    Iterable<ServiceCategorisationViewModel> getRestrictedServiceCategorisations();

    void setRestrictedServiceCategorisations(Iterable<ServiceCategorisationViewModel> restrictedServiceCategorisations);
}
