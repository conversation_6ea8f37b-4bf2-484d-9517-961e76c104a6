package com.ecco.webApi.featureConfig;

import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import com.ecco.webApi.contacts.IndividualUserSummaryViewModel;
import com.ecco.webApi.viewModels.ServicesViewModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(NON_NULL)
public class SessionDataViewModel extends GlobalConfigViewModel implements UserSessionData {

    /** e.g. fnightingale */
    private String username;

    private Long userId;

    /** CalendarId for this user so we can request nearby appointments */
    private String calendarId;


    /**
     * Unique reference to the native object of the user (calendarId).
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     * @return value as set by {@link com.ecco.infrastructure.hibernate.EntityUriMapper}
     */
    private String calendarIdUserReferenceUri;

    private IndividualUserSummaryViewModel individualUserSummary;

    /** e.g. ROLE_STAFF */
    private String[] roles;
    private Set<String> groups;

    private Iterable<ServiceCategorisationViewModel> restrictedServiceCategorisations;

}
