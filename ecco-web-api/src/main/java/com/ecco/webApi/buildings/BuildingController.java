package com.ecco.webApi.buildings;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.dom.QFixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.infrastructure.config.root.CacheConfig;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.support.EtaggedResponseCacheManager;
import com.ecco.webApi.viewModels.Result;
import com.querydsl.core.BooleanBuilder;
import lombok.AllArgsConstructor;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.StreamSupport;

import static com.ecco.config.dom.ListDefinitionEntry.BUILDING_RESOURCETYPE_ID;
import static com.ecco.rota.service.BuildingCareRunRotaHandler.CARERUN_RESOURCE_ID;
import static java.util.stream.Collectors.toList;

@RestController
@PreAuthorize("hasRole('ROLE_STAFF')")
@AllArgsConstructor
public class BuildingController extends BaseWebApiController {

    static private Predicate<FixedContainer> isNotCareRun = it -> !it.getResourceTypeId().equals(CARERUN_RESOURCE_ID);

    private final FixedContainerRepository fixedContainerRepository;

    private final FixedContainerToViewModel fixedContainerToViewModel;

    @Nonnull
    private final BuildingCommandHandler buildingCommandHandler;

    @Nonnull
    private final AddressLocationChangeCommandHandler addressCommandHandler;

    private final EtaggedResponseCacheManager etaggedResponseCacheManager;

    // get the id - with hateos the whole URL is the id
    // but for the purposes of retrofitting, getting the id like this is acceptable
    // NB see https://github.com/spring-projects/spring-hateoas/issues/66
    // NB and http://tommyziegler.com/how-to-expose-the-resourceid-with-spring-data-rest/
    public static Pattern extractIdPattern = Pattern.compile("buildings/(\\d+)/");
    public static final Function<String, Integer> EXTRACT_ID_FN = (href) -> {
        Matcher matches = extractIdPattern.matcher(href);
        matches.find();
        String id = matches.group(1);
        return Integer.parseInt(id);
    };



    @GetJson("/buildings/{id}/")
    public FixedContainerViewModel findOne(@PathVariable int id) {
        return fixedContainerRepository.findById(id).map(fixedContainerToViewModel).orElse(null);
    }

    @GetJson("/buildings/byServiceRecipient/{serviceRecipientId}/")
    public FixedContainerViewModel findOneBySrId(@PathVariable int serviceRecipientId) {
        return fixedContainerRepository.findByServiceRecipient_Id(serviceRecipientId).map(fixedContainerToViewModel).orElse(null);
    }

    @GetJson("/buildings/byExternalRef/{ref}/")
    public FixedContainerViewModel findOneByRef(@PathVariable String ref) {
        return fixedContainerRepository.findByExternalRef(ref).map(fixedContainerToViewModel).orElse(null);
    }

    @Secured("ROLE_ADMINBUILDING") // TODO: Provide workflow for removal?
    @DeleteMapping("/buildings/{id}/")
    public Result deleteOne(@PathVariable int id) {
        fixedContainerRepository.deleteById(id);
        return new Result("deleted");
    }

    /**
     * @param resourceType defaults to BUILDING resource type when omitted
     * @param showChildren defaults to true.  Include rooms/runs etc at this location
     * @param addressLocationId if specified, finds all at this location (e.g. rooms within a care home)
     */
    @GetJson("/buildings/")
    public void findFixedContainers(
            WebRequest request,
            HttpServletResponse response,
            @RequestParam(required=false) String resourceType,
            @RequestParam(required=false, defaultValue = "true") Boolean showChildren,
            @RequestParam(required=false, defaultValue = "false") Boolean showDisabled,
            @RequestParam(required=false) Integer addressLocationId) throws IOException {

        var key = resourceType == null ? "all" : resourceType;
        key = key + (showChildren ? "withChildren" : "noChildren");
        key = key + (showDisabled ? "withDisabled" : "");

        var resourceTypes = resourceType == null ? null : new String[]{resourceType};
        etaggedResponseCacheManager.getFromCacheWithEtagHandling(request, response,
                CacheConfig.CACHE_BUILDINGS, key, 300,
                () -> StreamSupport.stream(BuildingController.getFixedContainers(fixedContainerRepository, showChildren, showDisabled, resourceTypes, addressLocationId != null ? Collections.singletonList(addressLocationId) : null, null).spliterator(), false)
                        .map(fixedContainerToViewModel)
                        .collect(toList()));
    }

    @GetJson("/buildings/byIds/")
    public Iterable<FixedContainerViewModel> findAllByIds(@RequestParam(name = "ids") List<Integer> buildingIds) {
        return fixedContainerRepository.findAllByIdIn(buildingIds).stream().map(fixedContainerToViewModel).toList();
    }

    @GetJson("/buildings/byLocationIds/")
    public Iterable<FixedContainerViewModel> findByLocations(
            @RequestParam(required=false) String resourceType,
            @RequestParam(required=false, defaultValue = "true") Boolean showChildren,
            @RequestParam(required=false, defaultValue = "false") Boolean showDisabled,
            @RequestParam List<Integer> ids) {

        var resourceTypes = resourceType == null ? null : new String[]{resourceType};
        return StreamSupport.stream(getFixedContainers(fixedContainerRepository, showChildren, showDisabled, resourceTypes, ids, null).spliterator(), false)
                .map(fixedContainerToViewModel)
                .collect(toList());
    }

    // UNUSED
    @GetJson("/buildings/byLocation/{addressLocationId}/")
    public Iterable<FixedContainerViewModel> findByLocation(
            @RequestParam(required=false) String resourceType,
            @RequestParam(required=false, defaultValue = "true") Boolean showChildren,
            @RequestParam(required=false, defaultValue = "false") Boolean showDisabled,
            @PathVariable int addressLocationId) {

        var resourceTypes = resourceType == null ? null : new String[]{resourceType};
        return StreamSupport.stream(getFixedContainersHierarchical(fixedContainerRepository, showChildren, showDisabled, resourceTypes, Collections.singletonList(addressLocationId)).spliterator(), false)
                .map(fixedContainerToViewModel)
                .collect(toList());
    }

    /**
     * Finds the building and its children 'building' or 'room' hierarchically
     * NB we did load all buildings/rooms and then sort out
     * but we often just need the hierarchy inside one parent
     * and it seems more performant to do a few queries than to load all buildings
     * NB this is from Addresses (1) work.
     */
    public static Iterable<FixedContainer> getFixedContainersHierarchical(FixedContainerRepository fixedContainerRepository, int buildingId) {
        // find the starting node
        FixedContainer startNode = fixedContainerRepository.findById(buildingId).orElse(null);
        if (startNode == null) {
            return new ArrayList<>(); // return empty if starting node is not found
        }

        // build the hierarchical structure from the starting node
        List<FixedContainer> rootNodes = new ArrayList<>();
        rootNodes.add(startNode);
        BuildingController.buildHierarchy(startNode, fixedContainerRepository, rootNodes);
        rootNodes.add(startNode);

        return rootNodes;
    }
    public static Iterable<FixedContainer> getFixedContainersHierarchical(FixedContainerRepository fixedContainerRepository, boolean showChildren, boolean showDisabled, String[] resourceTypes, List<Integer> addressLocationIds) {
        var bldgsAtLocation = getFixedContainers(fixedContainerRepository, showChildren, showDisabled, resourceTypes, addressLocationIds, null);
        List<FixedContainer> results = new ArrayList<>();
        bldgsAtLocation.forEach(b -> {
            results.add(b);
            buildHierarchy(b, fixedContainerRepository, results);
        });
        return results;
    }
    private static void buildHierarchy(FixedContainer node, FixedContainerRepository fixedContainerRepository, List<FixedContainer> result) {
        // find all buildings inside
        // ListDefinitionEntry.BUILDING_RESOURCETYPE_ID

        var resourceTypeNames = new String[]{"building", "room"};
        var allBldgs = getFixedContainers(fixedContainerRepository, true, false, resourceTypeNames, null, node.getId());

        for (FixedContainer child : allBldgs) {
            if (child.getParent() != null && child.getParent().getId().equals(node.getId())) {
                result.add(child);
                buildHierarchy(child, fixedContainerRepository, result);
            }
        }
    }
    private static Iterable<FixedContainer> getFixedContainers(FixedContainerRepository fixedContainerRepository, boolean showChildren, boolean showDisabled, String[] resourceTypes, List<Integer> addressLocationIds, Integer parentId) {
        var p = new BooleanBuilder();
        var bldg = QFixedContainer.fixedContainer;
        if (parentId != null) {
            p.and(bldg.parentId.eq(parentId));
        }
        if (addressLocationIds != null && !addressLocationIds.isEmpty()) {
            p.and(bldg.locationId.in(addressLocationIds));
        }
        if (showChildren) {
            if (resourceTypes != null) {
                p.and(bldg.resourceType.name.in(resourceTypes));
            } else {
                // if we're not specific, just don't load careruns
                // not just BUILDING_RESOURCETYPE_ID which excludes customer-created buildings since this option isn't available
                p.and(bldg.resourceTypeId.ne(CARERUN_RESOURCE_ID));
            }
        } else {
            p.and(bldg.parent.isNull());
            if (resourceTypes != null) {
                p.and(bldg.resourceType.name.in(resourceTypes));
            } else {
                // if we're not specific, just don't load careruns
                // not just BUILDING_RESOURCETYPE_ID which excludes customer-created buildings since this option isn't available
                p.and(bldg.resourceTypeId.ne(CARERUN_RESOURCE_ID));
            }
        }

        if (!showDisabled) {
            p.and(bldg.disabled.isFalse());
        }
        return fixedContainerRepository.findAll(p);
    }

    @GetJson("/buildings/{id}/children/")
    public Iterable<FixedContainerViewModel> findChildren(@PathVariable int id) {
        return fixedContainerRepository.findAllByParentId(id)
                .stream()
                .map(fixedContainerToViewModel)
                .collect(toList());
    }

    @GetJson("/buildings/{id}/careruns/")
    public Iterable<FixedContainerViewModel> findShifts(@PathVariable int id) {
        // show all careruns - whether disabled or not
        return fixedContainerRepository.findAllByParentIdAndResourceTypeId(id, CARERUN_RESOURCE_ID)
                .stream()
                .map(fixedContainerToViewModel)
                .collect(toList());
    }

    @PostJson("/buildings/commands/")
    public Result addBuilding(
            @Nonnull Authentication authentication,
            @Nonnull @RequestBody String requestBody) throws IOException {

        return buildingCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @PostJson("/buildings/address-location/commands/")
    public Result buildingAddress(
            @Nonnull Authentication authentication,
            @Nonnull @RequestBody String requestBody) throws IOException {

        return addressCommandHandler.handleCommand(authentication, null, requestBody);
    }

}
