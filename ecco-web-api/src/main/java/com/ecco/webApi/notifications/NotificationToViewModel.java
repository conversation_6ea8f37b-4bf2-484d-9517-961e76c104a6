package com.ecco.webApi.notifications;

import com.ecco.dom.notifications.NotificationRecipient;
import com.google.common.base.Function;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;

/**
 * Converts a NotificationRecipient entity to a NotificationViewModel
 */
@Component
public class NotificationToViewModel implements Function<NotificationRecipient, NotificationViewModel> {

    @Override
    public NotificationViewModel apply(@Nullable NotificationRecipient notification) {
        if (notification == null) {
            throw new NullPointerException("Notification must not be null");
        }

        NotificationViewModel viewModel = new NotificationViewModel();

        // Set basic properties
        viewModel.setId(notification.getCommandUuid()); // Using commandUuid as the ID
        viewModel.setUserId(notification.getUserId());
        viewModel.setCreated(notification.getCreated());
        viewModel.setReadAt(notification.getReadAt());
        viewModel.setCommandUuid(notification.getCommandUuid());

        // Try to get additional information from the command
        //enrichFromCommand(viewModel, notification.getCommandUuid());

        return viewModel;
    }

    /**
     * Enriches the view model with information from the associated command
     */
    /*private void enrichFromCommand(NotificationViewModel viewModel, UUID commandUuid) {
        try {
            BaseCommand<?> command = commandRepository.findOneByUuid(commandUuid);
            if (command == null) {
                return;
            }

            // Set notification type based on command type
            if (command instanceof ContactMessageCommand) {
                viewModel.setType("SMS");
                viewModel.setContent("SMS message received");
                viewModel.setLink("/contacts/" + ((ContactMessageCommand) command).getContactId() + "/messages/");
            } else {
                viewModel.setType("SYSTEM");
                viewModel.setContent("System notification");
            }

            // Additional command-specific enrichment could be added here

        } catch (Exception e) {
            // Log error but continue - we don't want to fail the entire conversion
            // just because we couldn't enrich with command data
        }
    }*/
}
