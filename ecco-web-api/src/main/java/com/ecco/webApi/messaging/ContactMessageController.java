package com.ecco.webApi.messaging;

import com.ecco.dom.ContactImpl;
import com.ecco.messaging.EsendexInbound;
import com.ecco.messaging.MessageResult;
import com.ecco.messaging.MessageService;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.webApi.contacts.ContactCommandParams;
import com.ecco.webApi.controllers.NotFoundException;
import com.ecco.webApi.viewModels.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ContactMessageController {

    private final ContactMessageCommandHandler handler;
    private final MessageService messageService;
    private final ContactRepository contactRepository;

    @Value("#{environment.encrypted}")
    private String encrypted;

    @PostJson("/contacts/{contactId}/message/commands/")
    public Result sendSms(
            @Nonnull Authentication authentication,
            @Nonnull ContactCommandParams params,
            @Nonnull @RequestBody String requestBody) throws IOException {
        return handler.handleCommand(authentication, params, requestBody);
    }

    // eg https://.../api/contacts/sms/********-0000-babe-babe-dadafee1600d
    /** Twilio-specific webhook that returns a {@link com.twilio.twiml.MessagingResponse} with the reply to send back */
    @RequestMapping(value = {"/contacts/sms/{webhookUuid}", "/contacts/sms/twilio/{webhookUuid}"}, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String receiveSms(
            @PathVariable UUID webhookUuid,
            @RequestParam("Body") String body,
            @RequestParam("AccountSid") String accountSid,
            @RequestParam("To") String to,
            @RequestParam("From") String from
    ) {
        return validateAndRespond(body, accountSid, to, from);
    }

    /**
     * esendex-specific webhook that returns the reply to send back
     * see https://developers.esendex.com/quickstarts/receive-sms-push/
     */
    @RequestMapping(path = "/contacts/sms/esendex/{webhookUuid}", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public void receiveSms(@RequestBody EsendexInbound inbound) {
        validateAndRespond(inbound.MessageText, inbound.AccountId.toString(), inbound.To, inbound.From);
    }

    private String validateAndRespond(String body, String accountSid, String to, String from) {
        log.info("Received inbound SMS: to {}, from {}: {} ", to, from, body);
        if (Boolean.valueOf(encrypted).equals(Boolean.TRUE)) {
            log.error("Cannot handle received SMS because mobilenumber is an encypted field");
            return MessageResult.failed(from, "System config error - cannot accept SMS").getMessage();
        }
        var contacts = findContacts(from);
        if (contacts.isEmpty()) {
            log.warn("No contact found for mobile no ending {}", from);
            throw new NotFoundException("Number not found");
        }

        contacts.forEach(c -> handler.saveInboundSms(c.getId().intValue(), body));
        return messageService.respondSms(accountSid, body);
    }

    private List<ContactImpl> findContacts(String fromMobileNo) {
        String matchText = fromMobileNo.startsWith("0") ? fromMobileNo.substring(1)
                : fromMobileNo.startsWith("+44") ? fromMobileNo.substring(3)
                : fromMobileNo;
        return contactRepository.findAllByMobileNumberEndsWith(matchText);
    }
}
