package com.ecco.webApi.messaging;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;

/**
 * View model, to capture a new/update/delete request to a calendar entry.
 */
@Getter
@Setter
@Slf4j
@NoArgsConstructor // only for Cglib/Hibernate etc
public class ContactMessageCommandDto extends BaseCommandViewModel {

    /** recipient if !inbound, sender if inbound */
    @Nonnull
    public Integer contactId;

    @Nonnull
    public String messageBody;

    public boolean inbound = false;

    /**
     * Constructor for system generated (non-user) messages - ie sms notification system, inbound sms, and tests.
     * <PERSON><PERSON> <PERSON> creates the full object from JSON - this omits things that a
     * proper command handler will require, such as initialising the UUID etc via super(uuid,...) */
    public ContactMessageCommandDto(boolean inbound, int contactId, @Nonnull String messageBody) {
        super(UriComponentsBuilder
                .fromUriString("contact/{contactId}/message/command/")
                .buildAndExpand(contactId)
                .toUriString());
        this.inbound = inbound;
        this.contactId = contactId;
        this.messageBody = messageBody;
    }

    public boolean hasChanges() {
        return true;
    }

    public boolean valid() {
        return super.valid();
    }

}
