package com.ecco.webApi.messaging;

import com.ecco.dao.commands.ContactsCommandRepository;
import com.ecco.dom.commands.ContactMessageCommand;
import com.ecco.messaging.MessageService;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.contacts.ContactBaseCommandHandler;
import com.ecco.webApi.contacts.ContactCommandParams;
import com.ecco.webApi.controllers.NotFoundException;
import com.ecco.webApi.users.UserBaseController;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.io.Serializable;

@Component
public class ContactMessageCommandHandler extends ContactBaseCommandHandler<
        ContactMessageCommandDto, ContactMessageCommand, ContactCommandParams> {

    private static final Logger log = LoggerFactory.getLogger(ContactMessageCommandHandler.class);

    private final MessageService sender;
    private final ContactRepository contactRepository;

    @Autowired
    public ContactMessageCommandHandler(@Nonnull ObjectMapper objectMapper,
                                        @Nonnull MessageService sender,
                                        @Nonnull ContactsCommandRepository contactsCommandRepository,
                                        @Nonnull ContactRepository contactRepository) {
        super(objectMapper, contactsCommandRepository, ContactMessageCommandDto.class);
        this.sender = sender;
        this.contactRepository = contactRepository;
    }

    @Override
    protected CommandResult handleInternal(@Nonnull Authentication auth,
                                           @Nonnull ContactCommandParams params,
                                           @Nonnull ContactMessageCommandDto viewModel) {

        if (viewModel.inbound) {
            // viewModel.contactId - is the inbound/from contact
            // we don't need to do anything, since the command is all we need to save for inbound sms
            return null;
        } else {
            var contact = contactRepository.findById(params.contactId).orElseThrow(() -> new NotFoundException("Not found"));
            var msg = sender.sendSms(contact.getMobileNumber(), viewModel.messageBody);
            // TODO ? apply NotFoundException(msg.message);
            // TODO check for what we did response, eg "SMS submitted" from TwilioMessageService
            return new CommandResult().withMessage(msg.isSuccessful() ? "message sent" : msg.getMessage());
        }
    }

    @Override
    @Nonnull
    protected ContactMessageCommand createCommand(Serializable targetId,
                                                  @Nonnull ContactCommandParams params,
                                                  @Nonnull String requestBody,
                                                  ContactMessageCommandDto viewModel,
                                                  long userId) {
        Assert.state(params.contactId == viewModel.contactId, "contactId in body must match URI");

        return new ContactMessageCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.contactId);
    }

    @SneakyThrows
    public void saveInboundSms(int contactIdFrom, String message) {
        // process the command using the handler mechanism, so we get to create an event etc
        var dto = new ContactMessageCommandDto(true, contactIdFrom, message);
        this.handleCommand(UserBaseController.EXTERNAL_USERID, new ContactCommandParams(contactIdFrom), dto);
    }
}
