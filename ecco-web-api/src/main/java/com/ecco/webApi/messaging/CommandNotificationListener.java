package com.ecco.webApi.messaging;

import com.ecco.dao.ClientRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dom.commands.ContactMessageCommand;
import com.ecco.dom.notifications.NotificationRecipient;
import com.ecco.repositories.notifications.NotificationRepository;
import com.ecco.security.repositories.UserRepository;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.webApi.evidence.CommandCreatedEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.Instant;
import java.util.*;

/**
 * Listens for CommandCreatedEvent and creates notifications for appropriate users.
 * This is inline with CommandSMSNotificationAgent and CommandEmailNotificationAgent,
 * which could be combined at some point.
 */
@Component
public class CommandNotificationListener {

    private static final Logger log = LoggerFactory.getLogger(CommandNotificationListener.class);

    private final NotificationRepository notificationRepository;
    private final UserRepository userRepository;
    private final ClientRepository clientRepository;
    private final ReferralRepository referralRepository;

    @Autowired
    public CommandNotificationListener(
            NotificationRepository notificationRepository,
            UserRepository userRepository,
            ClientRepository clientRepository,
            ReferralRepository referralRepository) {
        this.notificationRepository = notificationRepository;
        this.userRepository = userRepository;
        this.clientRepository = clientRepository;
        this.referralRepository = referralRepository;
    }

    /**
     * Handles CommandCreatedEvent to create notifications
     * This is triggered after the transaction that created the command is committed
     */
    @TransactionalEventListener
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void notificationsForEvent(CommandCreatedEvent event) {
        try {

            // INBOUND SMS
            // determine the type of command and create appropriate notifications
            if (event.getCommand() instanceof ContactMessageCommand) {
                var vm = (ContactMessageCommandDto) event.getViewModel();
                if (vm.inbound) {
                    handleInboundContactMessageCommand((ContactMessageCommand) event.getCommand(), vm);
                }
            }

        } catch (Exception e) {
            // Log error but don't rethrow - we don't want notification failures to affect the main transaction
            log.error("Error creating notifications for command: {}", event.getCommand().getUuid(), e);
        }
    }

    /**
     * Handles notifications for inbound ContactMessageCommand (SMS messages)
     */
    private void handleInboundContactMessageCommand(ContactMessageCommand command, ContactMessageCommandDto viewModel) {

        // get key worker - assuming a client/referral's supportWorkerId BUT we should use ServiceRecipientContact
        var fromContactId = (int) command.getContactId();
        Set<Long> userIds = determineRecipientsForInboundSms(fromContactId);

        if (userIds.isEmpty()) {
            return;
        }

        // create a notification recipient entry for each interested user
        var now = Instant.now();
        UUID commandUuid = command.getUuid();

        for (Long userId : userIds) {
            NotificationRecipient notification = new NotificationRecipient();
            notification.setCommandUuid(commandUuid);
            notification.setUserId(userId);
            notification.setCreated(now);
            notification.setReadAt(null);
            notificationRepository.save(notification);
        }

    }

    /**
     * Determines which users should receive a notification about an inbound SMS
     * This implementation finds users associated with the contact (case workers)
     */
    private Set<Long> determineRecipientsForInboundSms(int contactId) {
        Set<Long> recipients = new HashSet<>();

        // we only want notifications for support workers of current live referrals
        Long clientId = clientRepository.findOneIdByContactId(contactId);
        if (clientId != null) {
            var workerIds = referralRepository.findByClientId(clientId).stream()
                    .filter(ServiceRecipientCaseStatusView::isLive)
                    .filter(r -> r.getSupportWorker() != null)
                    .map(w -> w.getSupportWorker().getId())
                    .toList();
            var userIds = userRepository.findUserIdsByContactIds(workerIds);
            recipients.addAll(userIds);
        }

        return recipients;
    }

}
