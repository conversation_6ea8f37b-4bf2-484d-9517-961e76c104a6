package com.ecco.webApi.serviceConfig;

import com.ecco.infrastructure.rest.hateoas.schema.EnumSchemaCreator;
import com.ecco.infrastructure.rest.hateoas.schema.ResourceSchemaCreator;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.viewModel.ProjectViewModel;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToViewModel;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import com.ecco.webApi.annotations.GetJsonSchema;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.ServicesViewModel;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.factories.JsonSchemaFactory;
import com.fasterxml.jackson.module.jsonSchema.types.IntegerSchema;
import com.fasterxml.jackson.module.jsonSchema.types.ObjectSchema;
import lombok.RequiredArgsConstructor;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Stream;

import static java.util.Collections.singleton;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

@RestController
@RequiredArgsConstructor
public class ServiceController extends BaseWebApiController {

    private final ServicesService servicesService;
    private final ServiceCategorisationRepository serviceCategorisationRepository;
    private final ResourceSchemaCreator resourceSchemaCreator;
    private final EnumSchemaCreator enumSchemaCreator;
    private final JsonSchemaFactory schemaFactory;
    private final ServiceCategorisationToViewModel serviceCategorisationToViewModel;

    @GetJson("/service/")
    public Stream<ServiceViewModel> findAll(HttpServletResponse response) {
        cacheModerately(response);
        return servicesService.findAll();
    }

    @GetJson("/serviceCategorisations/")
    public List<ServiceCategorisationViewModel> findAllServiceCategorisations() {
        return serviceCategorisationRepository.findAll().stream().map(serviceCategorisationToViewModel).toList();
    }

    @GetJson("/servicesProjects/")
    public ServicesViewModel findAllServicesProjects() {
        return servicesService.findAllServicesProjects();
    }

    @GetJsonSchema("/servicesProjects$enumSchema/")
    public ResponseEntity<JsonSchema> enumerateAllServicesProjects() {
        List<com.ecco.dto.ServiceViewModel> services = servicesService.findAllServicesProjects().services;

        JsonSchema schema = resourceSchemaCreator.createNestedEnum(services.stream(), methodOn(getClass()).enumerateAllServicesProjects(),
                vm -> {
                    ObjectSchema objectSchema = schemaFactory.objectSchema();
                    IntegerSchema serviceSchema = schemaFactory.integerSchema();
                    serviceSchema.setEnums(singleton(String.valueOf(vm.getId())));
                    serviceSchema.setTitle(vm.getName());
                    objectSchema.putProperty("service", serviceSchema);
                    if (!vm.getProjects().isEmpty()) {
                        objectSchema.putOptionalProperty("project", enumSchemaCreator.createEnum(
                                vm.getProjects().stream(),
                                p -> String.valueOf(p.getId()), ProjectViewModel::getName, null,
                                schemaFactory::integerSchema
                        ));
                    }
                    return objectSchema;
                }, null, null);
        return ResponseEntity.ok(schema);
    }


    @GetJson("/service/{id}/")
    public ServiceViewModel findOne(@PathVariable long id, HttpServletResponse response) {
        cacheModerately(response);
        return servicesService.findOne(id);
    }

}
