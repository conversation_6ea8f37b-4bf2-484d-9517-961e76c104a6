package com.ecco.webApi.evidence;

import com.ecco.dao.ReferralPredicates;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.ReferralSummary;
import com.ecco.dom.ReferralStatusName;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.infrastructure.rest.hateoas.schema.EnumConstant;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaMetadata;
import com.ecco.infrastructure.rest.hateoas.schema.JsonSchemaProperty;
import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingController;
import com.ecco.infrastructure.rest.hateoas.schema.SchemaProvidingLinkDescriptionObject;
import com.ecco.infrastructure.rest.hateoas.schema.TypedReferenceSchema;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.webApi.serviceConfig.ServiceController;
import com.ecco.webApi.viewModels.ResourceList;
import com.fasterxml.jackson.module.jsonSchema.JsonSchema;
import com.fasterxml.jackson.module.jsonSchema.types.LinkDescriptionObject;

import com.querydsl.core.types.Predicate;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.hateoas.IanaLinkRelations;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import java.util.Optional;

import static com.ecco.dom.QReferral.referral;
import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * Web API to obtain a list of referrals, paged and filtered by service, project and status group.
 * This can then be used to build a rich client version of the referral list page.
 * The API is also self describing by means of a schema.
 *
 * @since 20/01/2016
 */
@RestController
@RequestMapping("/referrals/list")
public class ReferralListController extends SchemaProvidingController<ReferralListController> {
    private static final String REL_INSTANCES = "instances";


    private final EntityRestrictionService entityRestrictionService;
    private final RepositoryBasedServiceCategorisationService svcCatsService;
    private final ReferralRepository referralRepository;
    private final ReferralListRowResourceAssembler rowResourceAssembler;


    public ReferralListController(ReferralRepository referralRepository,
                                  EntityRestrictionService entityRestrictionService,
                                  RepositoryBasedServiceCategorisationService svcCatsService,
                                  ReferralListRowResourceAssembler rowResourceAssembler) {
        super();
        this.referralRepository = referralRepository;
        this.entityRestrictionService = entityRestrictionService;
        this.svcCatsService = svcCatsService;
        this.rowResourceAssembler = rowResourceAssembler;
    }

    @Override
    public String getEntityTypeName() {
        return "referrals";
    }

    @GetJson("/")
    public ResourceList<ReferralListRowResource> list(
            @RequestParam(name = "company", required = false)
            @JsonSchemaMetadata(order = 3)
                    Integer companyId,
            @RequestParam(name = "client group", required = false)
            @JsonSchemaMetadata(order = 6)
                    Integer clientGroupId,
            @RequestParam(name = "service", required = false)
            @JsonSchemaMetadata(order = 10)
            Long serviceId,
            @RequestParam(name = "project", required = false)
            @JsonSchemaMetadata(order = 20)
            Long projectId,
            @RequestParam(name = "statusGroup")
            @JsonSchemaMetadata(title = "status group", defaultValue = "ongoing", order = 30)
            @JsonSchemaProperty(enumValues = {
                    @EnumConstant(value = "allNoDates", title = "all"),
                    @EnumConstant("ongoing"), // = incomplete + live
                    @EnumConstant("live"),
                    @EnumConstant(value = "dueSlaTask", title = "due date"),
                    @EnumConstant("incomplete"),
                    @EnumConstant("waiting"),
                    @EnumConstant("signposted"),
                    @EnumConstant("exited")
            })
            String statusGroup,
            @RequestParam(name = "page", defaultValue="0")
            Integer page,
            @RequestParam(name = "pageSize", defaultValue="15") // could do getPageSize("pageSize.referrals"));
            @JsonSchemaProperty(readOnly = true)
            Integer pageSize) {

        ReportCriteriaDto dto = createCriteriaDto(companyId, clientGroupId, serviceId, projectId, statusGroup);
        Predicate p = ReferralPredicates.referralPredicateHideDeleteRequested(dto, entityRestrictionService, svcCatsService);
        PageRequest pr = referralPageAndSorting(page, pageSize, dto);
        final Page<ReferralSummary> referrals = referralRepository.findAllAsReferralSummary(p, pr);

        Page<ReferralListRowResource> resourcePage = referrals.map(rowResourceAssembler::toModel);

        ResourceList<ReferralListRowResource> resourceList = new ResourceList<>(resourcePage.getContent());

        if (resourcePage.hasPrevious()) {
            resourceList.add(
                    linkToApi(methodOn(ReferralListController.class).list(companyId, clientGroupId, serviceId, projectId, statusGroup, page - 1, pageSize))
                        .withRel(IanaLinkRelations.PREV));
        }
        if (resourcePage.hasNext()) {
            resourceList.add(
                    linkToApi(methodOn(ReferralListController.class).list(companyId, clientGroupId, serviceId, projectId, statusGroup, page + 1, pageSize))
                        .withRel(IanaLinkRelations.NEXT));
        }
        resourceList.setNumPages(resourcePage.getTotalPages());
        resourceList.setPageSize(pageSize);
        resourceList.setNumItems(resourcePage.getTotalElements());
        addDescribedByLink(resourceList);
        return resourceList;
    }


    private ReportCriteriaDto createCriteriaDto(Integer companyId, Integer clientGroupId, Long serviceId, Long projectId, String statusGroup) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setReferralStatus(statusGroup);
        dto.setCompanyId(companyId);
        dto.setClientGroupId(clientGroupId);
        dto.setProjectId(projectId);
        dto.setServiceId(serviceId);
        // don't restrict to time-based data, we want all of the db for the status
        // so we don't supply dates, so it doesn't matter that newReferralsOnly
        // default has changed - but if it did, we should consider this value
        return dto;
    }

    @Override
    public ResponseEntity<com.fasterxml.jackson.module.jsonSchema.JsonSchema> describe(WebRequest request) {
        Object invocation = self().list(0, 0, 0L, 0L, "", 0, 0);

        JsonSchema listRequestParamSchema = getSchemaCreator().createForRequestParams(invocation);
        // Make sure the parameters are valid services and projects by extending the enumeration of them.
        // NB extends[] = allOf, type[] = anyOf (this is more explicit in JSON Schema v4)
        var linkBuilder = linkToApi(methodOn(ServiceController.class).enumerateAllServicesProjects());
        listRequestParamSchema.setExtends(new JsonSchema[] {
                new TypedReferenceSchema(linkBuilder.toString(),
                        listRequestParamSchema.getType())
        });

        LinkDescriptionObject instancesLink = new SchemaProvidingLinkDescriptionObject()
                .setRel(REL_INSTANCES)
                .setMethod(RequestMethod.GET.toString())
                .setHref(linkToApi(invocation).toString())// .toUriComponentsBuilder().replaceQuery(null).build(false).toString())
                .setSchema(listRequestParamSchema);

        JsonSchema schema = getSchemaCreator().create(ReferralListRowResource.class,
                self().describe(request),
                Optional.of(instancesLink),
                Optional.empty());

        return ResponseEntity.ok(schema);
    }

    private PageRequest referralPageAndSorting(int page, int pageSize, ReportCriteriaDto dto) {
        QSort sort;


        if (ReferralStatusName.DueSlaTask.getName().equalsIgnoreCase(dto.getReferralStatus())) {
            sort = new QSort(referral.serviceRecipient.nextSlaDueDate.asc().nullsLast());
        }
        else if (!ReferralStatusName.Waiting.getName().equalsIgnoreCase(dto.getReferralStatus())) {
//            if (orderByReceivedDate) {
//                orderBy = "receivedDate";
//            } else if (orderByDecisionDate) {
//                orderBy = "decisionDate";
//            } else if (orderByReceivingServiceDate) {
//                orderBy = "receivingServiceDate";
//            } else {
            sort = new QSort(referral.serviceRecipient.created.desc());
//            }
        } else {
            // score first, then others...
            sort = new QSort(
                    referral.waitingListScore.desc(),
                    referral.receivedDate.desc()
                    );
        }

        return PageRequest.of(page, pageSize, sort);
    }

}
