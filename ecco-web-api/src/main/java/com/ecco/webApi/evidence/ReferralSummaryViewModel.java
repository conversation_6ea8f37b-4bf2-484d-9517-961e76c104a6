package com.ecco.webApi.evidence;

import com.ecco.servicerecipient.ServiceRecipientCaseStatusView;
import com.ecco.dom.agreements.DaysOfWeek;
import com.ecco.servicerecipient.AcceptState;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import com.ecco.dom.Agency;
import com.ecco.dom.Individual;
import com.ecco.servicerecipient.ServiceRecipientSummary;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@NoArgsConstructor
@Setter
@Getter
//@JsonIgnoreProperties(ignoreUnknown = true, value = {"decisionReferralMadeOn"})
@JsonInclude(NON_NULL)
public class ReferralSummaryViewModel extends ServiceRecipientSummary implements ServiceRecipientCaseStatusView {

    /** This is true if this referral should be presented read-only to the user */
    public boolean _readOnly;

    /** The referral ID. */
    public Long referralId;

    /** A code possibly chosen on import, which uniquely identifies a referral */
    public String referralCode;

    /** Within family support, referrals can be created for family members which 'hang' off a primary referral
     * so the primaryReferralId represents the referral which this hangs off. */
    public Long primaryReferralId;

    /**
     * The relationship to the primary referral - see relationshipToPrimaryReferralId
     * A list def - search relationshipsListById.
     */
    public Integer primaryRelationshipId;

    /** The client ID of the client who is the subject of this referral. */
    public Long clientId;

    /** A code possibly chosen on import, which uniquely identifies a client */
    public String clientCode;

    /** The name of the service user who is the subject of this referral. */
    public String clientDisplayName;

    /** The client's first name. */
    public String clientFirstName; // ui-tests only - see ReferralToViewModel
    public String firstName;

    /** The client's last name. */
    public String clientLastName; // ui-tests only - see ReferralToViewModel
    public String lastName;

    /** The ID of the pending status of the referral. */
    public Integer pendingStatusId;
    public Integer latestClientStatusId;
    public DateTime latestClientStatusDateTime;

    /** The id of the {@link Agency} that referred the service user
     * to this service, or null if the service user was not referred by an
     * Agency.
     *
     * @see ReferralViewModel#source
     */

    public Long referrerAgencyId;

    /** The id of the {@link Individual} who referred the
     * service user to this service, or null if the service user was
     * not referred by an individual.
     *
     * @see ReferralViewModel#source
     */

    public Long referrerIndividualId;

    /**
     * A text description of the reason for this referral
     */
    public String referralReason;

    /** Id of the geographic area as defined in a list of {@link com.ecco.config.dom.ListDefinitionEntry}
     * exposed as {@link com.ecco.webApi.featureConfig.SessionDataViewModel#listDefinitions} */
    public Integer srcGeographicAreaId;

    /** reflects whether appropriate referral task has been done and what decision was */
    public AcceptState appropriateReferralState;
    /** reflects whether accept on service task has been done and what decision was */
    public AcceptState acceptOnServiceState;

    /** The date the referral is INTENDED to be signposted/accepted by the organisation (so not the audit date)
     *  This is historical, and should be changed to interviewDate (decisionMadeOn is the audit date for a decision) */
    public LocalDateTime decisionDate;

    /** The date at which the service user was accepted on the service (automatically set but can be changed)
     * This may not reflect the date the service user actually started - see receivingServiceDate, as there
     * could be a waiting list to actually start.
     * This is a users local date.
     */
    public LocalDate decisionReferralMadeOn;

    /** The date at which the service user was accepted on the service (automatically set but can be changed)
     * This may not reflect the date the service user actually started - see receivingServiceDate, as there
     * could be a waiting list to actually start.
     * This is a users local date.
     */
    public LocalDate decisionMadeOn;
    public DateTime decisionMadeOnDT;

    public LocalDate nextDueSlaDate;

    public Long nextDueSlaTaskId;

    public Long supportWorkerId; // TODO this should be refactored as allocatedWorkerContactId, and all other supportWorker fields should be refactored as allocatedWorkerContact

    public boolean selfReferral;

    /** The date this referral was received by the service provider, or null if
     * that date has not been recorded in Ecco. */
    public LocalDate receivedDate;

    /** The users date time of the agreement on the data protection (accepted/rejected, signed or otherwise).
     * Note this stays as UTC server-side - see ReferralServiceImpl.setReferralWithDataProtectionSignature
     * Eventually we could use ms and convert client-side. */
    public LocalDateTime dataProtectionAgreementDate;
    public Boolean dataProtectionAgreementStatus;
    /** Whether there has been a signature on the data protection */
    public UUID dataProtectionSignedId;

    /** The date by which the referral had a consent to disclose agreed or rejected (if any) */
    public LocalDateTime consentAgreementDate;
    public Boolean consentAgreementStatus;
    /** Whether there has been a signature on the consent to disclose */
    public UUID consentSignedId;

    public LocalDateTime agreement1AgreementDate;
    public Boolean agreement1AgreementStatus;
    public UUID agreement1SignedId;
    public LocalDateTime agreement2AgreementDate;
    public Boolean agreement2AgreementStatus;
    public UUID agreement2SignedId;
    public LocalDateTime agreement3AgreementDate;
    public Boolean agreement3AgreementStatus;
    public UUID agreement3SignedId;
    public LocalDateTime agreement4AgreementDate;
    public Boolean agreement4AgreementStatus;
    public UUID agreement4SignedId;
    public LocalDateTime agreement5AgreementDate;
    public Boolean agreement5AgreementStatus;
    public UUID agreement5SignedId;
    public LocalDateTime agreement6AgreementDate;
    public Boolean agreement6AgreementStatus;
    public UUID agreement6SignedId;
    public LocalDateTime agreement7AgreementDate;
    public Boolean agreement7AgreementStatus;
    public UUID agreement7SignedId;
    public LocalDateTime agreement8AgreementDate;
    public Boolean agreement8AgreementStatus;
    public UUID agreement8SignedId;
    public LocalDateTime agreement9AgreementDate;
    public Boolean agreement9AgreementStatus;
    public UUID agreement9SignedId;
    public LocalDateTime agreement10AgreementDate;
    public Boolean agreement10AgreementStatus;
    public UUID agreement10SignedId;

    /** The audit date to indicate when 'we' started working with the client.
     * Typically when setting up the interview. */
    public LocalDate firstResponseMadeOn;

    /** The date and time of the first interview offered. */
    public LocalDateTime firstOfferedInterviewDate;

    /**
     * The number of times which the interviewee has not attended the interview.
     */
    public Integer interviewDna;

    /**
     * Comments about the non attendence of an interviewee.
     */
    public String interviewDnaComments;

    /**
     * Comments about the set-up of the interview.
     */
    public String interviewSetupComments;

    /**
     * The date at which the service user will start, or has started receiving service agreed for
     * this referral, or null (see receivingService and referralStartServiceFlow.jsp).
     * NB This is ONLY set if a referral aspect 'start' or 'auto start' is configured
     * which is likely to be many, but not all sites. Until then we also use
     * decisionMadeOn.
     */
    public LocalDate receivingServiceDate;

    /** Bit field of days attending: bit 0 = MON -> bit 6 = SUN */
    public int daysAttending;

    /** Include a hidden flag if the referral returned has been requested for deletion */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    public boolean requestedDelete;

    public Long interviewer1ContactId;
    public Long interviewer2ContactId;
    public String interviewLocation;

    public Integer exitReasonId;

    /** The date the client exited the referral This is not the signposted/rejected date. */
    public LocalDate exitedDate;

    /**
     * The id of the signposted comment of the referral.
     * Non-null means that the referral has been signposted and we use this
     * in combination with decisionMadeOn to determine whether the decision
     * was successful or not. See ReferralStatusCommonPredicates.java closed().
     */
    public Long signpostedCommentId;

    /** Whether this referral was sent back to the referring agency/individual */
    public boolean signpostedBack;
    public Integer signpostedReasonId;
    public Long signpostedAgencyId;

    /**
     * The entity ID of the FundingSource who funded this referral
     */
    public Long fundingSourceId;

    /**
     * Hours of support available
     */
    public BigDecimal fundingHoursOfSupport;

    /** The reference for the funding */
    public String fundingPaymentRef;

    public BigDecimal fundingAmount;

    /** The date on which the decision about funding was made */
    public LocalDate fundingDecisionDate;

    /** The date on which a funding review is due */
    public LocalDate fundingReviewDate;

    /** Whether or not funding was accepted */
    public Boolean fundingAccepted;

    /** The calculated score this referral should be in a waiting list */
    public Integer waitingListScore;

    public DaysOfWeek getMeetingDays() {
        return DaysOfWeek.fromBits(daysAttending);
    }


    @Override
    public Integer getServiceRecipientId() {
        return serviceRecipientId;
    }

    @Override
    public LocalDate getReceivingServiceLocalDate() {
        return receivingServiceDate;
    }

    @Override
    public boolean isAcceptedOnService() {
        return acceptOnServiceState != null && acceptOnServiceState.equals(AcceptState.ACCEPTED);
    }

    @Override
    public Boolean isAcceptedReferral() {
        return appropriateReferralState != null && appropriateReferralState.equals(AcceptState.ACCEPTED);
    }

    /**
     * finalDecision is a maintained property, but silently ignored since its derivable from decisionDate != null
     */
    @Override
    public boolean isFinalDecision() {
        return decisionMadeOn != null;
    }

    @Override
    public Boolean isReferralDecision() {
        return decisionReferralMadeOn != null;
    }

    @Override
    public DateTime getDecisionMadeOnDT() {
        return decisionMadeOnDT;
    }

    @Override
    public boolean isPending() {
        return pendingStatusId != null;
    }

    // COPY of ServiceRecipientSummary.mapFrom
    public ReferralSummaryViewModel mapFrom(ServiceRecipientSummary input) {
        discriminator = input.discriminator;
        prefix = input.prefix;
        parentId = input.parentId;
        displayName = input.displayName;
        calendarId = input.calendarId;
        serviceRecipientId = input.serviceRecipientId;
        serviceIdAcl = input.serviceIdAcl;
        projectIdAcl = input.projectIdAcl;
        serviceAllocationId = input.serviceAllocationId;
        serviceTypeId = input.serviceTypeId;
        dataProtectionSignedId = input.dataProtectionSignedId;

        return this;
    }

}
