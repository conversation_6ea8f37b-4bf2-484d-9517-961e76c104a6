package com.ecco.webApi.evidence

import com.ecco.config.service.SettingsService
import com.ecco.dao.ReferralRepository
import com.ecco.dao.commands.ServiceRecipientCommandArchiveRepository
import com.ecco.dao.commands.ServiceRecipientCommandRepository
import com.ecco.dao.commands.ServiceRecipientTaskCommandRepository
import com.ecco.dom.commands.DeleteRequestEvidenceCommand
import com.ecco.dom.servicerecipients.BaseServiceRecipientCommand
import com.ecco.dom.servicerecipients.commands.ServiceRecipientCommandArchive
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.serviceConfig.EntityRestrictionService
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository
import com.ecco.serviceConfig.service.ServiceTypeService
import com.ecco.servicerecipient.ServiceRecipientSummary
import com.ecco.servicerecipient.ServiceRecipientSummaryService
import com.ecco.webApi.clients.*
import com.ecco.webApi.contacts.ServiceRecipientCalendarEntryCommandHandler
import com.ecco.webApi.contacts.ServiceRecipientCalendarEntryParams
import com.ecco.webApi.controllers.NotFoundException
import com.ecco.webApi.viewModels.Result
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.composed.web.rest.json.GetJson
import org.springframework.composed.web.rest.json.PostJson
import org.springframework.data.domain.PageRequest
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.Authentication
import org.springframework.util.Assert
import org.springframework.web.bind.annotation.*
import java.io.IOException
import java.util.*
import java.util.stream.Collectors

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
class ServiceRecipientController @Autowired constructor(
    serviceRecipientRepository: ServiceRecipientRepository,
    serviceRecipientSummaryService: ServiceRecipientSummaryService,
    // Temporary - we hope!  Used as short cut when it's a referral (which it usually is)
    referralRepository: ReferralRepository,
    entityRestrictionService: EntityRestrictionService,
    private val moveHandler: MoveServiceRecipientCommandHandler,
    private val deleteHandler: DeleteServiceRecipientCommandHandler,
    private val deleteRequestHandler: DeleteRequestServiceRecipientCommandHandler,
    private val deleteEvidenceHandler: DeleteEvidenceCommandHandler,
    private val deleteRequestEvidenceHandler: DeleteRequestEvidenceCommandHandler,
    private val serviceRecipientCalendarEntryCommandHandler: ServiceRecipientCalendarEntryCommandHandler,
    private val serviceRecipientCommandRepository: ServiceRecipientCommandRepository,
    private val serviceRecipientCommandArchiveRepository: ServiceRecipientCommandArchiveRepository,
    serviceTypeService: ServiceTypeService,
    serviceCategorisationRepository: ServiceCategorisationRepository,
    private val serviceRecipientTaskCommandRepository: ServiceRecipientTaskCommandRepository,
    @param:Qualifier("SvcRecExtractJsonWithDisplayName")
    private val srExtractJsonBody: ServiceRecipientExtractCommandViewModelJson<BaseServiceRecipientCommand>,
    @param:Qualifier("SvcRecExtractJson")
    private val goalExtractJsonBody: GoalCommandExtractCommandViewModelJson<BaseServiceRecipientCommand>
) : ServiceRecipientBaseController(serviceRecipientRepository, serviceRecipientSummaryService, referralRepository, entityRestrictionService, serviceTypeService, serviceCategorisationRepository) {

    private val referralSummaryToViewModel: ReferralSummaryToViewModel

    init {
        referralSummaryToViewModel = ReferralSummaryToViewModel()
    }

    @GetJson("/service-recipient/{serviceRecipientId}/cached")
    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    fun findOneCached(@PathVariable serviceRecipientId: Int): ServiceRecipientSummary {
        return findOne(serviceRecipientId, true);
    }

    @GetJson("/service-recipient/{serviceRecipientId}")
    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    fun findOne(@PathVariable serviceRecipientId: Int): ServiceRecipientSummary {
        return super.findOne(serviceRecipientId, cached = false, withoutAclSecurity = false);
    }

    @GetJson("/service-recipients/byIds/noAcl/")
    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    fun findManyWithoutAclSecurity(@RequestParam(name = "ids") serviceRecipientIds: List<Int>): Iterable<ServiceRecipientSummary> {
        val recipients = this.serviceRecipientRepository.findAllById(serviceRecipientIds)
        return recipients.map { recipient -> ServiceRecipientSummary().mapFrom(recipient)}
    }

    @PostJson("/service-recipient/{serviceRecipientId}/calendar-entry/command/")
    @ResponseStatus(HttpStatus.OK)
    @Throws(
        IOException::class
    )
    fun calendarEntryCommand(
        authentication: Authentication,
        params: ServiceRecipientCalendarEntryParams,
        @RequestBody requestBody: String
    ): Result {
        Assert.notNull(params.serviceRecipientId, "Must specify srId")
        return serviceRecipientCalendarEntryCommandHandler.handleCommand(authentication, params, requestBody)
    }

    @PreAuthorize("hasRole('ROLE_STAFF')")
    @PostJson("/service-recipient/{serviceRecipientId}/delete-request/")
    @ResponseStatus(
        HttpStatus.OK
    )
    @Throws(
        IOException::class
    )
    fun deleteRequestCommand(
        authentication: Authentication,
        params: DeleteServiceRecipientParams,
        @RequestBody requestBody: String
    ): Result {
        Assert.notNull(params.serviceRecipientId, "Must specify id when deleting")
        deleteRequestHandler.handleCommand(authentication, params, requestBody)
        return Result("updated")
    }

    @PreAuthorize("hasRole('ROLE_DELETEREFERRAL')")
    @PostJson("/service-recipient/{serviceRecipientId}/delete/")
    @ResponseStatus(
        HttpStatus.OK
    )
    @Throws(
        IOException::class
    )
    fun deleteCommand(
        authentication: Authentication,
        params: DeleteServiceRecipientParams,
        @RequestBody requestBody: String
    ): Result {
        Assert.notNull(params.serviceRecipientId, "Must specify id when deleting")
        deleteHandler.handleCommand(authentication, params, requestBody)
        return Result("deleted")
    }

    @PreAuthorize("hasRole('ROLE_DELETEREFERRAL')")
    @PostJson("/service-recipient/{serviceRecipientId}/move/")
    @ResponseStatus(
        HttpStatus.OK
    )
    @Throws(
        IOException::class
    )
    fun moveCommand(
        authentication: Authentication,
        params: DeleteServiceRecipientParams,
        @RequestBody requestBody: String
    ): Result {
        Assert.notNull(params.serviceRecipientId, "Must specify id when moving")
        moveHandler.handleCommand(authentication, params, requestBody)
        return Result("moved")
    }

    @PreAuthorize("hasRole('ROLE_STAFF')")
    @PostJson("/service-recipient/{serviceRecipientId}/evidence/{workUuid}/delete-request/")
    @ResponseStatus(
        HttpStatus.OK
    )
    @Throws(
        IOException::class
    )
    fun deleteRequestEvidenceCommand(
        authentication: Authentication,
        params: DeleteEvidenceParams,
        @RequestBody requestBody: String
    ): Result {
        Assert.notNull(params.serviceRecipientId, "Must specify serviceRecipientId when deleting")
        Assert.notNull(params.workUuid, "Must specify uuid when deleting")
        deleteRequestEvidenceHandler.handleCommand(authentication, params, requestBody)
        return Result("updated")
    }

    @PreAuthorize("hasRole('ROLE_ADMINEVIDENCE')")
    @PostJson("/service-recipient/{serviceRecipientId}/evidence/{workUuid}/delete/")
    @ResponseStatus(
        HttpStatus.OK
    )
    @Throws(
        IOException::class
    )
    fun deleteCommand(
        authentication: Authentication,
        params: DeleteEvidenceParams,
        @RequestBody requestBody: String
    ): Result {
        Assert.notNull(params.serviceRecipientId, "Must specify id when deleting")
        Assert.notNull(params.workUuid, "Must specify uuid when deleting")
        deleteEvidenceHandler.handleCommand(authentication, params, requestBody)
        return Result("deleted")
    }

    @GetJson("/service-recipients/{serviceRecipientId}/evidence/commands/delete-request/")
    fun findAllDeleteEvidenceRequestCommandsByServiceRecipient(@PathVariable serviceRecipientId: Int): String {
        val requests: List<DeleteRequestEvidenceCommand> =
            serviceRecipientCommandRepository.findDeleteEvidenceRequestsByServiceRecipientId(
                serviceRecipientId
            )
        return srExtractJsonBody.asJsonArrayx(
            requests
        )
    }

    @GetJson("/service-recipients/{serviceRecipientId}/commands/")
    fun findAllCommandsByServiceRecipient(
        @PathVariable serviceRecipientId: Int,
        @RequestParam(name = "page", required = false) page: Int?
    ): String {
        var pr: PageRequest? = null
        if (page != null) {
            pr = settingsService.settingFor(SettingsService.Audits.PageSizeAudits).asPageRequest(page)
        }
        return srExtractJsonBody.asJsonArray(
            // paged and ordered by created DESC
            serviceRecipientCommandRepository.findAllByServiceRecipientIdOrderByCreatedDesc(
                serviceRecipientId,
                pr
            )
        )
    }

    @GetJson("/service-recipients/{serviceRecipientId}/commands/{taskName}/")
    fun findAllCommandsByServiceRecipientAndTaskName(
        @PathVariable serviceRecipientId: Int,
        @PathVariable taskName: String,
        @RequestParam(name = "page", required = false) page: Int?,
        @RequestParam(name = "draft", required = false) draft: Boolean?
    ): String {
        var pr: PageRequest? = null
        if (page != null) {
            pr = settingsService.settingFor(SettingsService.Audits.PageSizeAudits).asPageRequest(page)
        }
        return srExtractJsonBody.asJsonArray(
            // index on draft, service_recipient_id, task_name, created
            if (draft == true) {
                serviceRecipientTaskCommandRepository.findAllByServiceRecipientIdAndTaskNameAndDraftTrueOrderByCreatedDesc(
                    serviceRecipientId, taskName, pr
                )
            } else {
                serviceRecipientTaskCommandRepository.findAllByServiceRecipientIdAndTaskNameOrderByCreatedDesc(
                    serviceRecipientId,
                    taskName,
                    pr
                )
            }
        )
    }

    /**
     * A command may not exist if it is now in the command archive table - ie a client deleted.
     * Using a query param is part of caching in the spec, but proxy servers may not have caught up.
     */
    @GetJson("/service-recipients/command/{uuid}/")
    fun findOneCommandByServiceRecipient(
        @PathVariable uuid: UUID?,
        @RequestParam(value = "optional", defaultValue = "false") optional: Boolean
    ): String {
        return if (optional) {
            serviceRecipientCommandRepository.findOneOptionalByUuid(uuid)
                .map { cmd -> srExtractJsonBody.apply(cmd).toString() }
                .orElseThrow { NotFoundException(uuid) }
        } else Objects.requireNonNull(srExtractJsonBody.apply(serviceRecipientCommandRepository.findOneByUuid(uuid)))
            .toString()
    }

    @GetJson("/service-recipients/{serviceRecipientId}/commands/delete-request/")
    fun findAllDeleteRequestCommandsByServiceRecipient(@PathVariable serviceRecipientId: Int): String {
        return srExtractJsonBody.asJsonArray(
            serviceRecipientCommandRepository.findDeleteRequestsByServiceRecipientId(
                serviceRecipientId
            )
        )
    }

    @GetJson("/service-recipients/{serviceRecipientId}/evidence/commands/delete/")
    fun findAllDeleteEvidenceCommandsByServiceRecipient(@PathVariable serviceRecipientId: Int): String {
        return srExtractJsonBody.asJsonArray(
            serviceRecipientCommandRepository.findDeleteEvidenceRequestsByServiceRecipientId(
                serviceRecipientId
            )
        )
    }

    /**
     * Return the JSON of the archived commands.
     * We can use BaseServiceRecipientCommandViewModel if we somehow load the archived commands into the existing
     * class hierarchy (which is currently fixed to the main svcrec_commands table) - otherwise we load into a generic
     * class [ServiceRecipientCommandArchive] which will miss properties in the transfer.
     */
    @GetJson("/service-recipient/{serviceRecipientId}/commands/archive/")
    fun findArchiveCommandsByServiceRecipient(@PathVariable serviceRecipientId: Int): String {
        val commands =
            serviceRecipientCommandArchiveRepository.findByServiceRecipientIdOrderByCreatedDesc(serviceRecipientId)
        val joinedJson: CharSequence = commands.stream()
            .map(goalExtractJsonBody)
            .collect(Collectors.joining(","))
        return "[$joinedJson]"
    }
}