package com.ecco.webApi.listsConfig;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class AppointmentTypeViewModel {
    public Integer id;
    public String name;
    public String service;
    public Long serviceId;
    public int recommendedDurationInMinutes;
    public Boolean disabled;
    // TODO: See if we can find a way of doing JSON pass-thru to/from database instead of to from map
    // surely a lazy serialiser would be able to do it...
    public Map<String, Object> parameters;

    /** List of skills & attributes that are required for this appointment type */
    public List<ListDefinitionEntryViewModel> requirements;
}
