<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <artifactId>ecco-web-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-calendar</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-buildings</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-evidence</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-group-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-hr</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-rota</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-incidents</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-messaging</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-notifications</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-repairs</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-contracts</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-finance</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-workflow</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-reports</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>2.5.15</version> <!-- To align with what is in ecco-int-* -->
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>ecco-workflow-activiti</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.hateoas</groupId>
            <artifactId>spring-hateoas</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eccosolutions</groupId>
            <artifactId>test-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.fge</groupId>
            <artifactId>json-patch</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
    </dependencies>

</project>
