package com.ecco.service;

import java.util.Collection;
import java.util.List;

import javax.annotation.PostConstruct;

import com.ecco.dom.*;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import org.springframework.security.access.prepost.PostFilter;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.security.dto.AclExtractor;
import com.ecco.security.acl.AclHandler;
import lombok.AllArgsConstructor;

@org.springframework.stereotype.Service("entityRestrictionService")
@WriteableTransaction
@AllArgsConstructor
public class EntityRestrictionServiceImpl implements EntityRestrictionService {

    private final AclHandler aclHandler;
    private final ServiceRepository serviceRepository;
    private final ProjectRepository projectRepository;

    @PostConstruct
    @Override
    public void ensureAcls() {
        Collection<AclExtractor<? extends BaseEntity<Long>>> aclExtractors = aclHandler.aclExtractors();
        for (AclExtractor<? extends BaseEntity<Long>> aclExtractor : aclExtractors) {
            List<? extends BaseEntity<Long>> objects = aclExtractor.listObjects();
            for (BaseEntity<Long> o : objects) {
                if (!aclHandler.checkAclExists(aclExtractor.getClazz().getCanonicalName(), o.getId())) {
                    aclHandler.createAcl(aclExtractor.getClazz().getCanonicalName(), o.getId());
                }
            }
        }
    }

    //@PostFilter("hasPermission(#user, 'allow without acls') or (hasPermission(filterObject, 'read') or hasPermission(filterObject, 'admin'))")
    @PostFilter("hasRole('AAA') or hasPermission(filterObject, 'read')")
    @Override
    public List<ServiceAclId> getRestrictedServiceIds() {
        // not entity-related else we get proxy issues mismatching hasPermission
        return serviceRepository.findAllAclIds();
    }

    @PostFilter("hasRole('AAA') or hasPermission(filterObject, 'read')")
    @Override
    public List<ProjectAclId> getRestrictedProjectIds() {
        // not entity-related else we get proxy issues mismatching hasPermission
        List<ProjectAclId> entities = projectRepository.findAllAclIds();

        // previously, restricting the projects list couldn't help with knowing access to 'any project' (including 'none') or specifically 'this set'
        // so we used add a dummy -1 to indicate 'access all areas' and allows access to 'null' projects
        // this has now been deprecated in favour of simply specifing access at the service level and specifying no projects
        // we can do this because we are no longer assuming that services and project lists are independent of each other
        // as we move to a system where we specify the service with projects explicitly
        // the admin screen has now brought back the -1 permission, so we want to honour it
        // until we get Service and ServiceProject restrictions
        entities.add(ProjectAclId.accessAllProjectsFakeProject);

        // -2 indicates we can show null-assigned referrals in the list, but at present this is not implemented
        //entities.add(Project.accessNullProjectsFakeProject);

        return entities;
    }

}
