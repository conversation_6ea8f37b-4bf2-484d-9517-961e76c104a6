package com.ecco.service;

import com.ecco.dom.AddressedLocation;
import com.ecco.dom.Individual;
import com.ecco.dom.contacts.Address;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.dom.upload.SimpleUploadedFile;
import com.ecco.dom.upload.UploadedBytes;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationAclId;
import com.ecco.upload.dao.SimpleUploadedFileRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Objects;

import static java.util.stream.Collectors.toList;

@Service("contactService")
@WriteableTransaction
public class ContactServiceImpl implements ContactService {

    @Autowired private SimpleUploadedFileRepository simpleUploadedFileRepository;
    @Autowired private ContactRepository contactRepository;
    @Autowired private IndividualRepository individualRepository;
    @Autowired private ServiceRecipientRepository serviceRecipientRepository;
    @Autowired private EntityRestrictionService entityRestrictionService;
    @Autowired private RepositoryBasedServiceCategorisationService serviceCategorisationService;

    @Override
    public void setAvatar(long contactId, long bytesId) {
        // could do a simple hql versioned update, but this only loads one record
        Individual c = individualRepository.getById(contactId); // TODO: Support any ContactImpl
        UploadedBytes b = new UploadedBytes();
        b.setId(bytesId);
        c.setAvatar(b);
        individualRepository.save(c);
    }

    @Override
    public byte[] getAvatar(long contactId) {

        Long avatarId = contactRepository.findAvatarIdById(contactId);
        final SimpleUploadedFile uploadedFile = simpleUploadedFileRepository.findOneByUploadedBytes_Id(avatarId);
        return uploadedFile.getUploadedBytes().getBytes();
    }

    @Override
    public void removeAvatar(long contactId) {

        Long avatarId = contactRepository.findAvatarIdById(contactId);
        if (avatarId != null) {
            contactRepository.setAvatarByContactId(contactId, null);

            // Delete the orphaned avatar file and bytes (cascades)
            final SimpleUploadedFile uploadedFile = simpleUploadedFileRepository.findOneByUploadedBytes_Id(avatarId);
            // The avatar may have been from a referral attachment via uploadReferral.jsp, in which case it won't be a
            // SimpleUploadedFile at all and we'll leave it there.
            if (uploadedFile != null) {
                simpleUploadedFileRepository.delete(uploadedFile);
            }
        }
    }

    @Override
    public void updateAddress(long contactId, AddressedLocation addressedLocation) {
        // need jpa-managed entity for the annotations, @Query approach doesn't do it
        Individual contact = individualRepository.getById(contactId);
        contact.setAddressedLocation(addressedLocation);
        contact.setAddress(Address.from(addressedLocation)); // see ContactImpl.AddressLike
        // use updateEntity to trigger a flush in the 'before' commit tx phase, meaning the event gets triggered correctly
        individualRepository.save(contact);
    }

    @Nullable
    public List<Integer> getContactScopeServiceAllocationIdsFromFile(Integer serviceRecipientId, AgencyScope value) {
        var restrictions = EntityRestrictionService.getRestrictedServicesProjectsDto(entityRestrictionService, serviceCategorisationService);
        var cats = restrictions.getRestrictedServiceCategorisationIds();
        var srAlloc = serviceRecipientRepository.getById(serviceRecipientId).getServiceAllocation();
        var serviceId = srAlloc.getServiceId();
        var projectId = srAlloc.getProjectId() != null ? srAlloc.getProjectId() : null;

        List<Integer> allocationIds = null;
        if (AgencyScope.SERVICE.equals(value)) {
            allocationIds = cats.stream()
                    .filter(c -> Objects.equals(srAlloc.getServiceId(), c.getServiceId()))
                    .map(ServiceCategorisationAclId::getId).collect(toList());
        }
        if (AgencyScope.SERVICE_PROJECT.equals(value)) {
            allocationIds = cats.stream().filter(c ->
                    (Objects.equals(serviceId, c.getServiceId()) && Objects.equals(c.getProjectId(), projectId))
            ).map(ServiceCategorisationAclId::getId).collect(toList());
        }
        return allocationIds;
    }

}
