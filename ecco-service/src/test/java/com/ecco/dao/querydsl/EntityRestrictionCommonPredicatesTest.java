package com.ecco.dao.querydsl;

import com.ecco.dom.ProjectAclId;
import com.ecco.dom.ServiceAclId;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.serviceConfig.dom.QServiceCategorisation;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import com.querydsl.core.types.Predicate;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Test security predicates around services/projects.
 * NB This should mimic EntityRestrictionsCommonFilterTest.
 */
public class EntityRestrictionCommonPredicatesTest {

    private ServiceAclId s59;
    private ProjectAclId p33, p35;
    private List<ServiceCategorisationViewModel> svcCats = new ArrayList<>();

    public EntityRestrictionCommonPredicatesTest() {
        this.s59 = new ServiceAclId(59, null);
        s59.setName("s59");
        this.p33 = new ProjectAclId(33, "33");
        this.p35 = new ProjectAclId(35, "p35");

        // global
        var sc1 = new ServiceCategorisationViewModel(1, 59, 33);
        var sc2 = new ServiceCategorisationViewModel(2, 59, 35);
        Collections.addAll(svcCats, sc1, sc2);
    }

    @Test
    public void happyPath() {
        // restricted
        var restrictedServices = Collections.singletonList(s59);
        var restrictedProjects = Collections.singletonList(p33);
        ServicesProjectsDto restrictions = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);

        // predicate
        EntityRestrictionCommonPredicates terms = new EntityRestrictionCommonPredicates(restrictions, 59L, 33L);
        QServiceCategorisation svCat = QServiceCategorisation.serviceCategorisation;
        Predicate be = terms.getServiceCategorisations(QServiceCategorisation.serviceCategorisation);

        // we should not be allowed to search on p35 - its not in our restrictions
        Assert.assertEquals(be, svCat.service.id.eq(s59.getId()).and(svCat.project.id.eq(p33.getId())));
    }

    @Test
    public void specifiedProjectDoesNotBypassSecurity() {
        // restricted
        var restrictedServices = Collections.singletonList(s59);
        var restrictedProjects = Collections.singletonList(p33);
        ServicesProjectsDto restrictions = new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCats);

        // predicate
        EntityRestrictionCommonPredicates terms = new EntityRestrictionCommonPredicates(restrictions, 59L, 35L);
        QServiceCategorisation svCat = QServiceCategorisation.serviceCategorisation;
        Predicate be = terms.getServiceCategorisations(QServiceCategorisation.serviceCategorisation);

        // we should not be allowed to search on p35 - its not in our restrictions
        Assert.assertNotEquals(be, svCat.service.id.eq(s59.getId()).and(svCat.project.id.eq(p35.getId())));
    }

}
