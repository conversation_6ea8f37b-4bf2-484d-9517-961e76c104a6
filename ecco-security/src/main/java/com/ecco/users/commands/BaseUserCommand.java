package com.ecco.users.commands;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;
import com.ecco.security.dom.User;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import java.util.UUID;

@MappedSuperclass
public abstract class BaseUserCommand extends BaseIntKeyedCommand {

    @Getter
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userIdSubject", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.EXCEPTION)
    private User userSubject;

    @Getter
    @Setter
    @Column
    private Long userIdSubject;

    /** Make this available to pass out as discriminiator at client end */
    @Column(name = "commandname", insertable = false, updatable = false)
    private String commandName;

    protected BaseUserCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                              long userId, @Nonnull String body, Long userIdSubject) {
        super(uuid, remoteCreationTime, userId, body);
        this.userIdSubject = userIdSubject;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected BaseUserCommand() {
        super();
    }

    @Override
    public String getCommandName() {
        return commandName;
    }
}
