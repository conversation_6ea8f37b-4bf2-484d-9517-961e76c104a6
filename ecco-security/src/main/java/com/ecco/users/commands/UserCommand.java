package com.ecco.users.commands;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;
import java.util.UUID;

@Entity
@Table(name = "usr_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
public abstract class UserCommand extends BaseUserCommand {

    public UserCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime, long userId, @Nonnull String body,
                       Long userIdSubject) {
        super(uuid, remoteCreationTime, userId, body, userIdSubject);
    }

    /**
     * Required by JPA/Hibernate.
     */
    @Deprecated
    protected UserCommand() {
    }
}
