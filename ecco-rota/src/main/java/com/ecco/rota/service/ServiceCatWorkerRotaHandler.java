package com.ecco.rota.service;

import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.ServiceAgreementRepository;
import com.ecco.dom.IndividualUserSummary;
import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.dom.agreements.QDemandSchedule;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaParams;
import com.ecco.service.EventService;
import com.ecco.service.acls.CachedAclVisibilityService;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLQuery;
import org.joda.time.DateTime;

import java.util.*;
import java.util.stream.StreamSupport;

import static com.ecco.calendar.core.Recurrence.Status.*;
import static com.ecco.dao.DemandSchedulePredicates.*;
import static com.ecco.rota.service.WorkerResourceableRotaHandler.WorkerOption.*;

/**
 * Finds referrals and workers assigned to a number of service categorisation's.
 * This is the live rota for workers (so shows demand from clients and care runs).
 * Workers need to have permission for the service, referrals need to be on the service.
 */
public class ServiceCatWorkerRotaHandler extends WorkerResourceableRotaHandler {

    private static final String DEMAND_HANDLER_PREFIX = "svccats:"; // demand is the service's referrals
    private static final String RESOURCE_HANDLER_PREFIX = "workers:"; // resource is the service's workers

    private final CachedAclVisibilityService aclService;
    private final ServiceCategorisationRepository serviceCatRepository;
    private final FixedContainerRepository fixedContainerRepository;

    public ServiceCatWorkerRotaHandler(DemandScheduleRepository demandScheduleRepository,
                                       WorkerJobRepository workerJobRepository,
                                       ServiceCategorisationRepository serviceCatRepository,
                                       ServiceAgreementRepository serviceAgreementRepository,
                                       FixedContainerRepository fixedContainerRepository,
                                       RotaService rotaService,
                                       CachedAclVisibilityService aclService,
                                       EventService eventService) {
        super(demandScheduleRepository, workerJobRepository, serviceAgreementRepository, rotaService, eventService);
        this.serviceCatRepository = serviceCatRepository;
        this.fixedContainerRepository = fixedContainerRepository;
        this.aclService = aclService;
    }

    @Override
    public boolean canHandle(String resourceFilter, String serviceRecipientDemandFilter) {
        return resourceFilter.startsWith(RESOURCE_HANDLER_PREFIX) && serviceRecipientDemandFilter.startsWith(DEMAND_HANDLER_PREFIX);
    }

    @Override
    public List<Integer> findAllResourceServiceRecipientIds(RotaParams params) {
        Rota rota = new Rota(params.getStartDate(), params.getEndDate(), params.getResourceFilter(), params.getDemandFilter(), params.getLoadResource(), params.getLoadDemand());
        var resources = this.getResources(rota);
        return StreamSupport.stream(resources.spliterator(), false).map(WorkerJob::getServiceRecipientId).toList();
    }

    @Override
    public void populateRota(Rota rota) {
        if (rota.getLoadResource()) {
            // svccats resources look for usersWithAccessTo (on the csv's) with 'ROLE_CARER' with staff employed at rota date
            Iterable<WorkerJob> workerJobs = getResources(rota);

            addWorkersEntries(rota, workerJobs, ALLOCATED_APPOINTMENTS, AVAILABILITY, EVENTS_WITH_CATEGORY);
        }

        // svccats demand are the referrals on the service categorisation's but also the
        // service categorisation has a buildingId which is used to: 'bldgId -> getSrIdsForDemandSchedulesOfCareRunsInBuilding'
        // where the carerun is a child of the building, as normal

        // demand includes clients and careruns - to show on the live rota (workers as resources down the left)
        // we need to show demand that isn't already allocated (otherwise its loaded by the resource)
        if (rota.getLoadDemand()) {
            addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.getDemandScheduleWherePredicate(rota));
            // if we ARE using a specific demand srId then we will already be calling the demand from findAllAgreementsByScheduleDate
            // if we AREN'T using a specific demand srId then we also need to load the unallocated carerun demand (see a362461e)
            if (getDemandServiceRecipientId(rota.getDemandFilter()) == null) {
                addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.getDemandScheduleCareRunsWherePredicate(rota));
            }
        }
    }

    protected Iterable<WorkerJob> getResources(Rota rota) {
        // resourceFilter - either workers:all, or workers:srId
        Integer srId = BaseRotaHandler.getResourceServiceRecipientId(rota.getResourceFilter());

        if (srId != null) {
            return workerJobRepository.findAllByServiceRecipient_IdIn(List.of(srId));
        } else {
            var svcCatIds = getDemandServiceCategorisationIds(rota);
            var startDate = JodaToJDKAdapters.dateTimeToJdk(rota.getStartDate().toDateTimeAtStartOfDay());
            var usersWithAccessTo = svcCatIds.stream()
                    .map(svcCatId -> {
                        var cat = serviceCatRepository.findById(svcCatId).orElseThrow();
                        var svcId = cat.getServiceId();
                        var projId = cat.getProjectId();
                        return this.aclService.getUsersWithAccessTo(svcId, projId, "ROLE_CARER");
                    })
                    .flatMap(Collection::stream)
                    .distinct().toList();
            var contactIds = usersWithAccessTo.stream().map(IndividualUserSummary::getIndividualId).toList();
            var workerJobs = workerJobRepository.findAllStaffWithIndividualIdEmployedAt(contactIds, startDate);
            return workerJobs;
        }
    }

    // demandFilter is a csv of the services_projects id's, before a possible second ':'
    // eg demandFilter=svccats:1345,1223,12323,1212:23424234
    //    demandFilter=svccats:<csv>:<srId>
    public static List<Integer> getDemandServiceCategorisationIds(RotaParams rota) {
        var demandFilter = rota.getDemandFilter();
        Objects.requireNonNull(demandFilter);
        String[] svcCats = demandFilter.split(":")[1].split(",");
        return Arrays.stream(svcCats).map(Integer::parseInt).toList();
    }

    @Override
    public List<Integer> findAllAgreementSrIdsByScheduleDate(RotaParams params) {
        BooleanExpression wherePredicate = getDemandScheduleWherePredicate(params);
        BooleanExpression wherePredicateRuns = getDemandScheduleCareRunsWherePredicate(params);

        JPQLQuery<Integer> query = query(QDemandSchedule.demandSchedule)
                .where(wherePredicate.or(wherePredicateRuns))
                .select(QDemandSchedule.demandSchedule.agreement.serviceRecipientId)
                .distinct();

        return query.fetch();
    }


    @Override
    public List<ServiceAgreement> findAllAgreementsByScheduleDate(RotaParams params) {
        BooleanExpression wherePredicate = getDemandScheduleWherePredicate(params);
        BooleanExpression wherePredicateRuns = getDemandScheduleCareRunsWherePredicate(params);

        // load the agreements in one go
        // use joins to bring the disparate predicates together
        // in query-dsl 4, we could use JPAExpressions to avoid entityManager
        JPQLQuery<ServiceAgreement> query = query(QDemandSchedule.demandSchedule)
                .where(wherePredicate.or(wherePredicateRuns))
                .select(QDemandSchedule.demandSchedule.agreement)
                .distinct();

        return query.fetch();

        // NB find with projection produced an error:
        //      org.hibernate.hql.internal.ast.QuerySyntaxException: unexpected token: from near line 2, column 1 [select \nfrom com.ecco.dom.agreements.DemandSchedule demandSche
        /*var demandSchedules = demandScheduleRepository.findAllWithProjection(
                Projections.bean(QDemandSchedule.demandSchedule.agreement),
                wherePredicate.or(wherePredicateRuns));
        return demandSchedules.stream().distinct().toList();*/
    }

    @Override
    protected BooleanExpression getDemandScheduleWherePredicate(RotaParams rota) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().toDateTimeAtStartOfDay();

        var svcCatIds = getDemandServiceCategorisationIds(rota);
        var specificDemandSrId = getDemandServiceRecipientId(rota.getDemandFilter());
        return specificDemandSrId != null
                ? getWherePredicateForDemandSchedulesOfServiceRecipient(start, end, AppointmentSchedule.class, specificDemandSrId)
                : getWherePredicateForDemandSchedulesOfClientsInServiceCategorisations(start, end, AppointmentSchedule.class, svcCatIds);
    }

    protected BooleanExpression getDemandScheduleCareRunsWherePredicate(RotaParams rota) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().toDateTimeAtStartOfDay();
        var svcCatIds = getDemandServiceCategorisationIds(rota);
        //return getWherePredicate(start, end, AppointmentSchedule.class);
        return getWherePredicateForDemandSchedulesOfCareRunsInServiceCategorisations(start, end, AppointmentSchedule.class,
                svcCatIds, serviceCatRepository, fixedContainerRepository);
    }
}
