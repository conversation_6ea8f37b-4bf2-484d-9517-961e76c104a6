package com.ecco.servicerecipient;

import com.ecco.dom.contacts.AddressLike;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.infrastructure.hibernate.EntityDetailsSupplier;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.querydsl.core.annotations.QueryProjection;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.Transient;
import java.util.Map;
import java.util.UUID;

import static lombok.AccessLevel.NONE;

// DANGER: Don't put @Getter here
// NB CACHED in ServiceRecipientSummaryService but currently not used
// JsonIgnore because its extended by ReferralSummaryViewModel (see ServiceRecipientController#findOne) which is more than expected for ServiceRecipientActor
@JsonIgnoreProperties(ignoreUnknown = true)
@Configurable
@Getter
public class ServiceRecipientSummary implements EntityDetailsSupplier {

    @Transient
    @Autowired
    @Getter(NONE)
    @Setter(NONE)
    private transient ServiceCategorisationRepository serviceCategorisationRepository;

    {
        // This class is instantiated by Hibernate, so not a managed Spring bean.
        injectServices();
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    /**
     * Discriminator to indicate what kind of service recipient this is.
     * This is the database discriminator which is abstracted away from client side code.
     * Please use {@link #prefix} or {@link BaseServiceRecipient#getPrefix()}
     * NB This class is extended by view models, hence this property is protected and has JsonIgnore,
     * because it is the responsibility of the view model to translate this into getPrefix.
     */
    @Nonnull
    @JsonIgnore
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    public String discriminator;

    /** The human-readable status */
    public String statusMessageKey;

    /** NOTE: We would probably love to remove this as it's implementation specific
     * e.g. referral.client.contact.lastName + firstName, but is needed where we want to see a useful name
     * in visual support plan, and reports */
    @Nonnull
    public String displayName;

    public Long contactId;

    public String calendarId;

    // not an impl, so have to ignore - this isn't used anywhere as it breaks with a value
    @Nullable
    @JsonIgnore
    public AddressLike address;

    // NB only used in testing cache request currently
    public String addressCommaSep;

    /** Ignored for web API for the moment - just for internal use here */
    @JsonIgnore
    public Map<String, String> textMap;

    /**
     * Client-side representation of what type of service recipient this is. As per {@link BaseServiceRecipient#getPrefix()}.
     * NB This class is created directly as a view model, so we need the ability to specify the 'prefix' property here.
     * However, we can't specify Nonnull, since we don't want to access ReferralServiceRecipient just to translate 'rlfl' to 'r'
     */
    public String prefix;

    /**
     * referralId, workerId or buildingId - use prefix to work out which.
     * NB Not always provided - eg ServiceRecipientRepository findOne projection
     */
    public Long parentId;

    /** The service recipient ID (unique across bldg, hr, referral */
    @Nonnull
    public Integer serviceRecipientId;

    @Nullable
    public Integer parentServiceRecipientId;

    @Nullable
    public Long serviceTypeId;

    //@Nonnull - we might be importing from ReferralViewModel - so we can't enforce this
    public Integer serviceAllocationId;
    public Long serviceIdAcl; // for permissions server-side only
    public Long projectIdAcl; // for permissions server-side only

    @JsonIgnore // Do not use client-side - use workflow API instead
    @Nullable
    public Long currentTaskDefId;

    @JsonIgnore // Do not use client-side - use workflow API instead
    @Nonnull
    public Integer currentTaskIndex;

    public UUID dataProtectionSignedId;
//    public DateTime dataProtectionAgreementDate;
//    public Boolean dataProtectionAgreementStatus;

    public int loadConfigServiceTypeId() {
        // try to move away from getServiceAllocation()...
        // try to cache, or use AntiProxyUtils.identifier etc (but not in id)
        if (serviceTypeId != null) {
            return serviceTypeId.intValue();
        } else {
            // try to move away from getServiceAllocation()...
            return serviceCategorisationRepository.findServiceTypeId(serviceAllocationId);
        }
    }

    public ServiceRecipientSummary() {
    }

    @QueryProjection
    public ServiceRecipientSummary(@Nonnull String discriminator,
                                   @Nonnull Integer serviceRecipientId,
                                   @Nonnull Integer serviceAllocationId,
                                   @Nonnull Long serviceTypeId,
                                   @Nonnull Long serviceIdAcl,
                                   @Nullable Long projectIdAcl,
                                   @Nonnull Integer currentTaskIndex,
                                   @Nullable Long currentTaskDefId) {
        this.discriminator = discriminator;
        this.serviceRecipientId = serviceRecipientId;
        this.serviceTypeId = serviceTypeId;
        this.serviceIdAcl = serviceIdAcl;
        this.projectIdAcl = projectIdAcl;
        this.serviceAllocationId = serviceAllocationId;
        this.currentTaskIndex = currentTaskIndex;
        this.currentTaskDefId = currentTaskDefId;
    }

    // perhaps some DtoProxyBuilder could be used here, or @Builder(toBuilder=true)
    public void copy(@Nonnull ServiceRecipientSummary shallowClone) {
        shallowClone.discriminator = discriminator;
        shallowClone.prefix = prefix;
        shallowClone.serviceRecipientId = serviceRecipientId;
        shallowClone.serviceAllocationId = serviceAllocationId;
        shallowClone.serviceTypeId = serviceTypeId;
        shallowClone.calendarId = calendarId;
        shallowClone.contactId = contactId;
        shallowClone.parentId = parentId;
        shallowClone.serviceIdAcl = serviceIdAcl;
        shallowClone.projectIdAcl = projectIdAcl;
        shallowClone.displayName = displayName;
        shallowClone.address = address;
        shallowClone.addressCommaSep = addressCommaSep;
        shallowClone.textMap = textMap;
        // (as below) NOTE: Don't map task stuff for view model as it's internal to workflow
        //shallowClone.currentTaskIndex = currentTaskIndex;
        //shallowClone.currentTaskDefId = currentTaskDefId;
        shallowClone.dataProtectionSignedId = dataProtectionSignedId;
    }

    // called by entities who are, or who have, a ServiceRecipient
    //   - eg referral-dto.ts (extends ServiceRecipient), hr-dto.ts (StaffJobDto has a ServiceRecipient)
    // but not called by entities who refer to a serviceRecipientId
    //   - eg building-dto.ts, contract-dto.ts, group-support-dto.ts, incident-dto.ts
    // See BaseServiceRecipient implementations
    public ServiceRecipientSummary mapFrom(BaseServiceRecipient input) {

        discriminator = input.getDiscriminator();
        prefix = input.getPrefix();
        serviceRecipientId = input.getId();
        serviceAllocationId = input.getServiceAllocationId();
        serviceTypeId = input.getServiceTypeId();
        calendarId = input.getCalendarId();
        contactId = input.getContact() != null ? input.getContact().getId() : null;
        parentId = input.getParentId();

        serviceIdAcl = input.getPermissionServiceId(); // needed since ServiceRecipientBaseController does verifyAccess
        projectIdAcl = input.getPermissionProjectId(); // input.permissionServiceId input.permissionProjectId
        displayName = input.getDisplayName();
        address = input.getAddress();
        addressCommaSep = address != null ? address.toCommaSepString() : null;
        // NB this is not mapped in ReferralSummary
        textMap = input.getTextMap();

        // NOTE: Don't map task stuff for view model as it's internal to workflow
//        currentTaskIndex = input.getCurrentTaskIndex();
//        currentTaskDefId = input.getCurrentTaskId();

//        if (input.dataProtectionAgreementDate != null) {
//            dataProtectionAgreementStatus = input.getDataProtectionAgreementStatus();
//            dataProtectionAgreementDate = input.dataProtectionAgreementDate.toLocalDateTime();
//        }
        dataProtectionSignedId = input.getDataProtectionSignatureId();

        return this;
    }

    @JsonIgnore
    @Override
    public String getEntityName() {
        return "BaseServiceRecipient"; // because this is a representation of BaseServiceRecipient
    }

    @JsonIgnore
    @Override
    public Object getEntityId() {
        return serviceRecipientId;
    }
}
