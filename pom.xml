<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>org.eccosolutions</groupId>
    <artifactId>ecco-aggregator</artifactId>
    <packaging>pom</packaging>
    <version>4.0.0-SNAPSHOT</version>


    <modules>
        <module>fake-commons-logging</module>
        <module>parent-spring-boot</module>
        <module>parent</module>
        <module>ecco-infrastructure</module>
        <module>test-support</module>

        <module>ecco-int-api-default</module>
        <module>ecco-int-core</module>
        <module>ecco-int-northgate</module>
        <module>ecco-int-ql</module>
        <module>ecco-int-homemaster</module>

        <module>ecco-config</module>
        <module>ecco-messaging</module>
        <module>ecco-notifications</module>
        <module>ecco-security</module>

        <module>ecco-service-config</module>
        <module>ecco-servicerecipient</module>
        <module>ecco-contacts</module>
        <module>test-entities</module>
        <module>ecco-calendar-core</module>
        <module>ecco-security-core</module>
        <module>ecco-upload-dom</module>
        <module>ecco-upload-web</module>

        <module>ecco-security-ldap</module>

        <module>ecco-dom</module>
        <module>ecco-dao</module>
        <module>ecco-service</module>
        <module>ecco-evidence</module>
        <module>ecco-web</module>
        <module>ecco-reports</module>
        <module>ecco-incidents</module>
        <module>ecco-repairs</module>
        <module>ecco-buildings</module>
        <module>ecco-contracts</module>
        <module>ecco-finance</module>
        <module>ecco-hr</module>
        <module>ecco-group-support</module>
        <module>ecco</module>
        <module>ecco-submissions-sp</module>
        <module>ecco-offline</module>
        <module>ecco-calendar-cosmo</module>
        <module>ecco-calendar</module>
        <module>ecco-rota</module>
        <module>ecco-workflow</module>
        <module>ecco-workflow-activiti</module>
        <module>ecco-web-api</module>
        <module>ecco-webapi-boot</module>
        <module>ecco-data-client</module>

        <module>ecco-acceptance-tests</module>
    </modules>

</project>
