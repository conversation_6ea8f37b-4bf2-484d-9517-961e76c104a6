import {arrays, EccoDate, EccoDateTime, EccoTime} from "@eccosolutions/ecco-common";
import {
    AddedRemovedOptional,
    ArrayChange,
    BooleanChange,
    NumberChangeOptional,
    StringChangeOptional
} from "./command-dto";

/** Return the elements in `here` that are not in `butNotHere` */
export function diff<T extends number | string>(here: T[] = [], butNotHere: T[] = []): T[] {
    return here.filter(h => butNotHere.indexOf(h) < 0);
}

export function onlyInLeft<T>(left: T[], right: T[], equalsFunction: (a: T, b: T) => boolean) {
    return left.filter(
        leftValue => !right.some(rightValue => equalsFunction(leftValue, rightValue))
    );
}

export function hashCode(s: string) {
    return s.split("").reduce((a, b) => {
        a = (a << 5) - a + b.charCodeAt(0);
        return a & a;
    }, 0);
}

/** Turn into {from: [...], added: [...], removed: [...]} using diff each way */
export function asAddedRemoved<T extends string | number>(
    from: T[] = [],
    to?: T[]
): AddedRemovedOptional<T> | undefined {
    const added = diff(to, from);
    const removed = diff(from, to);
    return added.length == 0 && removed.length == 0
        ? undefined
        : {
              from: from.length > 0 ? from : undefined,
              added: added.length > 0 ? added : undefined,
              removed: removed.length > 0 ? removed : undefined
          };
}

/** return null if there is no change, and treat "" and null as equal for from and to params */
export function asStringChange(from: string | null | undefined, to: string | null | undefined): StringChangeOptional {
    from = (from == "") ? null : from;
    to = (to == "") ? null : to;

    if (to != from) {
        return {from: from ?? null, to: to ?? null};
    }
    return undefined; // reduces amount of JSON we store when there is no change
}

/**
 * @param from Either a string containing ISO-8601 yyyy-MM-dd, or an EccoDate instance or null
 * @param to Either a string containing ISO-8601 yyyy-MM-dd, or an EccoDate instance or null
 */
export function asDateChange(from: EccoDate | string | null, to: EccoDate | string | null): StringChangeOptional {
    const fromStr = from && (from instanceof EccoDate ? from.formatIso8601() : from);
    const toStr = to && (to instanceof EccoDate ? to.formatIso8601() : to);

    if (toStr != fromStr) {
        return {from: fromStr, to: toStr};
    }
    return undefined; // reduces amount of JSON we store when there is no change
}

export function asLocalTimeChange(from: EccoTime | null, to: EccoTime | null): StringChangeOptional {
    const fromStr = from && from.formatHoursMinutes();
    const toStr = to && to.formatHoursMinutes();
    if (toStr != fromStr) {
        return {from: fromStr, to: toStr};
    }
    return undefined; // reduces amount of JSON we store when there is no change
}

export function asLocalDateTimeChange(from: EccoDateTime | null, to: EccoDateTime | null): StringChangeOptional {
    const fromStr = from && from.formatIso8601();
    const toStr = to && to.formatIso8601();
    if (fromStr && fromStr.length != 23) {
        throw new Error("from should not have a timezone: " + fromStr);
    }
    if (toStr && toStr.length != 23) {
        throw new Error("to should not have a timezone: " + toStr);
    }

    if (toStr != fromStr) {
        return {from: fromStr, to: toStr};
    }
    return undefined; // reduces amount of JSON we store when there is no change
}

/** return null if there is no change */
export function asNumberChange(
    from: number | null | undefined,
    to: number | null | undefined
): NumberChangeOptional {
    if (to != from) {
        // exclude '0' from being falsy and turning into null
        return {from: from || from === 0 ? from : null, to: to || to === 0 ? to : null};
    }
    return undefined; // reduces amount of JSON we store when there is no change
}

/** return null if there is no change */
export function asBooleanChange(fromVal: boolean | null, toVal: boolean | null): BooleanChange {
    if (toVal != fromVal) {
        // false to null should be equivalent, so we capture it as undefined
        const falseToNullIgnore = !fromVal && toVal == null;
        if (!falseToNullIgnore) {
            return {from: fromVal, to: toVal};
        }
    }
    return undefined; // reduces amount of JSON we store when there is no change
}

export function asArrayChange<T>(from: T[] | null, to: T[] | null): ArrayChange<T> {
    if (from != null && to != null && arrays.equals(from, to) || from == to) {
        return undefined; // reduces amount of JSON we store when there is no change
    }
    return {from, to};
}