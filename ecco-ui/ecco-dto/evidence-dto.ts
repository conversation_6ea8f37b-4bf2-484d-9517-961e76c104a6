import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    NumberAddedRemoved,
    <PERSON><PERSON><PERSON>eOptional,
    StringChangeOptional,
    UpdateCommandDto
} from "./command-dto";
import {
    Decrypted,
    EccoDateTime,
    Encrypted,
    HateoasResource,
    SparseArray,
    UploadResult
} from "@eccosolutions/ecco-common";
import {ReferralSummaryDto} from "./referral-dto";
import {EventResourceDto, EventSnapshotDto} from "./calendar-dto";
import {RiskWorkPlainFields, RiskWorkSecretFields} from "./evidence-risk-dto";
import {
    QuestionAnswerSnapshotDto,
    QuestionnaireWorkDto,
    SnapshotPeriod
} from "./evidence/questionnaire-dto";
import {StaffDto} from "./hr-dto";
import {ReviewChoices} from "./evidence/domain";
import {SessionData} from "./session-data/feature-config-domain";
import {evidenceTypes, TaskDefinitionType, TaskEvidenceType} from "./service-config-dto";
import {ServiceType} from "./service-config-domain";
import {Client} from "./client-dto";

export enum DeleteType {
    REQUEST = 0, REVOKE = 1, DELETE = 2
}

export const statusMessages: { title: string; iconPath: string }[] = [
    {title: "", iconPath: ""},
    {title: "relevant", iconPath: "plus24"},
    {title: "not relevant", iconPath: "plus-faded24"},
    {title: "achieved", iconPath: "star24"},
    {title: "not achieved", iconPath: "star-faded24"},
    {title: "achieved and still relevant", iconPath: "tick"}
];

/** Data-transfer object representing a task within a workflow.
 * This is different to TaskDto in workflow-dto.
 * This represents the fuller linear task.
 *
 * This interface must match the Java class TaskStatusViewModel, from TaskStatus. */
export type TaskStatus = TaskStatusPlainFields & TaskStatusSecretFields;

export interface TaskStatusPlainFields {
    /** The task Uuid */
    taskInstanceUuid: string;

    /** The service recipient ID of the referral corresponding to this task. */
    serviceRecipientId: number;
}
export interface TaskStatusSecretFields {
    taskDefinitionId: number;
    taskDefName: string;
    serviceAllocationId: number;

    /** The instant this task was created (currently server side) */
    created: string;
    /** The instant this task was completed (currently client side) */
    completed: string;
    /** The status of the completed task as a list def (completed / parentinactive) */
    completedStatusId: number;
    /** The description of the task */
    description: string;
    /** The due date and time of the task in Iso8601 */
    dueDate: string;
    /** The id of the user assigned to the task */
    assignee: number;
    /** The display name of the user assigned to the task */
    assigneeDisplayName: string;
    /** The email of the user assigned to the task */
    assigneeEmail: string;
    /** The security group of users that this task is relevant for */
    relevantGroup: number;

    // populated client side for TaskStatus
    referralSummary: ReferralSummaryDto; // a Referral instance of ServiceRecipient for convenience
    lastAudit: BaseServiceRecipientCommandDto; // the respective task audit, the very latest
    // populated client side
}

export interface CommentFormPlainFields {

    /** The id (UUID) of the signature if this work has been signed, if not signed, this is null. */
    signatureId: string;
}

export interface CommentFormSecretFields {
    /** A comment that annotates this support work. The comment was written by
     * the person named in {@link #authorDisplayName}. */
    comment: string;

    /**
     * The UTC datetime the work entry was created - ISO8601 extended format (i.e. ends in Z)
     * This amounts to the created time on the client. This class is derived from a snapshot
     * not a command (eg see SupportWorkRepositoryImpl.findAllWorkSummaryByServiceRecipientId)
     * but the work 'created' gets set from the command's timestamp
     * (eg see SupportCommentCommandHandler.createNewWork or GoalCommandHandler.findOrCreateWork)
     */
    createdDate: string;

    /** The datetime the work was carried out - ISO8601 extended format.
     * This is stored as zoneless time in database (so 7am BST is stored as 7am) */
    workDate: string;

    /** The type of work (e.g. meeting, phone call) */
    commentTypeId: number;

    /** Number of minutes spent on the support work */
    minsSpent: number;
}

// see EvidenceAttachmentViewModel.java
export interface EvidenceAttachment extends HateoasResource {
    // see TaskEvidenceType (ts) or ReferralAspect.Type (java) - but this doesn't differentiate evidence tasks
    taskEvidenceType: TaskEvidenceType;
    workUuid: string;
}

export interface CommentFormFields extends CommentFormPlainFields, CommentFormSecretFields {}

export interface BaseWorkPlainFields extends CommentFormPlainFields {
    /** The support work Uuid */
    id: string;

    /** The service recipient ID of the referral corresponding to this support work. */
    serviceRecipientId: number;

    /** The allocation this work was recorded against */
    serviceAllocationId: number;

}

export interface BaseWorkSecretFields extends CommentFormSecretFields {
    /** Indicate if this work is marked for delete */
    requestedDelete: boolean;

    /** The parent ID or code of the referral corresponding to this support work. */
    parentCode: string;

    /** r, b or w for what type of service recipient it is */
    parentPrefix: string;

    /** The display name of the person who recorded this evidence. */
    authorDisplayName: string;

    /** The (workflow) task that was being performed when this evidence was recorded.
     *  Expect things like "needsAssessment", "needsReduction", "needsAssessmentReductionReview" */
    taskName: string;

    attachments?: UploadResult[];

    // populated client side for TaskStatus
    // a Referral instance of ServiceRecipient for convenience
    referralSummary?: ReferralSummaryDto;
    // populated client side
}
/** This interface must match the Java class com.ecco.webApi.evidence.BaseWorkViewModel. */
export type BaseWork = BaseWorkPlainFields & BaseWorkSecretFields;

/** A faked Record<string, object> that pretends to have _isCustomForm: true present, so that we can detect if we've
 * assigned some other value to what is effectively 'object' */
export type CustomFormFields = {_isCustomForm: true} & Record<string, object | string | number | boolean> // So we can pretend this is the type for sanity's sake

export interface FormEvidenceSecretFields<FORM extends CustomFormFields>
    extends BaseWorkSecretFields {
    form: FORM;
    evidenceGroupKey: string;
    formDefinitionUuid: string;
}

/** This interface must match Java class com.ecco.webApi.evidence.EvidenceFormWorkViewModel */
export type FormEvidence<FORM extends CustomFormFields> = BaseWorkPlainFields &
    FormEvidenceSecretFields<FORM>;

export type EncryptedFormEvidence<FORM extends CustomFormFields> = Encrypted<
    FormEvidenceSecretFields<FORM>,
    BaseWorkPlainFields
>;


export interface BaseOutcomeBasedWorkPlainFields extends BaseWorkPlainFields {

    meetingStatusId?: number;

    clientStatusId?: number;

    locationId?: number;

    /** related calendar event id */
    eventId?: string;
}

export interface BaseOutcomeBasedWorkSecretFields extends BaseWorkSecretFields {
    mileageTo?: number;

    mileageDuring?: number;

    /** Minutes spent travelling to/from client */
    minsTravel?: number;

    /** Action ids of actions linked to this work (currently only populated for support work) */
    associatedActions?: number[];

    /** related calendar event - populated client side */
    event?: EventResourceDto;

    eventSnapshot?: EventSnapshotDto;
}

/** This interface must match the Java class com.ecco.webApi.evidence.BaseWorkViewModel. */
export interface BaseOutcomeBasedWork
    extends BaseOutcomeBasedWorkPlainFields,
        BaseOutcomeBasedWorkSecretFields {}

export interface SupportWorkPlainFields extends BaseOutcomeBasedWorkPlainFields {
    clientStatusId?: number;
}

export interface FlagArea {
    label: string;
    evidenceDef: EvidenceDef;
    flagSnapshots: FlagEvidenceDto[];
}
/** see FlagViewModel.java */
export interface FlagEvidenceDto {
    id: number;
    flagId: number;
    value: boolean;
}


// ** COPY ** of RiskFlags...
// for the support flags report
export interface EvidenceFlagsPlainFields {
    id: number;
    serviceRecipientId: number;
    workUuid: string;
    flagId: number;
}
export interface EvidenceFlagsSecretFields {
    value: boolean;
    workDate: string;
    //work: RiskWorkEvidenceDto; // probably without everything - see EvidenceFlagsToViewModel
}
export interface EvidenceFlags extends EvidenceFlagsPlainFields, EvidenceFlagsSecretFields {
    // populated client side for reporting
    referralSummary?: ReferralSummaryDto;
    client?: Client;
    // populated client side for reporting
}
export interface SupportFlags extends EvidenceFlags {
    work: SupportWork; // probably without everything - see EvidenceFlagsToViewModel
    // populated client side for reporting
    questionnaireWork: QuestionnaireWorkDto;
    latestWorkComment: string;
    // populated client side for reporting
}

export interface SupportWorkSecretFields extends BaseOutcomeBasedWorkSecretFields {
    /** Changes to actions on the client's support plan made as part of this
     * support work. */
    actions: SupportAction[];

    /**
     * Changes to answers as part of this support work.
     * A duplicate of QuestionnaireEvidenceViewModel.answers (java) and QuestionnaireWorkDto (ts), but we may be loading this EvidenceSupportWork
     * without knowing the type of support it is, and we have no discriminator - so we need a way to hold answers also.
     */
    answers?: QuestionAnswerSnapshotDto[];

    flags: FlagEvidenceDto[];

    riskManagementRequired?: boolean;

    riskManagementHandled?: string; // uuid

    // populated client side
    // a Referral instance of ServiceRecipient for convenience
    referralSummary?: ReferralSummaryDto;
    // populated client side
}

/** Evidence of a piece of work done to support a client, and any corresponding
 * changes to the client's support plan.
 *
 * This interface must match the Java class com.ecco.webApi.evidence.SupportWorkViewModel. */
export type SupportWork = SupportWorkPlainFields & SupportWorkSecretFields;

/**
 * The status of the smart step.
 * The existing GenericTypeDtoAbstract displays the icons according to the status and screen but it uses 2 further status to help it do this.
 * Occasionally these extra statuses get persisted - for instance, when changing a target date on a support plan the status gets saved
 * as unAchieved even though the step is still just 'isRelevant' (or 'WantToAchieve'). The current 'GenericType' screens handle
 * this oddity, and can handle the correct expectation too - which is why we only want the correct statuses here.
 */
export enum SmartStepStatus {
  WantToAchieve = 1,
  // the appearance of this data is historical, it does appear that smart steps could be un-selected in early ecco
  // it gets treated now as if no smart step had happened
  NoLongerWanted = 2,
  Achieved = 3,
  // although code refers to unachieved it isn't well supported in all areas
  // and its appearance in the database is by accident and only sparadic
  // it can occur when on a support plan and change the target date of a 'faded star'
  // the status is saved as its presented on the page - which is unachieved
  Unachieved = 4,

  AchievedAndStillRelevant = 5
}

// see GenericTypeDtoAbstract.java
export const SmartStepStatusName: SparseArray<string> = {};
SmartStepStatusName[SmartStepStatus.WantToAchieve] = "relevant";
SmartStepStatusName[SmartStepStatus.NoLongerWanted] = "not relevant";
SmartStepStatusName[SmartStepStatus.Achieved] = "achieved";
SmartStepStatusName[SmartStepStatus.Unachieved] = "relevant";
SmartStepStatusName[SmartStepStatus.AchievedAndStillRelevant] = "achieved and still relevant";

// when the steps aren't chosen as 'relevant' the language could do with changing also
export const SmartStepStatusPlanName: SparseArray<string> = {};
SmartStepStatusPlanName[SmartStepStatus.WantToAchieve] = "in progress";
SmartStepStatusPlanName[SmartStepStatus.NoLongerWanted] = "stopped progress";
SmartStepStatusPlanName[SmartStepStatus.Achieved] = "achieved";
SmartStepStatusPlanName[SmartStepStatus.Unachieved] = "in progress";
SmartStepStatusPlanName[SmartStepStatus.AchievedAndStillRelevant] = "achieved, in progress again";


/** The symbol to show on the screen - see SmartStepStatusControl */
export enum SmartStepDisplaySymbol {
  FadedPlus = 1,
  FullPlus = 2,
  FadedStar = 3,
  FullStar = 4,
  Tick = 5
}
/** The type of page we are on */
export enum EvidencePageType {
  assessment = 1,
  reduction = 2,
  // like assessmentReduction, but walks-through the tabs
  review = 3,
  // some systems don't have a needs assessment - they go straight to a combined page
  // which is not quote a review, since it doesn't walk through the tabs
  assessmentReduction = 4,
  /** For pages that don't show any outcomes */
  commentsOnly = 6,


  auditonly = 5,
  // NB not really needed here, but allows us to pass validation on BaseEvidenceForm constructor - 'actAs' determines a questionnaire page
  questionnaireOnly = 7,
  customFormOnly = 8
}

/**
 * Used to convert a task name into hard coded values (bypassing config)
 * - EvidencePageType is used as a rendering switch
 * - TaskName is not used after this except to get configuration, and for the command
 * - EvidenceGroup is also passed through to the command (as a string)
 */
export class EvidenceDef {
    /**
     * @param taskName
     * @param evidencePageType - Nullable means no default
     * @param evidenceGroup
     * @param reviewChoices
     */
    constructor(
        private taskName: string,
        private evidencePageType: EvidencePageType | null,
        private evidenceGroup: EvidenceGroup,
        private reviewChoices?: ReviewChoices
    ) {}

    public static isReview(taskName: string) {
        // TODO don't match by name for task definitions id > 1000
        return taskName.indexOf("Review") > -1;
    }

    public static taskType(
        sessionData: SessionData,
        taskName: string
    ): TaskDefinitionType | undefined {
        return sessionData.getTaskDefinitionByName(taskName)?.type;
    }

    public static taskEvidenceType(
        sessionData: SessionData,
        taskName: string
    ): TaskEvidenceType | null {
        const anyType = sessionData.getTaskDefinitionByName(taskName)?.type;
        return evidenceTypes.filter(t => t == anyType).length > 0
            ? <TaskEvidenceType>anyType
            : null;
    }

    public static isRisk(type: TaskEvidenceType | null) {
        return type == "EVIDENCE_RISK";
    }

    public static isSupport(type: TaskEvidenceType | null) {
        return type == "EVIDENCE_SUPPORT";
    }

    public static isQuestionnaire(type: TaskEvidenceType | null) {
        return type == "EVIDENCE_QUESTIONNAIRE";
    }

    public static isChecklist(type: TaskEvidenceType | null) {
        return type == "EVIDENCE_CHECKLIST";
    }

    public static isRota(type: TaskEvidenceType | null) {
        return type == "EVIDENCE_ROTA";
    }

    public static isCustomForm(type: TaskEvidenceType | null) {
        return type == "EVIDENCE_CUSTOMFORM";
    }

    public static isAuditOnly(type: TaskDefinitionType) {
        return type == "AUDITONLY";
    }

    public static isDedicated(type: TaskDefinitionType) {
        return type == "DEDICATED_TASK";
    }

    public static isAgreement(type: TaskDefinitionType) {
        return type == "AGREEMENT";
    }

    /**
     * Mimics ReferralAspectServiceImpl, particularly findGroupFromGroupName
     */
    public static fromTaskName(
        sessionData: SessionData,
        serviceType: ServiceType | null,
        taskName: string,
        reviewChoices?: ReviewChoices
    ) {
        let evidenceDef: EvidenceDef | null = null;

        if (!taskName) {
            throw new Error("We don't know how to handle a blank task/referralAspect");
        }

        // older commands can be loaded (eg deletion requests) and then fail because of this name clarification
        if (taskName == "groupActivities") {
            taskName = "groupSupport";
        }

        const type = this.taskEvidenceType(sessionData, taskName);
        // if (!type) {
        //     throw new Error("We don't know how to handle a blank task/referralAspect type");
        // }
        const typeNonEvidence = this.taskType(sessionData, taskName)!!;

        if (this.isRisk(type)) {
            // TODO don't match by name for task definitions id > 1000
            if (taskName.indexOf("threatAssessmentReduction") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessmentReduction,
                    EvidenceGroup.threat
                );
            } else if (taskName.indexOf("threatAssessment") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessment,
                    EvidenceGroup.threat
                );
            } else if (taskName.indexOf("threatReduction") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.reduction,
                    EvidenceGroup.threat
                );
            } else {
                // any other page configured assumes the needs grouping
                const evidencePageType = serviceType!.getEvidenceActAs(taskName);
                evidenceDef = new EvidenceDef(taskName, evidencePageType, EvidenceGroup.threat);
            }
        } else if (this.isSupport(type) || this.isRota(type)) {
            // TODO don't match by name for task definitions id > 1000
            if (taskName.indexOf("groupSupport") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessmentReduction,
                    EvidenceGroup.needs
                );
            } else if (taskName.indexOf("groupActivities") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessmentReduction,
                    EvidenceGroup.needs
                );
            } else if (EvidenceDef.isReview(taskName)) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.review,
                    EvidenceGroup.needs
                );
            } else if (taskName.indexOf("AssessmentReduction") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessmentReduction,
                    EvidenceGroup.needs
                );
            } else if (taskName.indexOf("Assessment") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessment,
                    EvidenceGroup.needs
                );
            } else if (taskName.indexOf("Reduction") > -1) {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.reduction,
                    EvidenceGroup.needs
                );
            } else if (taskName == "rotaVisit") {
                // TODO NEEDS group ???
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.reduction,
                    EvidenceGroup.needs
                );
            } else if (taskName == "engagementComments") {
                // was EvidenceGroup.engagementComments
                // needs to match EvidenceGroup.java
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessmentReduction,
                    EvidenceGroup.needs
                );
            } else if (taskName == "needsAttachment") {
                // a variation on needsReduction
                evidenceDef = new EvidenceDef(
                    taskName,
                    // TODO commentOnly?
                    EvidencePageType.reduction,
                    EvidenceGroup.needs
                );
            } else if (taskName == "supportStaffNotes") {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.commentsOnly,
                    new EvidenceGroup("supportStaffNotes")
                );
            } else if (taskName == "managerNotes") {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.commentsOnly,
                    new EvidenceGroup("managerNotes")
                );
            } else {
                // any other page configured assumes the needs grouping
                const evidencePageType = serviceType!.getEvidenceActAs(taskName);
                evidenceDef = new EvidenceDef(taskName, evidencePageType, EvidenceGroup.needs);
            }
        } else if (this.isChecklist(type)) {
            // TODO don't match by name for task definitions id > 1000
            if (taskName == "needsChecklist") {
                // a variation on needsReduction
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.reduction,
                    EvidenceGroup.checklist
                );
            } else if (taskName == "dailyRoutines") {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessment,
                    EvidenceGroup.checklist
                );
            } else if (taskName == "dietaryIntake") {
                evidenceDef = new EvidenceDef(
                    taskName,
                    EvidencePageType.assessment,
                    EvidenceGroup.checklist
                );
            } else {
                // any other page configured assumes the needs grouping
                const evidencePageType = serviceType!.getEvidenceActAs(taskName);
                evidenceDef = new EvidenceDef(taskName, evidencePageType, EvidenceGroup.checklist);
            }
        } else if (this.isQuestionnaire(type)) {
            const task = sessionData.getTaskDefinitionByName(taskName)!;
            const evidenceGroup = serviceType!.getTaskDefinitionSetting(
                taskName,
                "sourcePageGroup"
            );
            evidenceDef = new EvidenceDef(
                taskName,
                EvidencePageType.questionnaireOnly,
                EvidenceGroup.fromName(evidenceGroup || task.name)
            );
        } else if (this.isCustomForm(type)) {
            const task = sessionData.getTaskDefinitionByName(taskName)!;
            const evidenceGroup = serviceType!.getTaskDefinitionSetting(taskName, "taskNameGroup");
            evidenceDef = new EvidenceDef(
                taskName,
                EvidencePageType.customFormOnly,
                EvidenceGroup.fromName(evidenceGroup || task.name)
            );
        } else if (this.isDedicated(typeNonEvidence)) {
            if (taskName == "referralDetails") {
                evidenceDef = new EvidenceDef(
                        taskName,
                        EvidencePageType.customFormOnly,
                        EvidenceGroup.referralDetails
                );
            }
            if (taskName == "waitingListCriteria") {
                evidenceDef = new EvidenceDef(
                        taskName,
                        EvidencePageType.customFormOnly,
                        EvidenceGroup.waitingListCriteria
                );
            }
        } else if (this.isAuditOnly(typeNonEvidence)) {
            evidenceDef = new EvidenceDef(
                taskName,
                EvidencePageType.auditonly,
                EvidenceGroup.auditonly
            );
        } else if (this.isAgreement(typeNonEvidence)) {
            evidenceDef = new EvidenceDef(
                taskName,
                EvidencePageType.customFormOnly,
                EvidenceGroup.fromSameTaskName(taskName)
            );
        }

        if (!evidenceDef) {
            throw new Error("We don't know how to handle task/referralAspect named: " + taskName);
        }

        if (reviewChoices) {
            evidenceDef.setReviewChoices(reviewChoices);
        }

        return evidenceDef;
    }

    public getTaskName() {
        return this.taskName;
    }

    public getEvidenceGroup() {
        return this.evidenceGroup;
    }

    public getEvidencePageType() {
        return this.evidencePageType;
    }

    public getReviewPage() {
        return this.reviewChoices == null ? null : this.reviewChoices.dto.reviewPage;
    }
    public getReviewId() {
        return this.reviewChoices == null ? null : this.reviewChoices.dto.incompleteReviewId;
    }
    public setReviewPage(reviewPage: number) {
        if (this.reviewChoices) {
            this.reviewChoices.dto.reviewPage = reviewPage;
        }
    }
    public setReviewChoices(reviewChoices: ReviewChoices) {
        this.reviewChoices = reviewChoices;
    }
    public withPageType(pageType: EvidencePageType) {
        this.evidencePageType = pageType || this.evidencePageType;
        return this;
    }

    /** True if this is a page type where we show all actions such as assessment or assessmentReduction */
    public showAllActions() {
        return (
            this.evidencePageType == EvidencePageType.assessment ||
            this.evidencePageType == EvidencePageType.assessmentReduction
        ); // HERE:
    }
}

/** The group of data we are operating on: needs, threat, audits, or the name of the task */
export class EvidenceGroup {
    public static needs = new EvidenceGroup("needs");
    public static threat = new EvidenceGroup("threat");
    public static auditonly = new EvidenceGroup("auditonly");
    public static checklist = new EvidenceGroup("checklist");
    public static hactQuestionnaire = new EvidenceGroup("hactQuestionnaire");
    public static referralDetails = new EvidenceGroup("referralDetails");
    public static waitingListCriteria = new EvidenceGroup("waitingListCriteria");
    public static emergencyDetails = new EvidenceGroup("emergencyDetails");

    public static fromName(groupNme: string) {
        return new EvidenceGroup(groupNme);
    }

    /**
     * Separate method to indicate a bit of a hack, where the taskName and evidenceGroup will be the same (at least for now).
     * Ideally, it would be: EvidenceDef.fromTaskName(sessionData, serviceType, taskName);
     */
    public static fromSameTaskName(groupNme: string) {
        return new EvidenceGroup(groupNme);
    }

    constructor(public name: string) {}
}

export interface BaseActionInstance extends SupportAction {}
/** A change to a SMART step on a client's support plan, made as part of a piece
 * of SupportWork.
 *
 * This interface must match the Java class com.ecco.webApi.evidence.SmartStepViewModel. */
export interface SupportAction {
    /** The ID for this change. */
    id: number | null;

    /** The support work Uuid */
    workId: string | null;
    /** The datetime the work was carried out - ISO8601 extended format */
    workDate: string;

    /** The expiry date of the SMART step. */
    expiryDate: string | null;

    /**
     * The target date of the SMART step, or the next due dateTime (in the context of care)
     */
    targetDateTime: string | null;

    /** The schedule for future target dates - CRON-like expression */
    targetSchedule?: string;

    /** The SMART step definiton name for display purposes */
    name: string | null;

    /** Optional specific goal name for the client on this SMART step. */
    goalName: string | null;

    /** Optional specific detail on how to achieve the goal for the client on this SMART step. */
    goalPlan: string | null;

    /** The status of the SMART step.
     *
     * 0 - unsetRelevant
     * 1 - isRelevant
     * 2 - notRelevant
     * 3 - achieved
     * 4 - unAchieved,
     * 5 - achievedAndStillRelevant */
    status: SmartStepStatus;

    /** Whether the status was changed in this piece of work */
    statusChange: boolean;

    /** Score 0-10 for how well someone feels they have progressed */
    score: number | null;

    /** The list def entry recorded against this smart step  */
    statusChangeReasonId: number | null;

    /** Unique action instance - allows multiple/repeating smart steps.  Usually non-null. Use null! for exceptions */
    actionInstanceUuid: string;

    /** action instance parent reference, if any - allows hierarchical smart steps */
    parentActionInstanceUuid: string | null;

    /** The hierarchy level of the smart step (0 - root, 1 - inset 1, 2 - inset 2... */
    hierarchy: number | null;

    /** The order of the instance within its root hierarchy (0 - based) */
    position: string | null;

    /** Entity id for definition of the Action */
    actionId: number;

    /** Entity id for definition of the ActionGroup - avail when read from server, but not set client side */
    actionGroupId?: number;

    /** Entity id for definition of the Outcome - avail when read from server, but not set client side */
    outcomeId?: number;

    /** The score (1-5) of the likelihood */
    likelihood?: number;

    /** The score (1-5) of the likelihood */
    severity?: number;

    hazard?: string;

    intervention?: string;

    // client-side via commands
    lastUpdate?: EccoDateTime;
    statusChangeComment?: string;
    // client-side
}

//noinspection JSUnusedGlobalSymbols
/** A change to a SMART step activity on a client's support plan, made as part of a piece
 * of SupportWork.
 *
 * This interface must match the Java class com.ecco.webApi.evidence.SupportActivityViewModel. */
export interface SupportActivity {
    /**
     * The set of SMART steps that the activitySchedule refers to
     */
    smartStepIds: number[];

    /**
     * The activity schedule that defines the event or repeatable event which supports these SMART steps
     */
    activitySchedule: ActivitySchedule;
}

/** A defintion of an activity - its name and schedule
 *
 * This interface must match the Java class com.ecco.webApi.evidence.ActivityScheduleViewModel. */
export interface ActivitySchedule {
    /**
     * id of this entity
     */
    id: number;

    /**
     * the agreement this schedule relates to
     */
    agreementId: number;

    /**
     * start date of the activity
     */
    start: string;

    /**
     * the type/name of the activity
     */
    activityType: string;

    /**
     * days the activity is appropriate for (Sun 1...Sat 7)
     */
    days: number[];

    /**
     * time the activity takes place
     */
    time: string;

    /**
     * end date the activity ends
     */
    end: string;

    /**
     * duration of the event
     */
    agreedDurationInMinutes: number;
}


export interface SupportSmartStepsSnapshotPlainFields {

    serviceRecipientId: number;

    /** the evidence group */
    evidenceGroupKey: string;

    /** The id of the parent, e.g. referralId or workerId */
    parentId: number; // TODO: Remove
}

export interface SupportSmartStepsSnapshotSecretFields {
    // populated client side
    snapshotPeriod: SnapshotPeriod;
    referralSummary: ReferralSummaryDto;
    staff: StaffDto;
    // populated client side

    latestActions: SupportAction[];
    latestFlags: FlagEvidenceDto[];
}

/**
 * Generic smart step - threat/support etc.
 * NB The SmartStep dto's could be combined (see RiskActionEvidenceDto and SupportAction), but we choose to keep separate
 * and selectively use this, where required.
 */
export interface BaseActionInstanceSnapshotDto extends SupportSmartStepsSnapshotDto {}

/** Data-transfer object representing the latest snapshot for an evidenceGroup
 *
 * This interface must match the Java class com.ecco.webApi.evidence.SupportSmartStepsSnapshotViewModel. */
export type SupportSmartStepsSnapshotDto = SupportSmartStepsSnapshotPlainFields & SupportSmartStepsSnapshotSecretFields;

/**
 * Data-transfer object representing the options when going to a review
 * This interface must match the Java class com.ecco.webApi.viewModels.ReviewChoicesViewModel.
 */
export interface ReviewChoicesDto {

    /** An incompleteReviewId indicating that we must progress or complete this review */
    incompleteReviewId: number;

    /** The page/tab of the current review
     * which is simply the tab-index saved on the review */
    reviewPage: number;

    /** The date the current review was started with */
    currentReviewDate: string;

    /** The date the last review was started with - used for validation so the user  */
    lastReviewDate: string;

    /** The next review date that the user can select */
    nextReviewDate: string;

    /** The number of risks that are outstanding */
    threatsOutstanding: number;

}

// matches com.ecco.webApi.evidence.QuestionAnswerCommandViewModel
export interface QuestionAnswerCommandDto extends BaseServiceRecipientCommandDto {
    operation: string;
    workUuid: string;
    questionDefId: number;
    answerChange?: StringChangeOptional;
}


export enum AttendanceStatus {START, END}

/** matches com.ecco.webApi.evidence.AssociatedContactCommandViewModel */
export interface AssociatedContactCommandDto extends BaseServiceRecipientCommandDto {

    /** The uuid of the piece of work this relates to */
    workUuid: string;

    contactId: number;

    /** The attendance status */
    attendanceStatus: AttendanceStatus;

    /**
     * The eventId that the work is for (eg 'rota visit')
     * Optional
     */
    eventId: string;

    /**
     * The location of the contact at this (created) moment in time.
     */
    location: LocationDto;
}

export interface CommandDtoServer {
    /** START properties populated when retrieving the commands back from the server */
    displayName?: string;
    latestReferralCode?: string;
    latestClientCode?: string;
    latestReferralId?: number;
    latestClientId?: number;
    latestServiceAllocationId?: number;
    taskName?: string; // name of a related task (i.e. workflow task or 'screen')
    /** END properties populated when retrieving the commands back from the server */
}

export interface BaseServiceRecipientCommandDto extends CommandDto, CommandDtoServer {
    /** The service recipient ID of the referral/worker/building corresponding to this support work. */
    serviceRecipientId: number;

    evidenceTask?: string; // taskName not used on deletions - se DeleteEvidenceCommand<ViewModel>.evidenceName
    taskHandle?: string; // represents a workflow task which may or may not be persisted (used by workflow tasks)
    taskInstanceId?: string; // a persisted task id - that can be identified by activiti or linear (used by editing task commands)
    areaDefId?: number;
    actionDefId?: number;
    workUuid?: string;
}

/** Command-based data-transfer object representing a change to form data
 *
 * Must match the Java class com.ecco.webApi.evidence.EvidenceFormSnapshotCommandViewModel. */
export interface FormEvidenceCommandDto extends BaseServiceRecipientCommandDto {
    /** 'add' or 'update' - but currently only used client-side to help the flow of adding or editing a work comment */
    operation: "add" | "update" | "delete";

    /** The evidence group this relates to */
    evidenceGroup: string;

    workUuid: string;

    workDate: StringChangeOptional;

    jsonPatch: JsonPatch;

    /** The form that was shown to the user */
    formDefinitionUuid: string;
}

export type JsonPatch = {}[];

/** Matches DeleteRequestEvidenceCommandViewModel */
export interface DeleteEvidenceRequestCommandDto extends CommandDto {
    /** The type of evidence - eg hr/support/risk */
    discriminator: string;

    /** The service recipient ID of the referral */
    serviceRecipientId: number;

    /** The uuid of the piece of work this relates to */
    workUuid: string;

    /** The evidence group this relates to */
    evidenceGroup: string;

    /** The evidence task this relates to */
    evidenceTask: string;

    /** Whether we are trying to revoke a previous delete request */
    revoke: boolean;

    /** The reason for this request */
    reason: string;

    /** arbitrary data we can show as being relevant to this command */
    jsonViewModel: string;
}

export interface DeleteEvidenceCommandDto extends CommandDto {

    /** The service recipient ID of the referral */
    serviceRecipientId: number;

    /** The uuid of the piece of work this relates to */
    workUuid: string;

    /** The evidence group this relates to */
    evidenceGroup: string;

    /** The evidence task this relates to */
    evidenceTask: string;

    /** The reason for this request */
    reason: string;

    /** The deletion request to act upon */
    requestDeletionUuid: string;

    /** arbitrary data we can inspect details on deletions not created with commands */
    jsonViewModel: string;
}

export interface RedAmberGreen {
    label: string;
    value: number;
}

export const redAmberGreens: RedAmberGreen[] = [
    {label: 'unknown', value: -1},
    {label: 'red', value: 0},
    {label: 'amber', value: 1},
    {label: 'green', value: 2},
    {label: 'none', value: 3}
];

export interface AreaUpdateCommandDto extends BaseServiceRecipientCommandDto {
    areaDefId: number;
    workUuid: string;
    levelChange?: NumberChangeOptional;
    triggerChange?: StringChangeOptional;
    controlChange?: StringChangeOptional;
}


/**
 * Capture the changes per Goal as these are the more detailed.
 * Matches com.ecco.webApi.evidence.GoalUpdateCommandViewModel.
 */
export interface GoalUpdateCommandDto extends BaseServiceRecipientCommandDto {
    operation: string;
    workUuid: string;
    eventId?: string;
    actionInstanceUuid?: string;
    parentActionInstanceUuid?: string; // always the rootUuid
    positionChange?: StringChangeOptional;
    hierarchyChange?: NumberChangeOptional;
    statusChange?: NumberChangeOptional;
    isRelevant?: boolean;
    forceStatusChange?: boolean;
    expiryDateChange?: StringChangeOptional;
    targetDateChange?: StringChangeOptional;
    targetScheduleChange?: StringChangeOptional;
    goalNameChange?: StringChangeOptional;
    goalPlanChange?: StringChangeOptional;
    scoreChange?: NumberChangeOptional;
    statusChangeReason?: NumberChangeOptional;
    plannedDateTime?: string;
    annotationChange?: {[key: string]: StringChangeOptional};
    likelihoodChange?: NumberChangeOptional;
    severityChange?: NumberChangeOptional;
    triggerChange?: StringChangeOptional;
    controlChange?: StringChangeOptional;
}

/**
 * Data-transfer object representing a command to add or remove an association
 * between a Goal and an Activity Type.
 *
 * This command is POSTed to /api/service-recipients/{serviceRecipientId}/activityTypes/
 *
 * A GET from the same URL gives a list of Activity Types associated with the specified service recipient.
 *
 * This interface must match the Java class com.ecco.webApi.evidence.ActivityInterestChangeViewModel.
 */
export interface ActivityInterestChangeDto extends UpdateCommandDto {
    /** The operation to perform; either "add", or "remove". */
    operation: string;

    /** The ID of the Activity Type to be added or removed. */
    activityTypeId: number;
}


export interface LocationDto {
    lat?: number;
    lon?: number;
    time?: number; // timestamp
    errorCode?: number;
}

/** Command-based data-transfer object representing a command to record a comment that
 * has been carried out.
 *
 * Must match the Java class com.ecco.webApi.evidence.CommentCommandViewModel. */
export interface WorkEvidenceCommandDto extends BaseServiceRecipientCommandDto {
    /** indicates whether this originates from an external system */
    externalSource?: boolean;

    /** 'add' or 'update' - but currently only used client-side to help the flow of adding or editing a work comment */
    operation: string;

    /** The uuid of the piece of work this relates to */
    workUuid: string;

    /** The evidence group this relates to */
    evidenceGroup: string;

    /** The date and time on which the Support Work was carried out.
     *
     * Not necessarily the date and time on which the support work was
     * recorded.
     *
     * The date is represented as an ISO 8601 extended date-time string
     * with no timezone, i.e. "YYYY-MM-DDTHH:MM:SS.ssss".
     *
     * This can start life as the timestamp in UTC (see EvidenceBuilder.withWorkDate)
     * but is updated by the comment command to be the UTC of the provided LocalDateTime
     * but it is also used directly with a DateTime which is the users inputted date
     * - ie from tests, and EditInvidiationCommandHandler, and these are not UTC (so is a bug!)
     * Questionnaires are also still saving with the provided DateTime, which is a user
     * date (see GenericTypeDtoAbstract.toWork) and possibly GroupSupportController, and this
     * is also the legacy approach before version 15.05 with newer typescript screens.
     *
     * However, ongoing, it seems workDate is intended to be UTC.
     */
    workDate: StringChangeOptional;

    /** The time that the work was planned for - e.g. a rota visit date-time */
    plannedWorkDateTime?: string;

    /** The length of time of visit that was planned for */
    plannedMinsSpent?: number;

    /** The comment entered when this Support Work was recorded. */
    comment: StringChangeOptional;

    /** The id of the type of work (e.g. meeting, phone call) */
    commentTypeId: NumberChangeOptional;

    /** calendar event id that this work relates to */
    eventId: StringChangeOptional;

    /** Id of a review if this work was part of a review */
    reviewId?: number;

    /** Location of the device when the work was recorded */
    location?: LocationDto;

    /**
     * The id for the clientStatus list def
     * clientStatus is typically 'whereabouts unknown'/ 'located and OK'
     */
    clientStatusId: NumberChangeOptional;

    /**
     * The id for the meetingStatus list def
     * meetingStatus is typically 'client cancelled' / 'did not attend' etc
     */
    meetingStatusId: NumberChangeOptional;

    /** As per meetingStatusId, it's the result of the event
     * but this is saved on the event not evidence history */
    eventStatusId: NumberChangeOptional;

    /**
     * This is used via invoicing (select from listDef named 'eventStatusRateId'
     */
    eventStatusRateId: NumberChangeOptional;

    /**
     * The id for the location list def
     * location is typically 'community' / 'office' etc
     */
    locationId: NumberChangeOptional;

    /** Number of minutes spent on the support work */
    minsSpent: NumberChangeOptional;

    minsTravel: NumberChangeOptional;

    mileageTo: NumberChangeOptional;

    mileageDuring: NumberChangeOptional;

    flagIds: NumberAddedRemoved;

    /** @Deprecated for addedFlagIds (retained for audit history) */
    addedThreatFlags: StringChangeOptional;
    /** @Deprecated for removedFlagIds (retained for audit history) */
    removedThreatFlags: StringChangeOptional;

    riskManagementRequired: BooleanChange;

    riskManagementHandled: ArrayChange<string>; // uuid

    attachmentIdsToAdd: number[];
}

export interface SignatureSecretFields {
    /** SVG data representing the signature graphically.
     *
     * This is in the form of a complete SVG XML document. */
    svgXml: string;

    /**
     * ISO-8601 instant date and time when this signature was created
     */
    signedDate: string;

    /**
     * Display name of who this signature is on behalf of (usually the service recipient)
     */
    signedFor: string;
}

export interface SignaturePlainFields {
    /** The UUID of the Signature. */
    id: string,
}

/** A signature.
 *
 * This is a signature in the sense of a hand-written name, indicating the
 * named party's agreement to or acknowledgement of some particulars.
 *
 * This interface must match the Java class com.ecco.webApi.evidence.SignatureViewModel. */
export type Signature = SignaturePlainFields & SignatureSecretFields;


/** Data-transfer object representing a command to annotate one or more items
 * of Work with the service recipient's signature, or with a signature captured
 * on behalf of the service recipient.
 *
 * This interface must match the Java class com.ecco.webApi.evidence.SignWorkCommandViewModel. */
export interface SignWorkCommandDto extends BaseServiceRecipientCommandDto {
    /** Identify what evidence group is being signed - used to determine table at back end */
    evidenceGroup: string;

    /** The UUID of the newly created signature. */
    signatureUuid: string;

    /** The UUID of each piece of Work that shall be signed. */
    workUuids: string[];

    /** SVG data representing the signature graphically.
     *
     * This is in the form of a complete SVG XML document. */
    svgXml: string;

    /** Date and time when this signature was created. */
    signedDate: string;
}

// export type EncryptedSupportWork = Encrypted<SupportWorkSecretFields, SupportWorkPlainFields>;
export interface EncryptedSupportWork extends Encrypted<SupportWorkSecretFields, SupportWorkPlainFields> { }

export interface EncryptedRiskWork extends Encrypted<RiskWorkSecretFields, RiskWorkPlainFields> { }


/** Data-transfer object representing a signature.
 *
 * This is a signature in the sense of a hand-written name, indicating the
 * named party's agreement to or acknowledgement of some particulars.
 *
 * This type must match the Java class com.ecco.webApi.evidence.SignatureViewModel. */
export type MangledSignature = Decrypted<SignatureSecretFields, SignaturePlainFields>;

/** Encrypted data-transfer object representing a signature. */
export type EncryptedSignature = Encrypted<SignatureSecretFields, SignaturePlainFields>;

export type EncryptedEvidenceDtos = EncryptedSignature | EncryptedSupportWork | EncryptedRiskWork;



/** All of the evidence DTOs */
export type EvidenceDtos = SignWorkCommandDto;
