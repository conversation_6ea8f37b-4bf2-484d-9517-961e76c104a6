import {array, IdNameDisabled, NumberToObjectMap, StringToObjectMap} from "@eccosolutions/ecco-common";
import {
    FeatureSet as FeatureSetDto,
    GlobalConfig,
    ListDefinitionEntryDto,
    LoginProviders,
    PublicConfigDto,
    SessionDataDto,
    UserGroup,
    UserSessionDataDto
} from "./feature-config-dto";
import {
    AppointmentTypeDto,
    ProjectDto,
    Question,
    ServiceCategorisation,
    ServiceDto,
    TaskDefinition,
    RiskArea as RiskAreaDto,
    TaskNames,
    TaskSettingName
} from "../service-config-dto";
import {
    HactOutcomeEvidenceActivityDto,
    HactOutcomeEvidenceSurveyDto,
    HactOutcomeMappingDto,
    HactSocialValueBankDto
} from "../hact-dto";
import {
    Action,
    ActionComponent,
    Outcome, OutcomeComponent,
    RiskAction,
    RiskArea,
    Service,
    ServiceType
} from "../service-config-domain";
import {FormDefinition, OutcomeDto, PrefixType} from "../index";

const keyLastBy = array.keyLastBy;

// const ALWAYS_DISABLED = "ALWAYS_DISABLED";
const ALWAYS_ENABLED = "ALWAYS_ENABLED";
// const ABSTAIN = "ABSTAIN";
// const DISABLED_BY_DEFAULT = "DISABLED_BY_DEFAULT";
const ENABLED_BY_DEFAULT = "ENABLED_BY_DEFAULT";

export const AGENCYCATEGORY_LISTNAME = "agencyCategory";
export const GENDER_LISTNAME = "gender";
export const GENDERATBIRTH_LISTNAME = "genderAtBirth";
export const PRONOUNS_LISTNAME = "pronouns";
export const DISABILITY_LISTNAME = "disability";
export const ETHNICORIGIN_LISTNAME = "ethnicOrigin";
export const SIGNPOSTREASON_LISTNAME = "signpostReason";
export const EXITREASON_LISTNAME = "exitReason";
export const RELIGION_LISTNAME = "religion";
export const LANGUAGE_LISTNAME = "language";
export const SEXUALORIENTATION_LISTNAME = "sexualOrientation";
export const NATIONALITY_LISTNAME = "nationality";
export const MARITALSTATUS_LISTNAME = "maritalStatus";
export const ASSOCIATEDTYPES_LISTNAME = "associatedTypeId"; // TODO: Rename here and in database to 'contactAssociationType'
export const RELATIONSHIPASSOCIATIONTYPE_LISTNAME = "relationshipAssociationType";
export const FLAGS_RISK = "flags-risk";
export const VENUE_LISTNAME = "venue";
export const GROUPSUPPORTACTIVITY_LISTNAME = "activityType";
export const GROUPAUXACTIVITY_LISTNAME = "activityTypeAux";
export const GROUPAUXCATEGORY_LISTNAME = "activityCategoryAux";

/** Fixed ids that are populated via Liquibase */
export const LIST_DEF_IDS = {
    /** Fixed pre-populated parent entry for buildings */
    BUILDING_RESOURCETYPE_ID: 132,

    /** Fixed pre-populated parent entry for resources */
    RESOURCE_RESOURCETYPE_ID: 133,

    /** Resource type for a care run - which is dubiously related to a fixed container but should be like a person, not like a building */
    CARERUN_RESOURCETYPE_ID: 158
}

export class ListDefinitionEntryAdapter {
    private listDefEntryDtoOrdered: StringToObjectMap<ListDefinitionEntryDto[]> = {};
    private listDefEntryOrdered: StringToObjectMap<ListDefinitionEntry[]> = {};
    private listDefEntryOrderedArray: ListDefinitionEntry[] = [];

    // NumberToObjectMap INSERTS by id, so ignores SessionDataController which sorts by orderby / name
    private listDefEntriesIntermediate: NumberToObjectMap<ListDefinitionEntry> = {};

    constructor(defsByListName: StringToObjectMap<ListDefinitionEntryDto[]>) {
        this.listDefEntryDtoOrdered = defsByListName;
        // intermediate array in order to ensure we have parents fully populated
        this.populateListDefEntriesIntermediate(defsByListName, this.defsById(defsByListName));
        this.populateListDefEntries(defsByListName);
    }

    public getListDefinitionsById(id: number): ListDefinitionEntry | null {
        // sneakily use the intermediate array because it's there
        return this.listDefEntriesIntermediate[id] || null;
    }

    public getListDefinitionsByListName(listName: string) {
        return this.listDefEntryOrdered[listName] || [];
    }

    public getListDefinitionsDtoByListName(listName: string) {
        return this.listDefEntryDtoOrdered[listName] || [];
    }

    public getListDefinitionEntriesAsFlatArray() {
        return this.listDefEntryOrderedArray;
    }

    private defsById(defsByListName: StringToObjectMap<ListDefinitionEntryDto[]>) {
        const listDefEntryDtosByIdOrderedId: NumberToObjectMap<ListDefinitionEntryDto> = {};
        for (let listName in defsByListName) {
            defsByListName[listName]!.forEach(entry => {
                // ORDER LOST - inserted by id
                listDefEntryDtosByIdOrderedId[entry.id] = entry;
            });
        }
        return listDefEntryDtosByIdOrderedId;
    }

    private populateListDefEntriesIntermediate(defsByListName: StringToObjectMap<ListDefinitionEntryDto[]>, defsById: NumberToObjectMap<ListDefinitionEntryDto>) {
        // NumberToObjectMap INSERTS by id, so ignores SessionDataController which sorts by orderby / name
        for (let listName in defsByListName) {
            defsByListName[listName]!.forEach(entry => {
                if (!this.listDefEntriesIntermediate[entry.id]) {
                    // inserted by id
                    this.listDefEntriesIntermediate[entry.id] = this.getOrCreateEntry(entry, defsById)!!;
                }
            });
        }
    }

    private getOrCreateEntry(
            dto: ListDefinitionEntryDto | null,
            defsById: NumberToObjectMap<ListDefinitionEntryDto>,
            isParent = false
    ): ListDefinitionEntry | null {
        if (dto == null) {
            return null;
        }

        // return existing domain object if exists
        if (this.listDefEntriesIntermediate[dto.id]) {
            this.listDefEntriesIntermediate[dto.id].setIsParent(isParent); // we may only find out it's parent later
            return this.listDefEntriesIntermediate[dto.id];
        }

        // if doesn't exist, return new domain object linked to parent - recursively
        const parentEntry = dto.parentId ? defsById[dto.parentId] : null;
        return new ListDefinitionEntry(dto, this.getOrCreateEntry(parentEntry, defsById, true), isParent);
    }

    private populateListDefEntries(defsByListName: StringToObjectMap<ListDefinitionEntryDto[]>) {
        // populate this.listDefEntryOrdered by listName
        for (let listName in defsByListName) {
            const listDefEntryDtos = defsByListName[listName]!;
            // NB listDefinitions preserves order
            this.listDefEntryOrdered[listName] = listDefEntryDtos.map(dto => this.listDefEntriesIntermediate[dto.id]);
        }

        // NB Object/listDefinitions preserves order
        Object.values(this.listDefEntryOrdered)
                .forEach(listDefs => listDefs.forEach(ld => this.listDefEntryOrderedArray.push(ld)));
    }
}

export class ListDefinitionEntry {
    constructor(
        private dto: ListDefinitionEntryDto,
        private parent: ListDefinitionEntry | null,
        private isParent = true
    ) {}

    public getParent() {
        return this.parent;
    }

    public getId() {
        return this.dto.id;
    }
    public getDisabled() {
        return this.dto.disabled;
    }
    public getDefault() {
        return this.dto.defaulted;
    }
    public getName() {
        return this.dto.name;
    }
    public getListName() {
        return this.dto.listName;
    }
    public getValue() {
        return this.dto.metadata && this.dto.metadata.value;
    }
    public getIconClasses() {
        return this.dto.metadata && this.dto.metadata.iconClasses;
    }
    public getColour() {
        return this.dto.metadata && this.dto.metadata.colour;
    }
    public isIconClassSuccess() {
        return (
            this.dto.metadata &&
            this.dto.metadata.iconClasses &&
            this.dto.metadata.iconClasses.indexOf("fa-check-circle") > -1
        );
    }
    public isSignificant(): boolean {
        return this.dto.metadata && this.dto.metadata.significantIncident !== undefined
            ? this.dto.metadata.significantIncident
            : false;
    }
    public getFormDefUuid(): string | undefined {
        return this.dto.metadata?.formDefUuid;
    }
    public getDisplayName() {
        return this.dto.metadata && this.dto.metadata.displayName
            ? this.dto.metadata.displayName
            : this.dto.name;
    }

    public getParentId() {
        return this.getParent() ? this.getParent()!!.getId() : null;
    }

    public getFullName(): string {
        if (this.parent) {
            return this.parent.getFullName() + " - " + this.dto.name;
        }
        return this.dto.name;
    }
    public getIsParent() {
        return this.isParent;
    }
    public setIsParent(isParent: boolean) {
        this.isParent = isParent;
    }
    public getDto() {
        return this.dto;
    }
}

export function listDefToIdName(ld: ListDefinitionEntry): IdNameDisabled {
    return {
        id: ld.getId(),
        name: ld.getDisplayName(),
        disabled: ld.getDisabled()
    };
}

export class HactSessionData {

    public static HACT_QUESTION_GROUP: string = "hact"; // this needs to match the configured question group in the database

    constructor(public triggerMappings: HactOutcomeMappingDto[],
                public evidenceSurveys: HactOutcomeEvidenceSurveyDto[],
                public evidenceActivities: HactOutcomeEvidenceActivityDto[],
                public values: HactSocialValueBankDto[],
                public hactQuestions: Question[]) {
                    /*
        // Safety-check that we don't have any question mappings, because we don't load any triggers (see commit)
        const hasQuestionTriggers = triggerMappings.some(m => !!m.questionDefId); // truthy on the values are fine
        if (hasQuestionTriggers) {
            throw new Error("hact is configured with question triggers but not data is loaded - see this commit");
        }
*/
                }

    public getValuableChangeOutcome(questionDefId: number, answerChoiceDefIdBefore: number,
                                    answerChoiceDefIdAfter: number): string | null {

        if (answerChoiceDefIdBefore == answerChoiceDefIdAfter) {
            return null;
        }

        return this.evidenceSurveys
            .filter((e) => {
                let qnsMatch = e.questionDefId == questionDefId;
                let isValuable =
                    e.valuableAnswerChoiceDefIds.indexOf(answerChoiceDefIdBefore) < 0 &&
                    e.valuableAnswerChoiceDefIds.indexOf(answerChoiceDefIdAfter) >= 0;
                return qnsMatch && isValuable;
            })
            .map((e) => e.hactOutcomeDefCode)[0]!;
    }

    public getOutcome(hactOutcomeDefCode: string): HactSocialValueBankDto {

        return this.values
            .filter((svb) => svb.hactOutcomeDefCode == hactOutcomeDefCode)
            [0]!;
    }

    public getQuestion(hactQuestionDefId: number): Question {

        return this.hactQuestions
            .filter((qn) => qn.id == hactQuestionDefId)
            [0]!;
    }

}

let createFakeBlankListDefEntry = function () {
    return new ListDefinitionEntry(
        {
            id: -1,
            name: `-`,
            listName: "?",
            disabled: true,
            defaulted: false,
            businessKey: "",
            order: null
        },
        null
    );
};

let createFakeDeletedListDefEntry = function (id: number) {
    return new ListDefinitionEntry(
        {
            id,
            name: `[deleted entry: ${id}]`,
            listName: "?",
            disabled: true,
            defaulted: false,
            businessKey: "",
            order: null
        },
        null
    );
};

/** Populate a sparse array looking up names */
function populateNameLookupById<IDNAME extends IdNameDisabled>(idNameList: IDNAME[], namesById: NumberToObjectMap<string>) {
    idNameList && idNameList.forEach(idName => {
        namesById[idName.id] = idName.name;
    });
}

function populateLookupById<T extends {id: number}>(list: T[], itemsById: NumberToObjectMap<T>) {
    list && list.forEach(idName => {
        itemsById[idName.id] = idName; // avoids non-typing of _.keyBy(sessionData.getDto().languages, 'id') (prob fixed by now)
    });
}

/**
 * Config which is publicly available.
 */
export class PublicConfig {
    private loginProviders: { [key in LoginProviders]?: boolean }

    constructor(private config: PublicConfigDto) {
        this.loginProviders = config.loginProviders
    }

    public hasLoginProvider(provider: LoginProviders): boolean {
        return this.loginProviders[provider] || false
    }
}

/** Rich domain object for feature config - non-user specific */
export class SessionDataGlobal {
    private listDefEntriesAdapter = new ListDefinitionEntryAdapter({});

    /**
     * These are the visible Service/Project combinations the user is allowed to see
     * @see SessionDataController.java
     */
    private servicesById: NumberToObjectMap<ServiceDto> = {};

    private serviceTypesById = new Map<number, ServiceType>();

    private projectsId: NumberToObjectMap<ProjectDto> = {};

    // TODO: we might not need projectNames by id because we can just lookup service categorisation instead
    private serviceCategorisationsById: NumberToObjectMap<ServiceCategorisation> = {};

    // LISTDEFINITION indexes
    private genderById: NumberToObjectMap<IdNameDisabled> = {};
    private genderAtBirthById: NumberToObjectMap<IdNameDisabled> = {};
    private disabilityById: NumberToObjectMap<IdNameDisabled> = {};
    private pronounsById: NumberToObjectMap<IdNameDisabled> = {};
    private sexualOrientationById: NumberToObjectMap<IdNameDisabled> = {};
    private pendingStatusesById: NumberToObjectMap<IdNameDisabled> = {};

    // LISTS not converted to listDefinitions
    private appointmentTypesByName: StringToObjectMap<AppointmentTypeDto> = {};
    private appointmentTypesByIds: NumberToObjectMap<AppointmentTypeDto> = {};

    private fundingSourceNameById: NumberToObjectMap<string> = {};

    private languageById: NumberToObjectMap<IdNameDisabled> = {};

    private religionById: NumberToObjectMap<IdNameDisabled> = {};

    private ethnicOriginById: NumberToObjectMap<IdNameDisabled> = {};

    private nationalistListById: NumberToObjectMap<IdNameDisabled> = {};

    private maritalStatusListById: NumberToObjectMap<IdNameDisabled> = {};

    private associatedTypesListById: NumberToObjectMap<IdNameDisabled> = {};

    private relationshipsListById: NumberToObjectMap<IdNameDisabled> = {};

    private taskDefinitions: TaskDefinition[] = [];

    private likelihoodIndex: Record<string, ListDefinitionEntryDto> = {};
    private severityIndex: Record<string, ListDefinitionEntryDto> = {};

    // EVIDENCE
    // the entire outcomes in the system, and the actions assigned to them
    private supportActionsById: NumberToObjectMap<Action> = {};
    private supportOutcomes: Outcome[] = [];

    // the entire risk areas in the system, and the actions assigned to them
    private riskActionsById: NumberToObjectMap<RiskAction> = {};
    private riskAreas: RiskArea[] = [];

    private questionsById: NumberToObjectMap<Question> = {};

    private globalFeatureSet?: FeatureSet;

    constructor(private features: GlobalConfig) {
        if (!features) {
            return; // Default to empty if we got null - used when we are using offline version prior to sync
        }

        /* stick all dtos in a map so we can reference them */
        if (features.listDefinitions) {
            /* create a domain entry for each, and link to parent */
            this.listDefEntriesAdapter = new ListDefinitionEntryAdapter(features.listDefinitions);

            // creates map of id -> item, just for quicker access - probably pointless
            this.genderById = this.namedListById(features, GENDER_LISTNAME);
            this.genderAtBirthById = this.namedListById(features, GENDERATBIRTH_LISTNAME);
            this.disabilityById = this.namedListById(features, DISABILITY_LISTNAME);
            this.pronounsById = this.namedListById(features, PRONOUNS_LISTNAME);
            this.sexualOrientationById = this.namedListById(features, SEXUALORIENTATION_LISTNAME);
            this.ethnicOriginById = this.namedListById(features, ETHNICORIGIN_LISTNAME);
            this.nationalistListById = this.namedListById(features, NATIONALITY_LISTNAME);
            this.maritalStatusListById = this.namedListById(features, MARITALSTATUS_LISTNAME);
            this.associatedTypesListById = this.namedListById(features, ASSOCIATEDTYPES_LISTNAME);
            this.relationshipsListById = this.namedListById(
                features,
                RELATIONSHIPASSOCIATIONTYPE_LISTNAME
            );
            this.languageById = this.namedListById(features, LANGUAGE_LISTNAME);
            this.religionById = this.namedListById(features, RELIGION_LISTNAME);
        }

        /* Index services and projects so we can look up ids */
        features.services &&
            features.services.forEach(service => {
                this.servicesById[service.id] = service;
            });
        features.projects &&
            features.projects.forEach(project => {
                this.projectsId[project.id] = project;
            });

        features.serviceCategorisations &&
            features.serviceCategorisations.forEach(sc => {
                this.serviceCategorisationsById[sc.id] = sc;
            });

        /* stick domain objects for all DTOs in a Map so we can reference them */
        if (features.serviceTypesById) {
            for (const [k, v] of Object.entries(features.serviceTypesById)) {
                this.serviceTypesById.set(parseInt(k), new ServiceType(v, features.messages));
            }
        }

        this.globalFeatureSet = new FeatureSet(this.features.featureSets["global"]);

        this.taskDefinitions = features.taskDefinitions;

        // NOT LISTDEFs yet - also regions
        this.features.appointmentTypes &&
            this.features.appointmentTypes.forEach(apptType => {
                this.appointmentTypesByName[apptType.name] = apptType;
                this.appointmentTypesByIds[apptType.id] = apptType;
            });
        populateNameLookupById(this.features.fundingSources, this.fundingSourceNameById);

        // this mimics ecco-dto.ServiceType
        // but builds up the outcome -> actions based on the SessionData, which is the global list in the system
        this.features.supportOutcomes &&
            this.features.supportOutcomes.forEach(supportOutcome => {
                let outcome = new Outcome(supportOutcome);
                this.supportOutcomes.push(outcome);
                outcome.getActionGroups().forEach(ag =>
                    ag.getActions().forEach(a => {
                        this.supportActionsById[a.getId()] = a;
                    })
                );
            });

        // this mimics ecco-dto.ServiceType
        this.features.riskAreas &&
            this.features.riskAreas.forEach(riskArea => {
                let area = new RiskArea(riskArea);
                this.riskAreas.push(area);
                area.getActionGroups().forEach(ag =>
                    ag.getActions().forEach(a => {
                        this.riskActionsById[a.getId()] = a;
                    })
                );
            });

        // index by 'value' - which is the number 1-5
        const riskLikelihoodDefs =
            this.listDefEntriesAdapter.getListDefinitionsDtoByListName("riskMatrixLikelihood");
        if (riskLikelihoodDefs) {
            this.likelihoodIndex = keyLastBy(riskLikelihoodDefs, entry => {
                if (entry.metadata) {
                    return entry.metadata.value;
                }
                throw new Error("riskMatrixLikelihood requires metadata.value");
            });
        }
        const riskSeverityDefs =
            this.listDefEntriesAdapter.getListDefinitionsDtoByListName("riskMatrixSeverity");
        if (riskSeverityDefs) {
            this.severityIndex = keyLastBy(riskSeverityDefs, entry => {
                if (entry.metadata) {
                    return entry.metadata.value;
                }
                throw new Error("riskMatrixSeverity requires metadata.value");
            });
        }

        this.features.questionGroups &&
            this.features.questionGroups.forEach(questionGroup => {
                populateLookupById(questionGroup.questions, this.questionsById);
            });
    }

    private static comparatorByName<T extends {name: string}>(a: T, b: T) {
        return a.name.localeCompare(b.name);
    }

    /**
     * Turn a list into a map of id -> item
     * Where the list is alpha-ordered from the server - see thenComparing(ListDefinitionEntry::getName)
     * NB keyLastBy is from https://github.com/softwareventures/array/blob/main/index.ts
     */
    private namedListById(
        features: GlobalConfig,
        listName: string
    ): NumberToObjectMap<IdNameDisabled> {
        if (!this.listDefEntriesAdapter.getListDefinitionsDtoByListName(listName)) {
            return {};
        }
        // NB listDefinitions preserve order
        const listAsIdName = this.listDefEntriesAdapter
            .getListDefinitionsByListName(listName)
            .map(ld => listDefToIdName(ld));
        return keyLastBy(listAsIdName, idName => idName.id);
    }

    /** Allows null to be passed in and returned to make calling code easier if no form is specified */
    findFormDefinition(uuid: string, index?: number): FormDefinition | null {
        if (!uuid) {
            return null;
        }
        let formDef;

        // if we look like an array, then get the last - we're using the first ones in 'incidents' currently
        if (uuid.indexOf(",") > -1) {
            let formDefUuids = uuid.split(",");
            const items = formDefUuids.length;
            // if we specify an index, return that, or null if invalid
            // else we return the last item in the list
            if (index !== undefined) {
                if (index < items) {
                    formDef = this.getDto().formDefinitionsById[formDefUuids[index]];
                } else {
                    return null;
                }
            } else {
                formDef = this.getDto().formDefinitionsById[formDefUuids[items - 1]];
            }
        } else {
            const notNullUndefined = (v: any) => v !== null && v !== undefined;
            // if we expected an index return null if there isn't one
            if (notNullUndefined(index)) {
                return null;
            }
            // if we weren't expecting an index, return the single form def defined
            formDef = this.getDto().formDefinitionsById[uuid];
        }

        if (!formDef) {
            throw new Error("FormDefinition invalid for: " + uuid);
        }
        return formDef;
    }

    public getGlobalFeatureSet(): FeatureSet {
        return this.globalFeatureSet!!;
    }

    /** Return true if there are any projects that are configured for any services */
    public hasProjects() {
        return Object.keys(this.projectsId).length > 0; // was _.size(
    }

    public isModuleEnabled(
        softwareModuleKey:
            | "buildings"
            | "hact"
            | "loneWorker"
            | "offline"
            | "rota"
            | "workflow"
            | "zendesk"
    ) {
        return (
            this.features.softwareModulesEnabled[softwareModuleKey] != null &&
            this.features.softwareModulesEnabled[softwareModuleKey].enabled
        );
    }

    public isEnabled(featureKey: string) {
        try {
            // NOTE: Currently only works on basis of global features
            let vote =
                this.features &&
                this.features.featureSets["global"].featureVotes[featureKey].defaultVote;
            return vote == ENABLED_BY_DEFAULT || vote == ALWAYS_ENABLED;
        } catch (e) {
            return false; // If anything goes wrong just ignore.
        }
    }

    public static compare<T extends {name: string; id: number}>(a: T, b: T) {
        // if not equal and something is -ve, put the -ve first (eg serviceId -200, projectId -100)
        if (a.id != b.id && (a.id < 0 || b.id < 0)) {
            return a.id < 0 && b.id < 0
                ? b.id - a.id < 0
                    ? -1
                    : 1 // if 'a' is bigger negative, put earlier
                : a.id < 0
                ? -1
                : 1; // if 'a' is negative only, put earlier
        }
        return a.name.localeCompare(b.name);
    }
    public getAppointmentTypes() {
        return this.getDto().appointmentTypes.sort(SessionDataGlobal.compare);
    }
    public getListDefinitionEntryById(id: number | null) {
        return id == null
            ? createFakeBlankListDefEntry()
            : this.listDefEntriesAdapter.getListDefinitionsById(id) ||
                  createFakeDeletedListDefEntry(id);
    }

    public getListDefinitionsFilteredByCsvIds(csvIds: string | null) {
        if (!csvIds) {
            return [];
        }
        // NB ids preserve order
        let ids = csvIds.split(",").map(str => parseInt(str));
        return ids.map(id => this.listDefEntriesAdapter.getListDefinitionsById(id)!);
    }

    public getListDefinitionEntriesAsFlatArray() {
        return this.listDefEntriesAdapter.getListDefinitionEntriesAsFlatArray();
    }

    // See ListDefSelect2List.ts
    public getEntries(listName: string): Array<ListDefinitionEntry> {
        const initialEntries =
            listName != null
                ? this.getListDefinitionEntriesByListName(listName)
                : this.getListDefinitionEntriesAsFlatArray();
        let allEntries = initialEntries.slice(0);
        initialEntries.forEach((entry: ListDefinitionEntry) => {
            allEntries = allEntries.concat(
                this.getListDefinitionEntriesUnderParentRecursively(entry.getId())
            );
        });
        return allEntries;
    }

    public getServiceTypeById(id: number) {
        return this.serviceTypesById.get(id)!; // assumes there is always an entry
    }

    /**
     * get the serviceType from services -> serviceTypeId
     * ideally this does not get stored on the service
     * but comes from the entity using the service
     * so we can separate config from permissions
     */
    public getServiceTypeByServiceCategorisationId(svcCatId: number) {
        return this.getServiceTypeById(
            this.getService(this.getServiceCategorisation(svcCatId).serviceId).serviceTypeId
        );
    }
    public getServiceTypeFromServiceCategorisation(svcCatId: number): ServiceType {
        const serviceId = this.serviceCategorisationsById[svcCatId].serviceId;
        const serviceTypeId = this.getService(serviceId).serviceTypeId;
        return this.getServiceTypeById(serviceTypeId);
    }

    public getRiskManagementTaskForServiceTypeId(id: number): string | null {
        // mimics referralView_calcEvidenceLabels.jspf
        const lookupOrder = [
            "riskManagement",
            "riskManagementLone",
            "threatReduction",
            "threatAssessmentReduction",
            "threatAssessment"
        ];
        const conf = this.serviceTypesById.get(id)!;
        const tasks = conf
            .getTaskDefinitionEntries()
            .filter(td => lookupOrder.indexOf(td.getName()) > -1);
        return tasks.length > 0 ? tasks[0].getName() : null;
    }

    public getServiceTypes(): ServiceType[] {
        return this.serviceTypesById ? Array.from(this.serviceTypesById.values()) : [];
    }
    public getServicesOnServiceType(id: number) {
        const arr: ServiceDto[] = [];
        // Object.keys sticks to own properties, so avoids for..in with a hasOwnProperty check
        Object.keys(this.servicesById)
            .filter(key => this.servicesById[+key].serviceTypeId == id)
            .forEach(key => arr.push(this.servicesById[+key]));
        return arr;
    }

    public getTaskDefinitions() {
        return this.taskDefinitions;
    }

    /** Get from a list of global task definitions (referral aspects) */
    public getTaskDefinitionById(id: number): TaskDefinition | null {
        if (!id) {
            return null;
        }
        return this.taskDefinitions.filter(td => td.id == id)[0];
    }
    /** Get from a lList of global task definitions (referral aspects) */
    public getTaskDefinitionByName(name: string): TaskDefinition | null {
        if (!name) {
            return null;
        }
        return this.taskDefinitions.filter(td => td.name == name)[0];
    }

    public static isTaskDefinitionDedicated(taskDef: TaskDefinition) {
        return taskDef && taskDef.type == "DEDICATED_TASK";
    }

    public isTaskDefinitionAuditOnly(taskDef: TaskDefinition) {
        return taskDef && taskDef.type == "AUDITONLY";
    }

    public isTaskDefinitionEvidence(taskDef: TaskDefinition) {
        if (!taskDef) {
            return false;
        }
        switch (taskDef.type) {
            case "EVIDENCE_CUSTOMFORM":
            case "EVIDENCE_SUPPORT":
            case "EVIDENCE_RISK":
            case "EVIDENCE_QUESTIONNAIRE":
            case "EVIDENCE_ROTA":
            case "EVIDENCE_CHECKLIST":
                return true;
            default:
                return false;
        }
    }

    public isTaskDefinitionAgreement(taskDef: TaskDefinition) {
        return taskDef && taskDef.type == "AGREEMENT";
    }

    public getActionChecksListNameSetting(
        actionDef: ActionComponent,
        serviceType?: ServiceType,
        taskName?: string
    ) {
        const configName =
            serviceType &&
            taskName &&
            serviceType.getTaskDefinitionSetting(taskName, "actionDefaultListName");
        return actionDef.getStatusChangeReasonListName() || configName || "default-checks";
    }

    /** Return a list of entries with a matching listName */
    public getListDefinitionEntriesByListName(
        listName: string,
        parentsOnly = false,
        hideDisabledExceptId?: boolean | number
    ): Array<ListDefinitionEntry> {
        const childEntries: ListDefinitionEntry[] = [];
        // NB listDefinitions preserves order
        this.listDefEntriesAdapter.getListDefinitionsByListName(listName).forEach(item => {
            if (!parentsOnly || item.getIsParent()) {
                // hide if we are requesting hidden, its disabled and its not the current id selected
                const hide =
                    hideDisabledExceptId &&
                    item.getDisabled() &&
                    item.getId() != hideDisabledExceptId;
                if (!hide) {
                    childEntries.push(item);
                }
            }
        });
        return childEntries;
        //: childEntries.sort((a, b) => a.getDisplayName().localeCompare(b.getDisplayName()));
    }

    /** Return a list of entries with a matching parentId */
    public getListDefinitionEntriesByParentId(parentId: number): Array<ListDefinitionEntry> {
        return this.getListDefinitionEntriesAsFlatArray().filter(
            ld => ld.getParentId() == parentId
        );
    }

    public getListDefinitionEntriesUnderParentRecursively(
        parentId: number
    ): Array<ListDefinitionEntry> {
        const initialEntries = this.getListDefinitionEntriesByParentId(parentId);
        let allEntries = initialEntries.slice(0);
        initialEntries.forEach(entry => {
            allEntries = allEntries.concat(
                this.getListDefinitionEntriesUnderParentRecursively(entry.getId())
            );
        });
        return allEntries;
    }

    /**
     * returns parentId plus all ids for children recursively
     */
    public getAllMatchingListDefIdsForParent(parentId: number): Array<number> {
        return [parentId].concat(
            this.getListDefinitionEntriesUnderParentRecursively(parentId).map(entry =>
                entry.getId()
            )
        );
    }

    public getDto() {
        return this.features;
    }

    /** lookup as value = settings[namespace + ":" + key] */
    public getSetting(fullKey: string): string {
        return this.features && this.features.settings && this.features.settings[fullKey];
    }

    /** As getSetting(), but will split on comma into an array
     * @returns empty array if no setting exists */
    public getSettingAsArray(fullKey: string): string[] {
        const setting = this.getSetting(fullKey);

        return setting ? setting.split(",").map(s => s.trim()) : [];
    }

    // lists lookup, but could just use on-the-fly getListDefinitionEntriesByListName
    // which also offers alpha-order, and displayName etc
    // NB already by 'order' from the server
    public getGenderList(): IdNameDisabled[] {
        return Object.values(this.genderById);
    }
    public getGenderAtBirthList(): IdNameDisabled[] {
        return Object.values(this.genderAtBirthById);
    }
    public getDisabilityList(): IdNameDisabled[] {
        return Object.values(this.disabilityById);
    }
    public getPronounsList(): IdNameDisabled[] {
        return Object.values(this.pronounsById);
    }
    public getSexualOrientationList(): IdNameDisabled[] {
        return Object.values(this.sexualOrientationById);
    }
    public getEthnicOriginList(): IdNameDisabled[] {
        return Object.values(this.ethnicOriginById);
    }
    public getNationalityList(): IdNameDisabled[] {
        return Object.values(this.nationalistListById);
    }
    public getMaritalStatusList(): IdNameDisabled[] {
        return Object.values(this.maritalStatusListById);
    }
    public getPendingStatusList(): IdNameDisabled[] {
        return Object.values(this.pendingStatusesById);
    }
    public getAssociatedTypesList(): IdNameDisabled[] {
        return Object.values(this.associatedTypesListById);
    }
    public getLanguageList(): IdNameDisabled[] {
        return Object.values(this.languageById);
    }

    public getReligionList(): IdNameDisabled[] {
        return Object.values(this.religionById);
    }
    public getRelationshipAssociationTypesList(currentValue?: number): IdNameDisabled[] {
        return Object.values(this.relationshipsListById).filter(
            l => currentValue == l.id || !l.disabled
        );
    }

    /** Lookup the score of the likelihood */
    public getLikelihood(id: number) {
        return this.likelihoodIndex[`${id}`];
    }

    /** Lookup the score of the severity */
    public getSeverity(id: number) {
        return this.severityIndex[`${id}`];
    }

    public getProjectName(id: number | undefined) {
        return id ? this.getProject(id)!.name : null;
    }

    public getProject(id: number | null) {
        return id
            ? this.projectsId[id]
                ? this.projectsId[id]
                : ({id: id, name: "(" + id.toString() + ")"} as ProjectDto)
            : null;
    }

    public getServiceName(id: number) {
        return this.getService(id).name;
    }

    public getServiceProjectName(serviceId: number, projectId: number) {
        return projectId
            ? this.getServiceName(serviceId).concat(" ").concat(this.getProjectName(projectId)!!)
            : this.getServiceName(serviceId);
    }

    // NB Add company name & client group?
    public getServiceCategorisationName(serviceCategorisationId: number) {
        const svcCat = this.getServiceCategorisation(serviceCategorisationId);
        return svcCat
            ? svcCat.projectId
                ? this.getServiceName(svcCat.serviceId)
                      .concat(" ")
                      .concat(this.getProjectName(svcCat.projectId)!!)
                : this.getServiceName(svcCat.serviceId)
            : "(" + serviceCategorisationId.toString() + ")";
    }

    getService(serviceId: number): ServiceDto {
        return this.servicesById[serviceId]
            ? this.servicesById[serviceId]
            : ({id: serviceId, name: "(" + serviceId.toString() + ")"} as ServiceDto);
    }

    hasHactForService(serviceId: number) {
        const service = this.getService(serviceId);
        const hactExcludeService = service.parameters ? service.parameters["hact.exclude"] : false;
        const hactOnService = this.isModuleEnabled("hact") && !hactExcludeService;
        return hactOnService;
    }

    public getServiceCategorisation(id: number): ServiceCategorisation {
        return this.serviceCategorisationsById[id];
    }
    // bit of a hack in that we are using knowledge that it's the same on all items
    // therefore, we can just find the first item
    public getServiceTypeByServiceIdHack(serviceId: number): ServiceType | null {
        // get the first one and return
        if (!serviceId) {
            return null;
        }
        const svcId =
            this.features.serviceCategorisations.filter(sc => sc.serviceId == serviceId).pop() ||
            null;
        return svcId
            ? this.getServiceTypeById(this.getService(svcId.serviceId).serviceTypeId)
            : null;
    }
    public getServiceCategorisationByIds(
        serviceId: number,
        projectId: number | null
    ): ServiceCategorisation | null {
        if (!serviceId && !projectId) {
            return null;
        }
        return (
            this.features.serviceCategorisations
                .filter(sc => sc.serviceId == serviceId && sc.projectId == projectId)
                .pop() || null
        );
    }

    public getServiceCategorisations(
        hideDisabledExceptId?: boolean | number
    ): ServiceCategorisation[] {
        return this.features.serviceCategorisations.filter(sc => {
            const hide = hideDisabledExceptId && sc.disabled && sc.id != hideDisabledExceptId;
            return !hide;
        });
    }

    public getServiceCategorisationServices(hideDisabledExceptId?: boolean | number): ServiceDto[] {
        const scp = this.getServiceCategorisations(hideDisabledExceptId).map(sc => sc.serviceId!);
        const uniqueServiceIds = [...new Set(scp)];
        return uniqueServiceIds.map(u => this.getService(u)!).sort(SessionDataGlobal.compare);
    }

    public getServiceCategorisationProjects(
        serviceId: number,
        hideDisabledExceptId?: boolean | number
    ): ProjectDto[] {
        const scp = this.getServiceCategorisations(hideDisabledExceptId)
            .filter(sc => sc.serviceId == serviceId)
            .filter(sc => !!sc.projectId)
            .map(sc => sc.projectId!);
        const uniqueProjectIds = [...new Set(scp)];
        return uniqueProjectIds.map(u => this.getProject(u)!).sort(SessionDataGlobal.compare);
    }

    /** Similar to ServiceType.lookupTaskName, but this tries if the serviceType no longer has the task configured */
    public lookupTaskName(
        taskDefId?: number,
        serviceTypeId?: number,
        taskDefName?: string
    ): string {
        let name: string | undefined = "ad hoc";
        // try the more specific match first - if we are still configured in the service config
        if (serviceTypeId && taskDefName) {
            name = this.getServiceTypeById(serviceTypeId)
                .getTaskDefinitionEntry(taskDefName)
                ?.getDisplayName();
        }
        // try the taskDefId (referralaspect) in case we no longer have a configured servicetypeId with taskDefName
        if (!name && taskDefId) {
            name =
                this.getTaskDefinitionById(taskDefId)?.description ||
                `-unknown-taskDefId-${taskDefId}-`;
        }
        return name || `-unknown-taskDefName-${taskDefName}-`;
    }

    /** CAREFUL?: it might be there are shared names across the sevices */
    public getAppointmentTypeByName(name: string): AppointmentTypeDto {
        return this.appointmentTypesByName[name];
    }
    public getAppointmentTypeById(id: number): AppointmentTypeDto {
        return this.appointmentTypesByIds[id];
    }
    public getFundingSourceNameById(id: number): string {
        return this.fundingSourceNameById[id];
    }

    // EVIDENCE
    public getOutcomeById(outcomeId: number): Outcome | undefined {
        return this.supportOutcomes.filter(o => o.getId() == outcomeId)?.pop();
    }
    public getRiskAreaById(outcomeId: number): RiskArea | undefined {
        return this.riskAreas.filter(o => o.getId() == outcomeId)?.pop();
    }

    public getAnyOutcomeById(outcomeId: number): OutcomeDto | RiskAreaDto | undefined {
        const supportOutcome = this.getOutcomeById(outcomeId);
        if (supportOutcome) {
            return supportOutcome.asDto();
        }
        const riskArea = this.getRiskAreaById(outcomeId);
        if (riskArea) {
            return riskArea.asDto();
        }
        return undefined;
    }
    public getAnyOutcomeByIdAsComponent(outcomeId: number): OutcomeComponent | undefined {
        const supportOutcome = this.getOutcomeById(outcomeId);
        const riskArea = this.getRiskAreaById(outcomeId);
        return supportOutcome || riskArea;
    }

    public getAnyActionById(actionId: number): ActionComponent {
        const supportActionDef = this.supportActionsById[actionId];
        if (supportActionDef) {
            return supportActionDef;
        }
        return this.riskActionsById[actionId];
    }

    public isAnyActionByIdDisabled(actionId: number): boolean {
        const action = this.getAnyActionById(actionId);
        return (
            action.isDisabled() ||
            action.getActionGroupComponent().isDisabled() ||
            action.getOutcome().isDisabled()
        );
    }

    public getSupportActionById(id: number): Action {
        return this.supportActionsById[id];
    }

    public getRiskActionById(id: number): RiskAction {
        return this.riskActionsById[id];
    }

    describeSupportAction(actionId: number) {
        const action = this.supportActionsById[actionId];
        const hasGroups = action.actionGroup.outcome.actionGroups.length > 1;
        return "[".concat(
            action
                .getOutcome()
                .getName()
                .concat("] ")
                .concat(
                    action.getName() +
                        (hasGroups && action.actionGroup.getName()
                            ? " [" + action.actionGroup.getName().trim() + "]"
                            : "")
                )
        );
    }

    describeRiskAction(actionId: number) {
        const action = this.riskActionsById[actionId];
        const hasGroups = action.riskActionGroup.riskArea.riskActionGroups.length > 1;
        return "[".concat(
            action
                .getOutcome()
                .getName()
                .concat("] ")
                .concat(
                    action.getName() +
                        (hasGroups && action.riskActionGroup.getName()
                            ? " [" + action.riskActionGroup.getName().trim() + "]"
                            : "")
                )
        );
    }

    getQuestionGroupById(questionGroupId: number) {
        if (this.features.questionGroups) {
            return this.features.questionGroups.filter(qg => qg.id == questionGroupId)[0];
        }
        throw Error("questionGroup not found");
    }

    /**
     * Get the first question group this question is part of.
     * Technically, a question can be part of many question groups, but practically this isn't likely,
     * therefore just getting the first question group is suitable.
     * @param questionId
     */
    getQuestionGroupByQuestionId(questionId: number) {
        if (this.features.questionGroups) {
            return this.features.questionGroups
                .filter(qg => qg.questions.filter(q => q.id == questionId).length > 0)
                .pop();
        }
        throw Error("questionGroup not found");
    }

    public lookupQuestion(id: number): string {
        return this.getQuestionById(id).name || "";
    }

    getQuestionById(questionId: number): Question {
        return this.questionsById[questionId];
    }

    // choices might not exist yet in an admin page, so need to allowBlankChoices
    // see d07bd3a2
    public static questionType(question: Question, allowBlankChoices: boolean): string {
        if (question.choices && question.choices.length > 0) {
            return "choices";
        }
        if (question.parameters.listName) {
            return "list";
        }
        if (question.freeTypes && question.freeTypes.length > 0) {
            // get the freetype - which can only be one entry!
            let freeType = question.freeTypes[0];
            return freeType.valueType;
        }
        if (question.choices && allowBlankChoices) {
            return "choices";
        }
        throw new Error("misconfigured question: " + question.id);
    }

    /** If answerValue (what is stored in database) has a match in choices, then the displayValue of the match is
     * show, otherwise answerValue is returned, so as to cater for text input responses.
     */
    public getAnswerDisplayValue(questionId: number, answerValue: string | undefined): string {
        const question = this.getQuestionById(questionId);
        let displayValue: string | undefined = answerValue;
        if (
            question.freeTypes &&
            question.freeTypes.length > 0 &&
            question.freeTypes[0].valueType == "checkbox"
        ) {
            // show unanswered as '-' which is a bit odd for a checkbox as off is the same as no data
            // but it seems there is value in having '-' visually, and having 'no' meaning undone
            if (answerValue !== undefined) {
                displayValue = answerValue.toLowerCase() == "true" ? "yes" : "no";
            }
        }
        question.choices &&
            question.choices.forEach(choice => {
                if (choice.value == answerValue) {
                    displayValue = choice.displayValue;
                }
            });
        if (question.parameters?.listName && answerValue) {
            displayValue = this.getListDefinitionEntryById(parseInt(answerValue)).getDisplayName();
        }
        return displayValue || "-";
    }

    getAnswerValueId(questionId: number, answerValue: string): number | null {
        const question = this.getQuestionById(questionId);
        let valueId = null;
        question.choices &&
            question.choices.forEach(choice => {
                if (choice.value == answerValue) {
                    valueId = choice.id;
                }
            });
        return valueId;
    }

    getActionDefIdFromQuestionDefId(taskName: string, questionDefId: number): number | undefined {
        const qa = this.getTaskDefinitionByName(taskName)?.metadata?.questionsToActions;
        if (!qa) {
            return undefined;
        }
        return qa
            .filter(rec => rec[0] == questionDefId)
            .map(rec => rec[1])
            .pop();
    }

    getQuestionDefIdFromActionDefId(taskName: string, actionDefId: number): number | undefined {
        const qa = this.getTaskDefinitionByName(taskName)?.metadata?.questionsToActions;
        if (!qa) {
            return undefined;
        }
        return qa
            .filter(rec => rec[1] == actionDefId)
            .map(rec => rec[0])
            .pop();
    }

    getActionDefIdsInTask(taskName: string): number[] {
        return this.getQuestionActionDefIdsInTask(taskName, 1);
    }
    getQuestionDefIdsInTask(taskName: string): number[] {
        return this.getQuestionActionDefIdsInTask(taskName, 0);
    }
    getQuestionActionDefIdsInTask(taskName: string, position: number): number[] {
        const qa = this.getTaskDefinitionByName(taskName)?.metadata?.questionsToActions;
        if (!qa) {
            return [];
        }
        return qa.map(rec => rec[position]);
    }
}

/** Rich domain object for feature config - user specific, with global config */
export class SessionData extends SessionDataGlobal {
    private readonly userFeatures: UserSessionDataDto;

    constructor(private allFeatures: SessionDataDto) {
        super(allFeatures);
        this.userFeatures = allFeatures;
    }

    public getDto() {
        return this.allFeatures;
    }

    public getRestrictedServices(hideDisabledExceptId?: boolean | number): ServiceDto[] {
        // also see this.userFeatures.restrictedServicesProjects.services
        const scs = this.getRestrictedServiceCategorisations(hideDisabledExceptId).map(
            sc => sc.serviceId
        );
        const uniqueServiceIds = [...new Set(scs)];
        return (
            uniqueServiceIds
                .map(u => this.getService(u))
                // don't show info where the cache doesn't have them yet
                // anything using userFeatures needs to consider this
                .filter(s => !!s)
        );
    }

    public getRestrictedServiceCategorisations(
        hideDisabledExceptId?: boolean | number
    ): ServiceCategorisation[] {
        return (
            this.userFeatures.restrictedServiceCategorisations

                .sort((a, b) =>
                    a.serviceName.localeCompare(b.serviceName) != 0
                        ? a.serviceName.localeCompare(b.serviceName)
                        : a.projectName && b.projectName
                        ? a.projectName.localeCompare(b.projectName)
                        : a.projectName
                        ? -1
                        : 1
                )
                .filter(sc => {
                    const hide =
                        hideDisabledExceptId && sc.disabled && sc.id != hideDisabledExceptId;
                    return !hide;
                })
                // don't show info where the cache doesn't have them yet
                // anything using userFeatures needs to consider this
                .filter(sc => !!this.getServiceCategorisation(sc.id))
        );
    }

    public getMessages() {
        return this.getDto().messages;
    }

    private hasRole(role: string) {
        return (
            this.userFeatures &&
            this.userFeatures.roles &&
            this.userFeatures.roles.indexOf(role) >= 0
        );
    }

    public hasRoleStaff() {
        return this.hasRole("ROLE_STAFF");
    }

    /** Has role required for doing site admin functions such as deleting referrals, clients, group activities */
    public hasRoleAdmin() {
        return this.hasRole("ROLE_ADMIN");
    }

    public hasRoleUserAdmin() {
        return this.hasRole("ROLE_ADMINLOGIN");
    }

    public hasRoleAAA() {
        return this.hasRole("ROLE_AAA");
    }

    public hasRoleSwitchUser() {
        return this.hasRole("ROLE_SWITCHUSER");
    }
    public hasRoleBuildingAdmin() {
        return this.hasRole("ROLE_ADMINBUILDING");
    }
    public hasRoleCalendarAdmin() {
        return this.hasRole("ROLE_ADMINCALENDAR");
    }

    public hasRoleReports() {
        return this.hasRole("ROLE_REPORTS");
    }

    public hasRoleIncidents() {
        return this.hasRole("ROLE_INCIDENTS");
    }
    public hasRoleRepairs() {
        return this.hasRole("ROLE_REPAIRS");
    }

    /** Has role required for doing system admin functions, usually only done by ecco devs */
    public hasRoleSysAdmin() {
        return this.hasRole("ROLE_SYSADMIN");
    }

    // this is the 'service admin' permission
    public hasRoleGroupSupportAdmin() {
        return this.hasRole("ROLE_ADMINGROUPSUPPORT");
    }

    public hasRoleHr() {
        return this.hasRole("ROLE_HR");
    }

    public hasRoleRota() {
        return this.hasRole("ROLE_ADMINROTA");
    }

    public hasRoleFinance() {
        return this.hasRole("ROLE_FINANCE");
    }

    /** When impersonating - so we can allow 'stop impersonating' */
    public hasRolePreviousAdministrator() {
        return this.hasRole("ROLE_PREVIOUS_ADMINISTRATOR");
    }

    public hasRoleCarer() {
        return this.hasRole("ROLE_CARER");
    }

    public hasRoleDailyChecks() {
        return this.hasRole("ROLE_DAILYCHECKS");
    }

    /** Can synchronise and work offline */
    public hasRoleOffline() {
        return this.hasRole("ROLE_OFFLINE");
    }
    /** Has role required for doing stuff according to quick-guide on logins page
     * NB We could consider more fine grained handling through a Permission enum with a class UserPermissionEvaluator and static method
     */
    public hasRoleEvangelist() {
        return this.hasRole("ROLE_EVANGELIST");
    }
    public hasRoleTrial1() {
        return this.hasRole("ROLE_TRIAL1");
    }
    public hasRoleTrial2() {
        return this.hasRole("ROLE_TRIAL2");
    }
    public hasRoleReferralAdmin() {
        return this.hasRole("ROLE_ADMINREFERRAL");
    }
    public hasRoleReferralStart() {
        return this.hasRole("ROLE_STARTREFERRAL");
    }
    public hasRoleReferralDelete() {
        return this.hasRole("ROLE_DELETEREFERRAL");
    }
    // When limiting a referral view, see if we can see 'tasks'.
    // There is no ts reference currently - see referralViewTabs.tag 'ecco:referral-permission'
    public hasRoleReferralOverviewTasks() {
        return this.hasRole("ROLE_OVERVIEWREFERRAL_TASKS");
    }
    // When limiting a referral view, see if we can see agreements in the tasks (requires ROLE_OVERVIEWREFERRAL_TASKS).
    public hasRoleReferralOverviewTasks1() {
        return this.hasRole("ROLE_OVERVIEWREFERRAL_TASKS_1");
    }

    public hasRoleInterviewer() {
        return this.hasRole("ROLE_INTERVIEWER");
    }
    public hasRoleReferralNew() {
        return this.hasRole("ROLE_NEWREFERRAL");
    }
    public hasRoleReferralEdit() {
        return this.hasRole("ROLE_EDITREFERRAL");
    }
    // NB this should be ROLE_EXITREFERRAL
    public hasRoleReferralClose() {
        return this.hasRole("ROLE_CLOSEREFERRAL");
    }
    public hasRoleEvidenceAdmin() {
        return this.hasRole("ROLE_ADMINEVIDENCE");
    }
    public hasRoleEditEvidence() {
        return this.hasRole("ROLE_EDITEVIDENCE");
    }
    public hasRoleEditEvidenceParked() {
        return this.hasRole("ROLE_EDITEVIDENCEPARKED");
    }
    public hasRoleEditEvidenceFlags() {
        return this.hasRole("ROLE_EDITEVIDENCEFLAG");
    }
    public hasRoleEditTask() {
        return this.hasRole("ROLE_EDITTASK");
    }
    public hasRoleEditDueTask() {
        return this.hasRole("ROLE_EDITTASK_DUE");
    }
    /**
     * Determine what groups the user might have.
     * This could be done by working backwards from authorities (eg staff but not carer...)
     * but is cleaner using group names given in the user session.
     * Usage:
     *  'staff', true => see if the user has any of staff/manager/senior manager
     *  'manager', true => see if the user has any of manager/senior manager
     *  'senior manager', true => see if the user has senior manager
     *  <any group>, false => see if the user has <any group>
     */
    public hasTopLevelGroup(group: UserGroup, useGroupHierarchy: boolean) {
        let groups: UserGroup[] = [group];
        const staffHierarchy: UserGroup[] = ["staff", "manager", "senior manager"];

        const hierarchyIndex = staffHierarchy.indexOf(group);
        if (useGroupHierarchy && hierarchyIndex > -1) {
            groups = staffHierarchy.slice(hierarchyIndex);
        }

        return this.userFeatures?.groups?.some(g => groups.indexOf(g) > -1) || false;
    }

    hasRoleFileLimiting() {
        return SessionData.hasRoleFileLimitingList().some(r => this.hasRole(r));
    }
    public static hasRoleFileLimitingList() {
        // NB ROLE_OVERVIEWREFERRAL_TASKS_1 is about limiting to agreements within the 'tasks' tab.
        // The jsp has no concept of this since it loads the component.
        // Including it in this method (unlike referral-permission.tag) still works for depending methods.
        // NB the dependent method, hasRoleFilePermissionFor, is only for the newer referral file
        return [
            "ROLE_OVERVIEWREFERRAL",
            "ROLE_OVERVIEWREFERRAL_HIST",
            "ROLE_OVERVIEWREFERRAL_TASKS",
            "ROLE_OVERVIEWREFERRAL_TASKS_1"
        ];
    }

    public hasRoleClient() {
        return this.hasRole("ROLE_CLIENT");
    }

    public hasRoleSecurity() {
        return this.hasRole("ROLE_SECURITY");
    }

    public hasRoleCommissioner() {
        return this.hasRole("ROLE_COMMISSIONER");
    }

    hasRoleFilePermissionForTasks() {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.tasks");
        const secured = this.isEnabled("referralOverview.tasks.secured");
        const allowed = this.hasRole("ROLE_ADMINREFERRAL");
        return permission && (!secured || (secured && allowed));
    }

    hasRoleFilePermissionForSupport(serviceType: ServiceType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.support");
        const enabled = this.isEnabled("referralOverview.support");
        const hasSupport = !!serviceType.getFirstSupportTaskName(this);
        return permission && enabled && hasSupport;
    }

    hasRoleFilePermissionForSupportHistory(serviceType: ServiceType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.supportHistory");
        const enabled = this.isEnabled("referralOverview.supportHistory");
        const hasSupport = this.hasRoleFilePermissionForSupport(serviceType);
        return permission && enabled && hasSupport;
    }

    hasRoleFilePermissionForVisitHistory(serviceType: ServiceType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.visitHistory");
        const hasCare =
            this.isEnabled("rota.scheduler") ||
            !!serviceType.hasTaskDefinitionEntry(TaskNames.carePlan);
        return permission && hasCare;
    }

    fileSupportHistoryLabel(serviceType: ServiceType) {
        const supportHistoryLabel = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "supportHistoryLabel"
        );
        return supportHistoryLabel || this.getMessages()["referralView.tabs.supportHistory"];
    }

    fileVisitHistoryLabel(serviceType: ServiceType) {
        const visitHistoryLabel = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "visitHistoryLabel"
        );
        return visitHistoryLabel || this.getMessages()["referralView.tabs.visitHistory"];
    }

    fileSupportLabel(serviceType: ServiceType) {
        const label = serviceType.getTaskDefinitionSetting(TaskNames.referralView, "supportLabel");
        return label || this.getMessages()["referralView.tabs.support"];
    }

    hasSupportForwardPlan(serviceType: ServiceType): boolean {
        const firstSupportTaskName = serviceType.getFirstSupportTaskName(this);
        const hasSupportGoalPlan =
            firstSupportTaskName != undefined &&
            serviceType.taskDefinitionSettingHasFlag(
                firstSupportTaskName,
                "showActionComponents",
                "comment"
            );
        const hasSupportGoalName =
            firstSupportTaskName != undefined &&
            serviceType.taskDefinitionSettingHasFlag(
                firstSupportTaskName,
                "showActionComponents",
                "name"
            );
        const overridden = serviceType.taskDefinitionSettingHasFlag(
            TaskNames.referralView,
            "hiddenTabs",
            "forwardPlan"
        );
        return !overridden && (hasSupportGoalPlan || hasSupportGoalName);
    }

    hasRiskForwardPlan(serviceType: ServiceType): boolean {
        const firstRiskTaskName = serviceType.getFirstRiskTaskName(this);
        const hasGoalPlan =
            firstRiskTaskName != undefined &&
            serviceType.taskDefinitionSettingHasFlag(
                firstRiskTaskName,
                "showActionComponents",
                "comment"
            );
        const hasGoalName =
            firstRiskTaskName != undefined &&
            serviceType.taskDefinitionSettingHasFlag(
                firstRiskTaskName!,
                "showActionComponents",
                "name"
            );
        const overridden = serviceType.taskDefinitionSettingHasFlag(
            TaskNames.referralView,
            "hiddenTabs",
            "forwardRiskPlan"
        );
        return !overridden && (hasGoalPlan || hasGoalName);
    }

    getLimitedTaskNames(
        limitToSetting: TaskSettingName,
        serviceType: ServiceType
    ): string[] | undefined {
        let limitTo = undefined;
        if (limitToSetting) {
            const limitToTaskNamesLookup = serviceType.findTaskNamesWhereSettingHasFlag(
                limitToSetting,
                "y"
            );
            limitTo = limitToTaskNamesLookup.length > 0 ? limitToTaskNamesLookup : [];
        }
        return limitTo;
    }

    fileForwardPlanLabel(serviceType: ServiceType) {
        const label = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "forwardPlanLabel"
        );
        return label || this.getMessages()["referralView.tabs.forwardPlan"];
    }

    hasRoleFilePermissionForRisk(serviceType: ServiceType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.risk");
        const enabled = this.isEnabled("referralOverview.risk");
        const hasRisk = !!serviceType.getFirstRiskTaskName(this);
        return permission && enabled && hasRisk;
    }

    hasRoleFilePermissionForRiskHistory(serviceType: ServiceType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.riskHistory");
        const enabled = this.isEnabled("referralOverview.riskHistory");
        const hasRisk = this.hasRoleFilePermissionForRisk(serviceType);
        return permission && enabled && hasRisk;
    }

    fileRiskHistoryLabel(serviceType: ServiceType) {
        const historyLabel = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "riskHistoryLabel"
        );
        return historyLabel || this.getMessages()["referralView.tabs.riskHistory"];
    }

    fileRiskLabel(serviceType: ServiceType) {
        const historyLabel = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "riskLabel"
        );
        return historyLabel || this.getMessages()["referralView.tabs.risk"];
    }

    fileForwardRiskPlanLabel(serviceType: ServiceType) {
        const label = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "forwardRiskPlanLabel"
        );
        return label || this.getMessages()["referralView.tabs.forwardRiskPlan"];
    }

    hasRoleFilePermissionForChecklistHistory(serviceType: ServiceType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.checklistHistory");
        //var hasChecklist = !!serviceType.getFirstChecklistTaskName()
        const hasChecklist = serviceType.hasTaskDefinitionEntry("dailyRoutines");
        return permission && hasChecklist;
    }

    fileChecklistHistoryLabel(serviceType: ServiceType) {
        const historyLabel = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "checklistHistoryLabel"
        );
        return historyLabel || this.getMessages()["referralView.tabs.checklistHistory"];
    }

    hasRoleFilePermissionForFormHistory(serviceType: ServiceType, sessionData: SessionData) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.formHistory");
        const enabled = this.isEnabled("referralOverview.formHistory");
        const hasForm =
            serviceType
                .getTaskDefinitionEntries()
                .filter(
                    t =>
                        sessionData.getTaskDefinitionByName(t.getName())?.type ==
                        "EVIDENCE_CUSTOMFORM"
                ).length > 0;
        return permission && enabled && hasForm;
    }

    fileFormHistoryLabel(serviceType: ServiceType) {
        const historyLabel = serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "formHistoryLabel"
        );
        return historyLabel || this.getMessages()["referralView.tabs.formHistory"];
    }

    hasRoleFilePermissionForContacts() {
        return this.hasRoleFilePermissionFor("referralView.tabs.contacts");
    }

    hasRoleFilePermissionForRelationshipStar(
        primaryReferralId: number | undefined,
        serviceType: ServiceType
    ) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.relationshipStar");
        const hasMultipleReferralsTask = serviceType.hasTaskDefinitionEntry(
            TaskNames.newMultipleReferral
        );
        const multipleReferralsAllowed = !!primaryReferralId || hasMultipleReferralsTask;
        return permission && multipleReferralsAllowed;
    }

    hasRoleFilePermissionForAppointments() {
        return this.hasRoleFilePermissionFor("referralView.tabs.appointments");
    }

    hasRoleFilePermissionForCalendar() {
        return this.hasRoleFilePermissionFor("referralView.tabs.calendar");
    }

    // NB label is message referralView.tabs.invoices
    hasRoleFilePermissionForInvoices(serviceType: ServiceType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.invoices");
        const enabled = this.isEnabled("referralOverview.invoices");
        const hasAgreementTask = serviceType.hasTaskDefinitionEntry(
            TaskNames.agreementOfAppointments
        );
        const rota = this.isModuleEnabled("rota");
        return permission && enabled && hasAgreementTask && rota;
    }

    hasRoleFilePermissionForAttachments() {
        return this.hasRoleFilePermissionFor("referralView.tabs.attachments");
    }

    /**
     * Shows the legacy 'services' tab which was a way of showing one client file but accessing other services.
     * We can do the same now using the usual 'services' tab.
     * NB Oddly, a check is made on 'allocate to service' workflow, but should remove this (as likely used with hideServicesTab)
     */
    // NB label is message referralView.tabs.services
    hasRoleFilePermissionForLegacyServices(serviceType: ServiceType, service: Service) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.services");
        const disabled = this.isEnabled("referralOverview.hideServicesTab");
        const enabled = service.getParameterValue("allocateToServices");
        const hasAllocateToService = serviceType.hasTaskDefinitionEntry(
            TaskNames.allocateToServices
        );
        const showChildServicesTab = (enabled && !disabled) || hasAllocateToService;
        return permission && showChildServicesTab;
    }

    hasRoleFilePermissionForServices() {
        return this.hasRoleFilePermissionFor("referralView.tabs.services");
    }
    hasRoleFilePermissionForIncidents() {
        return this.hasRoleFilePermissionFor("referralView.tabs.incidents");
    }
    hasRoleFilePermissionForRepairs() {
        return this.hasRoleFilePermissionFor("referralView.tabs.repairs");
    }

    // NB A further condition of 'hasSupport' has not been transferred as its now a general 'reports' area
    hasRoleFilePermissionForDashboard() {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.reports");
        const enabled = this.isEnabled("referralOverview.dashboard.demoware");
        return permission && enabled;
    }

    hasRoleFilePermissionForCommunication(p: PrefixType) {
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.communication");
        if (p == "r") {
            const enabled = this.isEnabled("referralOverview.communication");
            return permission && enabled;
        }
        if (p == "m") {
            const enabled = this.isEnabled("repair.communication");
            return permission && enabled;
        }
        return false;
    }

    hasRoleFilePermissionForAudits() {
        const access = this.hasRoleReferralAdmin();
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.auditHistory");
        return access && permission;
    }

    hasRoleFilePermissionForAuditSearch() {
        const access = this.hasRoleStaff();
        const permission = this.hasRoleFilePermissionFor("referralView.tabs.auditSearch");
        return access && permission;
    }

    hasRoleFilePermissionFor(area: string) {
        const permission = !this.hasRoleFileLimiting();
        if (permission) {
            return true;
        }

        switch (area) {
            case "referralView.tabs.overview": // 'overview' tab
                return this.hasRole("ROLE_OVERVIEWREFERRAL");
            case "referralView.tabs.supportHistory":
                return this.hasRole("ROLE_OVERVIEWREFERRAL_HIST");
            case "referralView.tabs.tasks":
                return this.hasRole("ROLE_OVERVIEWREFERRAL_TASKS");

            default:
                return false;
        }
    }

    getGpsIfEnabled() {
        const gpsEnabled = this.isEnabled("evidence.recordLocation");
        if (gpsEnabled) {
            navigator.geolocation &&
                navigator.geolocation.getCurrentPosition(
                    position => {
                        console.info("location: %o", position);
                        return position;
                    },
                    error => {
                        console.info("error: %o", error);
                        return error;
                    }
                );
        }
        return undefined;
    }
}

/** Rich domain object for a Feature Set.
 *
 * A Feature Set is a set of votes for or against enabling a feature. */
export class FeatureSet {
    private votes = new Array<VoteEntry>();

    constructor(featureSetDto: FeatureSetDto) {
        if (featureSetDto) {
            for (let key in featureSetDto.featureVotes) {
                let vote = featureSetDto.featureVotes[key];
                this.votes.push(
                    {name: vote.name,
                        description: vote.description,
                        vote: FeatureVote[vote.defaultVote as "ENABLED_BY_DEFAULT" | "DISABLED_BY_DEFAULT"]
                    });
            }
            this.votes = this.votes.sort((a, b) => a.name.localeCompare(b.name));
        }
    }

    public getVotes(): Array<VoteEntry> {
        return this.votes;
    }
}

export interface VoteEntry {
    name: string;
    description: string;
    vote: FeatureVote;
}

export enum FeatureVote {
    ENABLED_BY_DEFAULT,
    DISABLED_BY_DEFAULT
}
