import {Result} from "@eccosolutions/ecco-common";
import {NotificationDto} from "./notifications-dto";

/**
 * Repository for managing notifications
 */
export interface NotificationRepository {
    /**
     * Get notifications for the current user
     * @param page Page number (0-based)
     * @param size Page size
     * @param unreadOnly Whether to return only unread notifications
     */
    findNotifications(
        page?: number,
        size?: number,
        unreadOnly?: boolean
    ): Promise<NotificationDto[]>;

    /**
     * Get a specific notification by ID
     * @param notificationId The notification ID
     */
    findNotificationById(notificationId: string): Promise<NotificationDto>;

    /**
     * Mark a notification as read
     * @param notificationId The notification ID
     */
    markAsRead(notificationId: string): Promise<Result>;

    /**
     * Mark all notifications as read for the current user
     */
    markAllAsRead(): Promise<Result>;

    /**
     * Get the count of unread notifications for the current user
     */
    countUnread(): Promise<number>;
}
