import {ApiClient} from "../web-api";
import {Result} from "@eccosolutions/ecco-common";
import {NotificationRepository} from "./NotificationRepository";
import {NotificationDto} from "./notifications-dto";

// TODO ideally the Ajax is in a separate project, but Notifications doesn't perhaps deserve its own

/**
 * AJAX implementation of the NotificationRepository
 */
export class NotificationAjaxRepository implements NotificationRepository {
    private readonly BASE_URL = "notifications";

    constructor(private apiClient: ApiClient) {}

    /**
     * Get notifications for the current user
     * @param page Page number (0-based)
     * @param size Page size
     * @param unreadOnly Whether to return only unread notifications
     */
    public findNotifications(
        page: number = 0,
        size: number = 20,
        unreadOnly: boolean = false
    ): Promise<NotificationDto[]> {
        const url = `${this.BASE_URL}/?page=${page}&size=${size}&unreadOnly=${unreadOnly}`;
        return this.apiClient.get<NotificationDto[]>(url);
    }

    /**
     * Get a specific notification by ID
     * @param notificationId The notification ID
     */
    public findNotificationById(notificationId: string): Promise<NotificationDto> {
        return this.apiClient.get<NotificationDto>(`${this.BASE_URL}/uuid/${notificationId}`);
    }

    /**
     * Mark a notification as read
     * @param notificationId The notification ID
     */
    public markAsRead(notificationId: string): Promise<Result> {
        return this.apiClient.post<Result>(`${this.BASE_URL}/uuid/${notificationId}/read`, null);
    }

    /**
     * Mark all notifications as read for the current user
     */
    public markAllAsRead(): Promise<Result> {
        return this.apiClient.post<Result>(`${this.BASE_URL}/read-all`, null);
    }

    /**
     * Get the count of unread notifications for the current user
     */
    public countUnread(): Promise<number> {
        return this.apiClient.get<number>(`${this.BASE_URL}/count-unread`);
    }
}
