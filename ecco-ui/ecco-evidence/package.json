{"private": true, "name": "ecco-evidence", "version": "0.0.0", "main": "./build-tsc/index.js", "typings": "./build-tsc/index.d.ts", "scripts": {"clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts,.tsx .", "build": "eslint --ext .ts,.tsx . && webpack", "lint": "eslint --ext .ts,.tsx .", "test": "echo && npm run cy:test", "cy:test": "cypress run-ct", "cy:dev": "cypress open-ct"}, "dependencies": {"@eccosolutions/ecco-mui": "0.0.0", "@eccosolutions/ecco-common": "1.8.4", "@eccosolutions/ecco-crypto": "1.0.2", "ecco-commands": "0.0.0", "ecco-components": "0.0.0", "ecco-dto": "0.0.0"}, "devDependencies": {"@testing-library/react": "^12.1.4", "@types/jest": "^29.5.2", "@bahmutov/cypress-esbuild-preprocessor": "^2.2.0", "@cypress/react": "^5.12.4", "@cypress/webpack-dev-server": "^1.8.3", "@testing-library/dom": "^8.11.3", "@testing-library/cypress": "^8.0.1", "@typescript-eslint/eslint-plugin": "^4.9.0", "@typescript-eslint/parser": "^4.9.0", "babel-loader": "^8.0.6", "cypress": "^9.5.2", "enzyme": "^3.6.0", "enzyme-adapter-react-16": "^1.5.0", "enzyme-to-json": "^3.3.4", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "fake-indexeddb": "^3.1.7", "jest": "^29.5.0", "jest-cli": "^29.5.0", "jest-config": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "shx": "0.3.2", "terser-webpack-plugin": "^2.2.1", "ts-jest": "^29.1.0", "ts-loader": "^5.3.3", "typescript": "5.1.5", "webpack": "^4.42.1", "webpack-cli": "^3.2.3"}}