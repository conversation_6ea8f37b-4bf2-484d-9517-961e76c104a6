import * as React from "react";
import {FC, useCallback, useState} from "react";
import {TabsBuilder, useServicesContext} from "ecco-components";
import {IconMenu, MenuIcon} from "@eccosolutions/ecco-mui-controls";
import {useEvidencePageContext} from "../EvidencePageRoot";
import {SessionData} from "ecco-dto";
import {QuestionAnswerState, QuestionAnswerWrapper} from "./QuestionAnswerRoot";
import {SmartStepWrapper} from "../smartsteps/SmartStepRoot";
import {MenuItem} from "@eccosolutions/ecco-mui";

/**
 * Get the questionGroups from the QuestionAnswerState, since this represents everything we want to show - its created
 * from snapshot and/or reference data (see createQuestionAnswerInits)
 */
export function getQuestionGroupTabs(
    questionAnswers: QuestionAnswerState[],
    sessionData: SessionData,
    allowSmartStep = false
) {
    const groupFromQuestion = (qId: number) => sessionData.getQuestionGroupByQuestionId(qId)!;
    const groupIds = questionAnswers.map(a => groupFromQuestion(a.questionDefId).id);
    const uniqueIds = [...new Set(groupIds)];

    const tabs = new TabsBuilder();
    uniqueIds.map(id => {
        tabs.addTab(
            sessionData.getQuestionGroupById(id)?.name || "unknown",
            <QuestionAnswerSection groupId={id} allowSmartStepMenuItem={allowSmartStep} />,
            undefined,
            "fa-question"
        );
    });
    return tabs.build();
}

/*
const QuestionAnswerSectionLayout: FC<{groupId: number}> = props => {
    return (
        <Grid container>
            <Grid item md={2}>
                &nbsp;
            </Grid>
            <Grid item md={8}>
                <QuestionAnswerSection {...props} />
            </Grid>
            <Grid item md={2}>
                &nbsp;
            </Grid>
        </Grid>
    );
};
*/

type IconMenuProps = {
    questionDefId: number;
    onAddSmartStep: () => void;
};
const Menu: FC<IconMenuProps> = props => {
    const [menuOpen, setMenuOpen] = useState(false);

    const onMenuChoice = () => {
        setMenuOpen(false);
        props.onAddSmartStep();
    };

    return (
        <IconMenu
            id={`${props.questionDefId}-menu`}
            iconComponent={<MenuIcon />}
            onClick={() => setMenuOpen(true)}
            open={menuOpen}
            onClose={() => setMenuOpen(false)}
        >
            <MenuItem title={"add smart step"} onClick={() => onMenuChoice()}>
                add smart step
            </MenuItem>
        </IconMenu>
    );
};

export const QuestionAnswerMenu: FC<{
    questionDefId: number;
    allowSmartStepMenuItem?: boolean;
}> = props => {
    const {init, dispatch} = useEvidencePageContext();
    const {sessionData} = useServicesContext();

    const relatedActionDefId = sessionData.getActionDefIdFromQuestionDefId(
        init.initData.taskName,
        props.questionDefId
    );

    const addSmartStep = useCallback(() => {
        if (relatedActionDefId) {
            dispatch({
                type: "addSmartStep",
                initData: init.initData,
                transientActionDefId: relatedActionDefId
            });
        }
    }, []);

    return (
        <>
            {props.allowSmartStepMenuItem && relatedActionDefId && (
                <Menu onAddSmartStep={addSmartStep} questionDefId={props.questionDefId} />
            )}
        </>
    );
};

/**
 * This is the tab of a questionnaire page
 */
const QuestionAnswerSection: FC<{groupId: number; allowSmartStepMenuItem: boolean}> = props => {
    const {sessionData} = useServicesContext();
    const {init, state: statePage} = useEvidencePageContext();

    // individual question and answer
    const questionAnswer = (state: QuestionAnswerState) => {
        const relatedActionDefId = sessionData.getActionDefIdFromQuestionDefId(
            init.initData.taskName,
            state.questionDefId
        );

        // avoid '0' being an option, which redners '0'
        const smartStepStates = relatedActionDefId
            ? statePage.smartSteps.filter(s => s.actionDefId == relatedActionDefId)
            : null;

        const showMenuToAddSmartStep =
            props.allowSmartStepMenuItem &&
            !!relatedActionDefId &&
            (!smartStepStates || smartStepStates.length == 0);

        return (
            <>
                {/* order is question-first, but could create component to loop questionsToActions and actionsToQuestions */}
                <QuestionAnswerWrapper
                    state={state}
                    showMenu={showMenuToAddSmartStep || false}
                    key={`${state.questionDefId}-wrapper`}
                    allowSmartStepMenuItem={props.allowSmartStepMenuItem || false}
                />

                {smartStepStates &&
                    smartStepStates.map(s => (
                        <>
                            <SmartStepWrapper
                                state={s}
                                key={`${s.controlUuid}-wrapper`}
                                showMenu={false}
                                allowQuestionAnswerMenuItem={false}
                            />
                            <br />
                            <br />
                        </>
                    ))}
                <br />
                <br />
            </>
        );
    };

    // could be done as props, but we have the context which can be stubbed
    return (
        <>
            {statePage.questionAnswers
                .filter(
                    s =>
                        sessionData.getQuestionGroupByQuestionId(s.questionDefId)!.id ==
                        props.groupId
                )
                .map(s => (
                    <div key={`${s.questionDefId}-questionid`}>{questionAnswer(s)}</div>
                ))}
        </>
    );
};
