import * as React from "react";
import {SmartStepEditLayout} from "../../smartsteps/SmartStep";
import {SmartStepStatusTransitions} from "../../smartsteps/SmartStepStatusTransitions";
import {SmartStepInit, SmartStepProps, SmartStepState} from "../../smartsteps/SmartStepRoot";
import {EvidenceGroup, EvidencePageType} from "ecco-dto";
import {EvidenceDef} from "../../domain";
import {ActionInstanceControlDataSetup} from "../../controlDataStructures";

//******
// REACT
import {render} from "@testing-library/react";

//******
// ENZYME - but this has an additional error
import {shallow} from "enzyme";
// required for react-hooks
// import {create, act} from "react-test-renderer";

//******
// TEST DATA - this could be shared, see testUtils.tsx
const evidenceDef = new EvidenceDef(
    "needsAssessment",
    EvidencePageType.assessment,
    EvidenceGroup.needs,
    undefined
);
const props: SmartStepProps = {
    init: {
        statusTransitions: new SmartStepStatusTransitions(
            evidenceDef.getEvidencePageType()!,
            false
        ),
        initData: {readOnly: false},
        initState: ActionInstanceControlDataSetup.fromEmpty(false, null)
    } as SmartStepInit,
    state: {actionInstanceUuid: null} as SmartStepState,
    stateSetter: (update: Partial<SmartStepState>) => {}
};

//******
// TEST
describe("SmartStepLayout", () => {
    it("should be defined", () => {
        expect(SmartStepEditLayout).toBeDefined();
    });
    it("should render correctly", () => {
        // ENZYME without hooks testing
        // const tree = shallow(<SmartStep {...props} />)
        // expect(tree).toMatchSnapshot();

        // ENZYME with react hooks - but causes error
        // const component = create(
        //     <SmartStepLayout {...props} />
        // );
        // let tree = component.toJSON();
        // expect(tree).toMatchSnapshot();

        // REACT
        const {asFragment} = render(<SmartStepEditLayout {...props} setEditing={() => {}} />);
        expect(asFragment()).toMatchSnapshot();
    });
});
