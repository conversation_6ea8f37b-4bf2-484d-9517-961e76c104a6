import {mount} from "@cypress/react";
import * as React from "react";
import {EvidencePageLayout, EvidencePageSetupForCommandForm} from "../../EvidencePage";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {
    sessionData,
    testQuestionnaireWithSmartStepData,
    testSupportWithOnlyQuestionnaireData,
    testSupportWithQuestionnaireData
} from "../../test-support/mockEvidence";
import {EccoAPI} from "ecco-components/EccoAPI";
import {CommandForm} from "ecco-components";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {Command} from "ecco-commands";

const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("EvidencePage qn+ss tests", () => {
    it("evidence page questionnaire + smart steps", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            {/* EvidencePageRoot - directly, when no commandForm required */}
                            {/* EvidencePageLoaderForCommandForm - when loading data */}
                            <EvidencePageSetupForCommandForm
                                initData={testQuestionnaireWithSmartStepData()}
                            >
                                <EvidencePageLayout />
                                <CommandFormTestOutput
                                    cmdEmitted={cmdEmitted}
                                    cmdEmittedDraft={cmdEmittedDraft}
                                />
                                {/* cmdEmitted here, and could show errors etc...*/}
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        /*
        // TEST pre-loaded data (2 smart steps instances)
        // CLONE of EvidencePageSmartStep.test.tsx
        cy.get('[name*="goalName"]').should("have.length", 2);
        // this is weird, but easier options fail: cy.findByLabelText("goal").first().should("eq", "pg");
        cy.get('[name*="goalName"]').first().invoke("val").should("eq", "pg");
        cy.contains("Clean bedroom");

        // click 'add smart step'
        // the first 'button' is actually a drop down on comment 'type', client status, meeting status
        cy.get("[role='button']").should("have.length", 4);
        cy.get("[role='button']").eq(3).click().get("li").eq(0).click();
        // check new smart step
        cy.contains("Go out");
        // check is green plus
        cy.get('[alt="status"]')
            .eq(4)
            .should("have.attr", "src")
            .should(
                "eq",
                "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9wFCg4SAHwfRDkAAAK1SURBVEjH7ZXPa1RXFMc/97735o0zk7SEoklQEm2VVgaRFqSmCCrSomBb6Ea6LKXgouCylP4B1ZVQwR+bWgWX6lLpwoWLICKaWAmotEr8lWqG2MnMvLx7zz1dOJmgjU20iyJ44Czu99xzvuece++58Fr+bzELbTh8s5p2F9/udvJ4qWhWAaOxKddiW5q8/fhM/btq7l+a4Njtj9eUC30DDxuXUtEMtO1kDEnUxbLKUHP03oFrP6xn4oUJjv6xdRBjqw+nLxLbSicwgKoCigsNVvZ8qo8ao8O7V4/U5otj5wMP3fhgUIJWJ5tjRLYLxVCKe/ls+XG29x8iNhUUS2y7GJ86Z8qFgY1fnqWwKIIfR3rj1Paumpj+DZEILxYvhqJZxorSECtKQ0R0t/Entj/rV822d7a/vyiCYtxfnmzeLFpKhGAQ4YkGRQkoigTt4CFYnBemWne7Dl/f/I8q4mcBL6GYi7dB4Y1CH0uXvIeo0JMOYLBEJmF56SMymcKaiFv1YXxokftWLDKZAvm/ErTyRqKAC47NA99T7dnZsakqBZPw+eD+Dnby9z2M1k4Rgho0ixaswHn1YBFNuD99nf7SOKKe2BR4M+1DUWrZXYIKBVuilj1AJEYxCoSFW+TtDKCQmnPjRxi+dwLVQG/5Xb6qHiWXJr9c+5p6/giAmVDHEBNA9Jn2zE/gogaWHEgBGtIClBk/59tyOU2XtVfJLDxtAjML3qJ9W8Zy582485Y5jchcILEpiS2Su4Cfs6vzFu+5snfLmC76JX97dt2HwFtzByykcYWgQi4Z1jyV25WfPhm9M1+c+HkETriA2rXAytlinWTtjGJkNkOjFw/uGJl46Wn6zekN3cGEtQZTVoJtu3iL/uVaPZd/3vVr+E/jukN0clPiTNMqhkJYIke+OO9f/3avhvwNLnFIKHOMt0kAAAAASUVORK5CYII="
            );*/
    });
});


describe("EvidencePage ss+qn tests", () => {
    it("evidence page smart steps + questionnaire", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            <EvidencePageSetupForCommandForm
                                initData={testSupportWithQuestionnaireData()}
                            >
                                <EvidencePageLayout />
                                <CommandFormTestOutput
                                    cmdEmitted={cmdEmitted}
                                    cmdEmittedDraft={cmdEmittedDraft}
                                />
                                {/* cmdEmitted here, and could show errors etc...*/}
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        /*
        // TEST assessment shows all - 3 smart steps instances - with prepared data
        // CLONE of EvidencePageSmartStep.test.tsx
        cy.get('[name*="goalName"]').should("have.length", 3);
        // this is weird, but easier options fail: cy.findByLabelText("goal").first().should("eq", "pg");
        cy.get('[name*="goalName"]').first().invoke("val").should("eq", "pg");
        cy.contains("Clean bedroom");

        cy.contains("Go out");
        // check is green plus
        cy.get('[alt="status"]')
            .eq(4)
            .should("have.attr", "src")
            .should(
                "eq",
                "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9wFCg4SAHwfRDkAAAK1SURBVEjH7ZXPa1RXFMc/97735o0zk7SEoklQEm2VVgaRFqSmCCrSomBb6Ea6LKXgouCylP4B1ZVQwR+bWgWX6lLpwoWLICKaWAmotEr8lWqG2MnMvLx7zz1dOJmgjU20iyJ44Czu99xzvuece++58Fr+bzELbTh8s5p2F9/udvJ4qWhWAaOxKddiW5q8/fhM/btq7l+a4Njtj9eUC30DDxuXUtEMtO1kDEnUxbLKUHP03oFrP6xn4oUJjv6xdRBjqw+nLxLbSicwgKoCigsNVvZ8qo8ao8O7V4/U5otj5wMP3fhgUIJWJ5tjRLYLxVCKe/ls+XG29x8iNhUUS2y7GJ86Z8qFgY1fnqWwKIIfR3rj1Paumpj+DZEILxYvhqJZxorSECtKQ0R0t/Entj/rV822d7a/vyiCYtxfnmzeLFpKhGAQ4YkGRQkoigTt4CFYnBemWne7Dl/f/I8q4mcBL6GYi7dB4Y1CH0uXvIeo0JMOYLBEJmF56SMymcKaiFv1YXxokftWLDKZAvm/ErTyRqKAC47NA99T7dnZsakqBZPw+eD+Dnby9z2M1k4Rgho0ixaswHn1YBFNuD99nf7SOKKe2BR4M+1DUWrZXYIKBVuilj1AJEYxCoSFW+TtDKCQmnPjRxi+dwLVQG/5Xb6qHiWXJr9c+5p6/giAmVDHEBNA9Jn2zE/gogaWHEgBGtIClBk/59tyOU2XtVfJLDxtAjML3qJ9W8Zy582485Y5jchcILEpiS2Su4Cfs6vzFu+5snfLmC76JX97dt2HwFtzByykcYWgQi4Z1jyV25WfPhm9M1+c+HkETriA2rXAytlinWTtjGJkNkOjFw/uGJl46Wn6zekN3cGEtQZTVoJtu3iL/uVaPZd/3vVr+E/jukN0clPiTNMqhkJYIke+OO9f/3avhvwNLnFIKHOMt0kAAAAASUVORK5CYII="
            );*/
    });
});

describe("EvidencePage ss+qn - only qns", () => {
    it("evidence page (support) with only questionnaire", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            <EvidencePageSetupForCommandForm
                                initData={testSupportWithOnlyQuestionnaireData()}
                            >
                                <EvidencePageLayout />
                                <CommandFormTestOutput
                                    cmdEmitted={cmdEmitted}
                                    cmdEmittedDraft={cmdEmittedDraft}
                                />
                                {/* cmdEmitted here, and could show errors etc...*/}
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );

        //cy.get('[name*="goalName"]').should("have.length", 0);
    });
});