import {mount} from "@cypress/react";
import * as React from "react";
import {EvidencePageLayout, EvidencePageSetupForCommandForm} from "../../EvidencePage";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {sessionData, testSmartStepData} from "../../test-support/mockEvidence";
import {CommandForm} from "ecco-components";
import {EccoAPI} from "ecco-components/EccoAPI";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {Command} from "ecco-commands";

const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("EvidencePage SmartStep tests", () => {
    it("evidence page smart steps", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            {/*draft save DEV-2650*/}
                            {/* EvidencePageRoot - directly, when no commandForm required */}
                            {/* EvidencePageLoaderForCommandForm - when loading data */}
                            <EvidencePageSetupForCommandForm initData={testSmartStepData()}>
                                <EvidencePageLayout />
                                <CommandFormTestOutput
                                    cmdEmitted={cmdEmitted}
                                    cmdEmittedDraft={cmdEmittedDraft}
                                />
                                {/* cmdEmitted here, and could show errors etc...*/}
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );

        // TEST pre-loaded data (2 smart steps instances)
        //cy.get('[name*="goalName"]').should("have.length", 3);
        // this is weird, but easier options fail: cy.findByLabelText("goal").first().should("eq", "pg");
        //cy.get('[name*="goalName"]').first().invoke("val").should("eq", "pg");
        cy.contains("Clean bedroom");

        // FILL IN
        cy.get('[name*="evidence-comment"]').type("comment...");
        // DATE
        //cy.get('[name*="workDate"]').type("16/05/2022");
        // DATETIME
        cy.contains("took place on").siblings("div.MuiInput-formControl").find("input").click();
        cy.contains("OK").click();

        // NB mui-specific .parent() and li not option
        cy.get('[name*="commentTypeId"]').parent().click();
        cy.findByText("item 1").click();

        //cy.findByLabelText("target").first().type("16/05/2022");
        cy.findByRole("button", {name: "submit"}).click();

        // TEST
        //cy.contains('"targetDateChange":{"from":null,"to":"2022-05-16"}');
    });
});
