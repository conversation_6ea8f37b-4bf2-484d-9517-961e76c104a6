import {
    EvidencePageType,
    Outcome,
    ServiceType,
    SmartStepStatus,
    SupportAction,
    SupportSmartStepsSnapshotDto
} from "ecco-dto";
import {EccoDate} from "@eccosolutions/ecco-common";
import {GoalUpdateCommand} from "ecco-commands";

/**
 * A way to filter through information in the snapshot.
 * The idea is that each property acts as an AND, when set.
 */
export class EvidenceDisplayOptions {

    constructor(evidencePageType: EvidencePageType) {
        // there was a consideration of filterUndo default to be by page type
        // but it should be that they always hide
    };

    public filterAchieved: boolean = true;
    public filterUndo: boolean = true;
    public allowUndoState: boolean = false;
}

/**
 * A way to filter through information in the history.
 * The idea is that each property acts as an AND, when set.
 */
export class EvidenceHistoryFilter {

    constructor() {
    };

    public useFilter = false;
    public filterTargets: boolean = false;
    public filterFlags: boolean = false;
    public filterAttachments: boolean = false;
    public filterOutSignaturesOverride: boolean = false; // used interally
    public filterTaskName: string | null = null;
    public filterCommentTypeName: string | null = null;
    public filterFromDate: EccoDate | null = null;
    public filterToDate: EccoDate | null = null;
}

export {EvidenceDef} from "ecco-dto" // TODO: Migrate usages

/**
 * Handles the 'position' of a smart step within the root/parent smart step
 * So "0-0" means the root's first child
 * "0-1-0" means the root's second child's first child
 */
export class HierarchyPosition {
    private position: string;

    public static builder(hierarchy: number, position: number): string {
        return new HierarchyPosition(hierarchy.toString() + "-" + position.toString()).asString();
    }

    constructor(position: string) {
        // ensure we have no null position - so check for null/undefined/NaN/empty string/0/false
        // and make everything a root if empty - this works for existing static or multiple smart steps
        this.position = !position ? "0" : position;
    }

    public asString() {
        return this.position;
    }

    /**
     * The hierarchy (0-based).
     * NB just counts the '-' in position.
     */
    public hierarchy(): number {
        return (this.position.match(/-/g) || []).length;
    }

    /** The order (0-based) within its hierarchy */
    public orderby(): number {
        const last = this.position.lastIndexOf("-");
        if (last > -1) {
            return Number(this.position.substr(last + 1));
        } else {
            return Number(this.position);
        }
    }

    /** Override the order by.
     * Only used since we add a new smart step without access to the end of the children
     * so we determine the order and setOrderby + 1. The alternative is to pass around all the controls
     * and use the last one to .next it - but passing around the controls feels a little invasive
     */
    public withOrderby(newOrderby: number) {
        const last = this.position.lastIndexOf("-");
        if (last > -1) {
            let newPosition = this.position.substr(0, last);
            return new HierarchyPosition(newPosition + "-" + newOrderby.toString());
        } else {
            return new HierarchyPosition(newOrderby.toString());
        }
    }

    /**
     .    * Return a position after 'this' position, which could be a new hierarchy or the end of the current list
     */
    public next(newHierarchy: boolean): HierarchyPosition {
        // start a new hierarchy, simple
        if (newHierarchy) {
            return new HierarchyPosition(this.position.concat("-0"));
            // add to the end of this list
        } else {
            const last = this.position.lastIndexOf("-");
            if (last > -1) {
                let newPosition = this.position.substr(0, last);
                let order = Number(this.position.substr(last + 1));
                return new HierarchyPosition(newPosition + "-" + (order + 1).toString());
            } else {
                const newPosition = (Number(this.position) + 1).toString();
                return new HierarchyPosition(newPosition);
            }
        }
    }

    /**
     * Return a position before 'this' position, which could be in the current list, or the end of the previous list.
     */
    public previous(): HierarchyPosition | null {
        const last = this.position.lastIndexOf("-");
        // if we have a '-' then return the position before in this list, or the end of the previous hierarchy
        if (last > -1) {
            let newPosition = this.position.substr(0, last); // chop off the last '-<order>'
            let order = Number(this.position.substr(last + 1)); // order in the last hierarchy
            if (order == 0) {
                // if we are the top child, then return the parent
                return new HierarchyPosition(newPosition);
            } else {
                // else return the previous child
                return new HierarchyPosition(newPosition + "-" + (order - 1).toString());
            }
        } else {
            // we are a root node
            const order = Number(this.position);
            if (order == 0) {
                // there is nothing before us
                return null;
            } else {
                const newPosition = (order - 1).toString();
                return new HierarchyPosition(newPosition);
            }
        }
    }
}

/** Summary of the actions for a given outcome */
export class OutcomeEvidence {
    /** Sparse array of actions by id */
    private supportActionsByActionId: { [id: number]: SupportAction } = {};

    private numWanted = 0;
    private numAchieved = 0;

    constructor(private outcome: Outcome) {
    }

    public add(supportAction: SupportAction) {
        const from = this.supportActionsByActionId[supportAction.actionId] && this.supportActionsByActionId[supportAction.actionId].status;
        const to = supportAction.status;

        // set the state we've gone to
        this.supportActionsByActionId[supportAction.actionId] = supportAction;

        // calc changes in wanted/achieved for this outcome

        // We split this in 2 to make the logic simpler
        // if from is null, do nothing, otherwise do the action that takes "from" -> null
        // then if "to" is non-null do the action that takes us from null -> "to"

        if (to == from) { return; }

        if (from == SmartStepStatus.Achieved) {
            this.numAchieved--;
            this.numWanted--;
        }
        else if (from == SmartStepStatus.WantToAchieve || from == SmartStepStatus.Unachieved) {
            this.numWanted--;
        }

        if (to == SmartStepStatus.Achieved) {
            this.numAchieved++;
            this.numWanted++;
        }
        else if (to == SmartStepStatus.WantToAchieve  || to == SmartStepStatus.Unachieved) {
            this.numWanted++;
        }
    }

    /** Allow outgoing changes to be applied - e.g. those generated by AddGoalForm and EditGoalForm */
    public applyCommand(cmd: GoalUpdateCommand) {
        const actionId = cmd.getActionDefId();
        // TODO check this FIXME
        console.warn("FIXME: OutcomeEvidence snapshot missing functionality for actionInstanceUuid");
        // var instanceUuid = cmd.getActionInstanceUuid();


        switch(cmd.getOperation()) {
            case "add":
                const to: SmartStepStatus = cmd.getStatusChange()
                    ? <SmartStepStatus>cmd.getStatusChange()!!.to
                    : SmartStepStatus.WantToAchieve;
                this.add(this.createSupportAction(actionId, to, true));
                return;
            case "update":
                const toUpdate: SmartStepStatus = cmd.getStatusChange()
                    ? <SmartStepStatus>cmd.getStatusChange()!!.to
                    : this.supportActionsByActionId[actionId].status;
                // NB we assume that 'statusChange' has been sanitised - eg from/to aren't the same
                // so that we can simply test for null
                this.add(this.createSupportAction(actionId, toUpdate, cmd.getStatusChange() == null));
                return;
            case "remove":
                throw new Error("not implemented");
        }
    }

    private createSupportAction(actionDefId: number, status: SmartStepStatus, smartStepChange: boolean,
        actionInstanceUuid?: string): SupportAction {
        return {
            actionId: actionDefId,
            actionInstanceUuid: actionInstanceUuid || null!,
            parentActionInstanceUuid: null,
            status: status,
            statusChange: smartStepChange,
            id: null, // Don't need id and remainder
            workId: null,
            workDate: "",
            targetDateTime: null,
            expiryDate: null,
            name: null,
            goalName: null,
            goalPlan: null,
            score: null,
            statusChangeReasonId: null,
            hierarchy: null,
            position: null
        };
    }

    public getMaxNum() { return this.outcome.getNumActions(); }

    public getNumWanted() { return this.numWanted; }

    public getNumAchieved() { return this.numAchieved; }

    public getOutcome() { return this.outcome; }
}


export class SupportSmartStepsSnapshot {

    /** Sparse array of actions by id */
    private supportActionsByActionId: { [id: number]: SupportAction } = {};

    private outcomeEvidenceByOutcomeId: { [id: number]: OutcomeEvidence } = {};

    /** For iterating over - must be same object as above ByOutcomeId */
    private outcomeEvidences = new Array<OutcomeEvidence>();


    constructor(private serviceType: ServiceType, snapshotDto?: SupportSmartStepsSnapshotDto) {
        serviceType.getOutcomes().forEach( (outcome) => {
            const outcomeEvidence = new OutcomeEvidence(outcome);
            this.outcomeEvidenceByOutcomeId[outcome.getId()] = outcomeEvidence;
            this.outcomeEvidences.push(outcomeEvidence);
        });
        if (snapshotDto) {
            snapshotDto.latestActions.forEach( (dto: SupportAction) => {
                this.addSupportActionEvidence(dto);
            });
        }
    }

    public getSupportActionByActionId( actionId: number ) {
        return this.supportActionsByActionId[actionId];
    }

    public addSupportActionEvidence(action: SupportAction) {
        // add the action array, because this is called by 'getAllActiveActions'
        this.supportActionsByActionId[action.actionId] = action;
        // check the current service type has the action configured in the outcome - if not, we can't add it
        let outcomeForActionId = this.serviceType.getOutcomeForActionId(action.actionId);
        if (outcomeForActionId) {
            const outcomeId = outcomeForActionId.getId();
            this.outcomeEvidenceByOutcomeId[outcomeId].add(action);
        }
    }

    /** Allow dynamic changes to be applied - e.g. those generated by AddGoalForm and EditGoalForm */
    public applyCommand(cmd: GoalUpdateCommand) {
        const outcomeId = this.serviceType.getOutcomeForActionId(cmd.getActionDefId())!!.getId();
        this.outcomeEvidenceByOutcomeId[outcomeId].applyCommand(cmd);
    }

    public getOutcomeEvidence() {
        return this.outcomeEvidences;
    }

    /** Return an array of all actions that have been added to this referral */
    public getAllActiveActions(): SupportAction[] {
        return Object.values(this.supportActionsByActionId);
    }
}


export class SmartStepTargetDate {
    constructor(public smartStepId: number, public targetDate: string) { }

    public matches(smartStepId: number, targetDate: string) {
        return this.smartStepId == smartStepId && this.targetDate == targetDate;
    }
}

export class SmartStepComment {
    constructor(public smartStepId: number, public comment: string) { }

    public matches(smartStepId: number, comment: string) {
        return this.smartStepId == smartStepId && this.comment == comment;
    }
}

export class SmartStepScore {
    constructor(public smartStepId: number, public score: number) { }

    public matches(smartStepId: number, score: number) {
        return this.smartStepId == smartStepId && this.score == score;
    }
}

export class ActivitySchedule {
    public collectionId: number | null = null; // allows us to know this object in the code without comparing all the fields

    constructor(public id: number, public agreementId: number, public start: string, public activityType: string,
        public days: number[], public time: string, public end: string, public agreedDurationInMinutes: number) {
    }

    public copyPropertiesFrom(source: ActivitySchedule) {
        this.start = source.start;
        this.activityType = source.activityType;
        // days the activity is appropriate for (Sun 1...Sat 7)
        this.days = source.days;
        this.time = source.time;
        this.end = source.end;
        this.agreedDurationInMinutes = source.agreedDurationInMinutes;
    }
}

/**
 * Initial state of activitySchedules with smart steps
 */
export class ActivityScheduleSmartSteps {
    activitySchedule: ActivitySchedule | null = null;
    smartStepIds: number[] = [];
}

/**
 * Linking class used by arrays to record changes
 */
export class ActivityScheduleIdSmartStep {
    collectionId: number | null = null;
    smartStepId: number | null = null;
}
