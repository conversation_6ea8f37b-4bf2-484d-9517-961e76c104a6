import {mount} from "@cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {Command} from "ecco-commands";
import {CommandFormTest, CommandFormTestOutput} from "../../cmd-queue/testUtils";
import {
    Agreement,
    AgreementDto,
    RotaAjaxRepository,
    RotaRepository
} from "ecco-rota";
import {CommandForm} from "../../cmd-queue/CommandForm";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {AgreementsView} from "../../agreements/ServiceAgreementsView";
import {sessionData} from "../../__tests__/testUtils";
import {
    ServiceRecipient,
    ServiceRecipientAjaxRepository,
    ServiceRecipientRepository
} from "ecco-dto";

const rotaRepository = getFailAllMethodsMock<RotaRepository>(RotaAjaxRepository);
rotaRepository.findAgreementsByServiceRecipientId = (srId: number) =>
    Promise.resolve(agreements.map(a => new Agreement(a)));

const serviceRecipientRepository = getFailAllMethodsMock<ServiceRecipientRepository>(
    ServiceRecipientAjaxRepository
);
serviceRecipientRepository.findOneServiceRecipientById = (srId: number) => {
    const d = {
        contactId: 99,
        contactName: "Me - 99",
        calendarId: "my-cal-id"
    } as any as ServiceRecipient;
    return Promise.resolve(d);
};

const overrides = {
    rotaRepository: rotaRepository,
    serviceRecipientRepository: serviceRecipientRepository,
    sessionData: sessionData
} as EccoAPI;

describe("CareScheduleList tests", () => {
    it("schedule list", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            <AgreementsView serviceRecipientId={99} />
                            <CommandFormTestOutput
                                cmdEmitted={cmdEmitted}
                                cmdEmittedDraft={cmdEmittedDraft}
                            />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        cy.viewport(1250, 750);
    });
});

// https://croy.e.c.u/croy/nav/referrals/32/
// https://croy.e.c.u/croy/api/rota/agreements/serviceRecipient/200070/
const agreements: AgreementDto[] = [{
    "serviceRecipientId": 200070,
    "agreementId": 100811,
    "contractId": null,
    "contractName": null,
    "serviceId": 100170,
    "serviceRecipientName": "SA Ahmad",
    "parameters": {},
    "agreedHours": 9.0,
    "start": "2020-01-04",
    "end": null,
    "demandSchedules": [{
        "agreementId": 100811,
        "scheduleId": 118721,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "83f7c709-02b6-412d-aead-64634e1702ac",
        "title": "dom+parent=null+previousScheduleId=111276",
        "start": "2022-06-13",
        "time": "10:45:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": " Kitchen : Mop floors in the kitchen :Wipe down cupboards; Wipe and clean the inside and outside of the microwave ; Wipe Kitchen sides down ; Wash up, dry up and put away dishes ; Check for out date food and ensure the fridge is left clean ( ensure you gain consent before throwing away food) ; Clean the bin lid; Change the bin.  Bathroom : Mop floor in bathroom ; Clean the toilet bowl; Wipe bathroom basin; Clean the shower curtains ; Record and report any issues in the bathroom.  Lounge:\tHoover lounge area, please ensure you hoover round the chairs/ sweep or mop the lounge areas ; Dusting around the window and tv. Bedroom : Strip bed linen and put clean bed linen on ; Hoover/Mop/ sweep ; Take down the laundry and wash it; \tlabel the laundry; Make sure the laundry is returned to the flat and put away. "},
        "previousScheduleId": 111276,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": null,
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/118721/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 111276,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "7e5382e3-3f15-4ee5-a677-45feb729b1ca",
        "title": "dom+parent=null+previousScheduleId=110573",
        "start": "2021-05-14",
        "time": "10:45:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "cleaning the flat "},
        "previousScheduleId": 110573,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2022-06-12",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/111276/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 116565,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "707b8289-7b29-4488-ab5f-b6f6ed0544c4",
        "title": "lunch+parent=null+previousScheduleId=116564",
        "start": "2022-02-24",
        "time": "12:45:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 2,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer eye drops "},
        "previousScheduleId": 116564,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2022-05-31",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/116565/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 116564,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "394b3a32-6b49-41c7-8a1a-34aed0138c15",
        "title": "lunch+parent=null+previousScheduleId=null",
        "start": "2020-01-04",
        "time": "12:30:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 2,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer eye drops "},
        "previousScheduleId": null,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2022-02-23",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/116564/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 115156,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "e28864f0-99c2-4c47-a738-254fabb7985e",
        "title": "am+parent=null+previousScheduleId=115153",
        "start": "2022-01-03",
        "time": "10:15:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication, breakfast and tidy up kitchen area"},
        "previousScheduleId": 115153,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2030-01-01",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/115156/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 110468,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "75fc724f-5d6b-4da4-b091-c39cddf40222",
        "title": "am+parent=null+previousScheduleId=110467",
        "start": "2021-04-19",
        "time": "09:00:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication, breakfast and tidy up kitchen area"},
        "previousScheduleId": 110467,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2022-01-02",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/110468/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 115153,
        "serviceRecipientId": 200070,
        "eventRef": "356f5a8f-d246-41d2-89ed-3aa335f2f836",
        "title": "am+parent=null+previousScheduleId=110468",
        "start": "2022-01-03",
        "time": "10:00:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication, breakfast and tidy up kitchen area"},
        "previousScheduleId": 110468,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2022-01-02",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/115153/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 110573,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "b04067f8-7d9f-4b75-aed8-5a389714df25",
        "title": "dom+parent=null+previousScheduleId=110571",
        "start": "2021-04-26",
        "time": "14:00:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "cleaning the flat "},
        "previousScheduleId": 110571,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2021-05-13",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/110573/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 110574,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "cc7bf0b9-e68e-497c-acb1-bd56c9ad3510",
        "title": "pm+parent=null+previousScheduleId=108265",
        "start": "2021-04-26",
        "time": "18:30:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 4,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication "},
        "previousScheduleId": 108265,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": null,
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/110574/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 110571,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "30a14637-9aec-40b5-a6d7-2c10c38b974d",
        "title": "dom+parent=null+previousScheduleId=109966",
        "start": "2021-04-26",
        "time": "02:00:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "cleaning the flat "},
        "previousScheduleId": 109966,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2021-04-25",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/110571/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 109966,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "5d45e51d-6f9a-4d2d-832b-31a6faabdb85",
        "title": "dom+parent=null+previousScheduleId=108263",
        "start": "2021-03-29",
        "time": "11:30:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "cleaning the flat "},
        "previousScheduleId": 108263,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2021-04-25",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/109966/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 108265,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "ce4f9de8-733a-48f7-8f43-50af406282f9",
        "title": "pm+parent=null+previousScheduleId=107551",
        "start": "2021-02-15",
        "time": "19:00:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 4,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication "},
        "previousScheduleId": 107551,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-04-25",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/108265/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 109917,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "1dc74266-a147-4919-90a9-739251dc4fbd",
        "title": "am+parent=null+previousScheduleId=109788",
        "start": "2021-03-29",
        "time": "08:45:00.000",
        "durationMins": 30,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication, breakfast and tidy up kitchen area"},
        "previousScheduleId": 109788,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-04-18",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/109917/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 110467,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "2625ba43-1465-4493-b2d6-e128d1b389ac",
        "title": "am+parent=null+previousScheduleId=109917",
        "start": "2021-04-19",
        "time": "09:00:00.000",
        "durationMins": 30,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication, breakfast and tidy up kitchen area"},
        "previousScheduleId": 109917,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-04-18",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/110467/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 109787,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "2429a880-ed12-448d-868d-aa3604e6f92c",
        "title": "tea+parent=null+previousScheduleId=108264",
        "start": "2021-03-29",
        "time": "16:00:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 3,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication "},
        "previousScheduleId": 108264,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": null,
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/109787/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 107807,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "5cb999a4-10b4-4ab5-b367-01d3e8b0cd62",
        "title": "am+parent=null+previousScheduleId=100812",
        "start": "2021-01-25",
        "time": "09:00:00.000",
        "durationMins": 30,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication, offer tea, tidy up "},
        "previousScheduleId": 100812,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-03-28",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/107807/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 109788,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "efb1a1c9-8a39-4c07-8bbf-816182f2af14",
        "title": "am+parent=null+previousScheduleId=107807",
        "start": "2021-03-29",
        "time": "09:00:00.000",
        "durationMins": 30,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication, breakfast and tidy up kitchen area"},
        "previousScheduleId": 107807,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-03-28",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/109788/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 108263,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "46d59d44-0af2-4be4-8875-24b2982c3ec6",
        "title": "dom+parent=null+previousScheduleId=107745",
        "start": "2021-02-15",
        "time": "11:00:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "cleaning the flat "},
        "previousScheduleId": 107745,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2021-03-28",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/108263/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 108264,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "ec6d7da1-1914-4c49-99eb-995c4a668b1c",
        "title": "tea+parent=null+previousScheduleId=100813",
        "start": "2021-02-15",
        "time": "15:30:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 3,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {"tasks": "administer medication "},
        "previousScheduleId": 100813,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-03-28",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/108264/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 107745,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "2d2553c8-206f-43a6-8595-b6b6b7c1f979",
        "title": "dom+parent=null+previousScheduleId=100815",
        "start": "2021-01-25",
        "time": "11:00:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": 100815,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2021-02-14",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/107745/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 100813,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "beb8cccf-c8ab-4310-9c30-6960445e0ac2",
        "title": "tea+parent=null+previousScheduleId=null",
        "start": "2020-01-04",
        "time": "15:30:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 3,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": null,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-02-14",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/100813/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 107551,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "df1a7a94-bcc4-44a2-b4b0-9be42d819031",
        "title": "pm+parent=null+previousScheduleId=100814",
        "start": "2021-01-11",
        "time": "19:00:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 4,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": 100814,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-02-14",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/107551/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 100812,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "9f5965e1-8d92-41ca-886f-dd2c8ebb4e02",
        "title": "am+parent=null+previousScheduleId=null",
        "start": "2020-01-04",
        "time": "08:00:00.000",
        "durationMins": 30,
        "intervalFrequency": 1,
        "categoryId": 1,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": null,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-01-24",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/100812/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 100815,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "bef08f6a-2e95-4d38-b3d1-39c4ef99b333",
        "title": "dom+parent=null+previousScheduleId=null",
        "start": "2020-01-04",
        "time": "14:00:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 6,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": null,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2021-01-24",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/100815/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 100814,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "52412301-be81-452c-bf39-6093dc12bc36",
        "title": "pm+parent=null+previousScheduleId=null",
        "start": "2020-01-04",
        "time": "19:30:00.000",
        "durationMins": 15,
        "intervalFrequency": 1,
        "categoryId": 4,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": null,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [1, 2, 3, 4, 5, 6, 7],
        "end": "2021-01-10",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/100814/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 107183,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "6fd682df-a814-4f25-91f7-424d5a60f853",
        "title": "laundry+parent=null+previousScheduleId=100816",
        "start": "2020-12-01",
        "time": "03:00:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 7,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": 100816,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [3],
        "end": null,
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/107183/taskDirectHandles"
        }]
    }, {
        "agreementId": 100811,
        "scheduleId": 100816,
        "serviceRecipientId": 200070,
        "serviceRecipientName": "SA Ahmad",
        "eventRef": "5a2b5476-9d61-417c-a625-ff76e64da91e",
        "title": "laundry+parent=null+previousScheduleId=null",
        "start": "2020-01-04",
        "time": "15:00:00.000",
        "durationMins": 60,
        "intervalFrequency": 1,
        "categoryId": 7,
        "rateCardId": null,
        "rateCardName": null,
        "parameters": {},
        "previousScheduleId": null,
        "parentScheduleId": null,
        "childScheduleIds": [],
        "calendarDays": [2],
        "end": "2020-11-30",
        "adHoc": false,
        "links": [{
            "rel": "demand-schedule-task-handles",
            "href": "https://croy.e.c.uk/croy/api/rota/agreements/schedules/100816/taskDirectHandles"
        }]
    }],
    "links": [{
        "rel": "appointments",
        "href": "https://croy.e.c.uk/croy/api/rota/agreements/100811/appointments/"
    }]
} as any as AgreementDto]
