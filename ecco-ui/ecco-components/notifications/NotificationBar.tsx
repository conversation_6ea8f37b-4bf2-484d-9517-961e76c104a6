import * as React from "react";
import {Component} from "react";
import * as ReactDOM from "react-dom";
import {bus} from "@eccosolutions/ecco-common";
import {EccoTheme} from "../theme";
import {But<PERSON>, Snackbar} from "@eccosolutions/ecco-mui";
import Element = JSX.Element;

export class NotificationEvent {
    constructor(
        public key: string,
        public message: string,
        public actionText?: string,
        public actionCallback?: () => void
    ) {}
}

const notificationBar = bus<NotificationEvent>();

export function add(
    key: string,
    message: string,
    actionText?: string,
    actionCallback?: () => void
) {
    notificationBar.fire(new NotificationEvent(key, message, actionText, actionCallback));
}

interface Props {}

interface State {
    /** Key for knowing if a subsequent notification is an update to the one being displayed */
    key: string | null;
    message?: string;
    actionText?: string;
    actionCallback?: () => void;
}

/**
 * The intent here is to allow notifications to be registered by raising an event, and for a sequence of notifications
 * to do appropriate things.
 * We should allow a key to discriminate notifications so that we can potentially update an already showing one, such
 * that a notification popup can be updated if it is already visible, but otherwise
 */
class Notifications extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = {
            key: null,
            message: ""
        };

        notificationBar.addHandler(event => this.handleEvent(event!!));
    }

    private handleEvent(event: NotificationEvent) {
        // TODO: don't overwrite previous event but instead queue them unless key is the same in which case can overwrite
        this.setState({
            key: event.key,
            message: event.message,
            actionText: event.actionText,
            actionCallback: event.actionCallback
        });
    }

    private handleSnackbarClose() {
        this.setState({key: null});
    }

    render() {
        return (
            <Snackbar
                open={this.state.key !== null}
                message={<span>{this.state.message}</span>}
                action={
                    this.state.actionText && (
                        <Button
                            key={this.state.key!!}
                            color="secondary"
                            size="small"
                            onClick={this.state.actionCallback}
                        >
                            {this.state.actionText}
                        </Button>
                    )
                }
                autoHideDuration={2500}
                onClose={() => this.handleSnackbarClose()}
            />
        );
    }
}
export const component: Element = <Notifications />;

let snackbarEl = document.querySelector("#snackbar");
if (!snackbarEl) {
    snackbarEl = document.createElement("div");
    snackbarEl.setAttribute("id", "snackbar");
    document.body.appendChild(snackbarEl);
}

// If we're on a page with this element, then attach to it
ReactDOM.render(
    <EccoTheme prefix="not">
        <Notifications />
    </EccoTheme>,
    snackbarEl
);
