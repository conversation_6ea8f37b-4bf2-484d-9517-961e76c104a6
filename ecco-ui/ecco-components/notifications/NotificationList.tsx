import * as React from "react";
import {useState, useEffect} from "react";
import {
    List,
    ListItem,
    ListItemText,
    ListItemSecondaryAction,
    IconButton,
    Typography,
    Paper,
    Divider,
    Badge,
    Button,
    CircularProgress,
    Box
} from "@eccosolutions/ecco-mui";
import {
    /*EmailReadIcon,*/
    EmailUnreadIcon,
    NotificationsIcon
} from "@eccosolutions/ecco-mui-controls";
/*import { formatDistanceToNow } from "date-fns";*/
import {NotificationDto, NotificationAjaxRepository, NotificationRepository} from "ecco-dto";
import {useServicesContext} from "../ServicesContext";

/**
 * Component for displaying user notifications
 */
export const NotificationList: React.FC = () => {
    const {notificationRepository} = useServicesContext();

    const [notifications, setNotifications] = useState<NotificationDto[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [unreadCount, setUnreadCount] = useState<number>(0);
    const [showUnreadOnly, setShowUnreadOnly] = useState<boolean>(false);

    // Load notifications on component mount and when showUnreadOnly changes
    useEffect(() => {
        loadNotifications();
    }, [showUnreadOnly]);

    // Load notifications from the API
    const loadNotifications = () => {
        setLoading(true);
        notificationRepository
            .findNotifications(0, 20, showUnreadOnly)
            .then(response => {
                setNotifications(response);

                // Count unread notifications
                const unread = response.filter(n => n.readAt == null).length;
                setUnreadCount(unread);

                setError(null);
                setLoading(false);
            })
            .catch(err => {
                console.error("Failed to load notifications", err);
                setError("Failed to load notifications. Please try again later.");
                setLoading(false);
            });
        /*.finally(() => {
                setLoading(false);
            });*/
    };

    // Mark a notification as read
    const markAsRead = (notificationId: string) => {
        notificationRepository
            .markAsRead(notificationId)
            .then(() => {
                // Update local state
                setNotifications(prev =>
                    prev.map(n =>
                        n.id === notificationId ? {...n, readAt: new Date().toISOString()} : n
                    )
                );

                // Update unread count
                setUnreadCount(prev => Math.max(0, prev - 1));
            })
            .catch(err => {
                console.error("Failed to mark notification as read", err);
            });
    };

    // Mark all notifications as read
    const markAllAsRead = () => {
        notificationRepository
            .markAllAsRead()
            .then(() => {
                // Update local state
                setNotifications(prev =>
                    prev.map(n => ({...n, read: true, readAt: new Date().toISOString()}))
                );

                // Update unread count
                setUnreadCount(0);
            })
            .catch(err => {
                console.error("Failed to mark all notifications as read", err);
            });
    };

    // Format the notification date
    const formatDate = (dateString: string) => {
        try {
            return dateString;
            //return formatDistanceToNow(new Date(dateString), { addSuffix: true });
        } catch (err) {
            return "Unknown date";
        }
    };

    // Get icon for notification type
    const getNotificationIcon = (type: string) => {
        switch (type) {
            case "SMS":
                return <NotificationsIcon color="primary" />;
            default:
                return <NotificationsIcon />;
        }
    };

    if (loading && notifications.length === 0) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress />
            </Box>
        );
    }

    return (
        <Paper elevation={2} style={{maxHeight: 400, overflow: "auto"}}>
            <Box p={2} display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">
                    <Badge badgeContent={unreadCount} color="primary">
                        Notifications
                    </Badge>
                </Typography>
                <Box>
                    <Button
                        size="small"
                        onClick={() => setShowUnreadOnly(!showUnreadOnly)}
                        color={showUnreadOnly ? "primary" : "default"}
                    >
                        {showUnreadOnly ? "Show All" : "Show Unread Only"}
                    </Button>
                    {unreadCount > 0 && (
                        <Button
                            size="small"
                            startIcon={<EmailUnreadIcon />}
                            onClick={markAllAsRead}
                            style={{marginLeft: 8}}
                        >
                            Mark All Read
                        </Button>
                    )}
                </Box>
            </Box>

            <Divider />

            {error && (
                <Box p={2} display="flex" alignItems="center" color="error.main">
                    {/*<Error color="error" style={{ marginRight: 8 }} />*/}
                    <Typography color="error">{error}</Typography>
                </Box>
            )}

            {notifications.length === 0 ? (
                <Box p={4} textAlign="center">
                    <Typography color="textSecondary">
                        {showUnreadOnly ? "No unread notifications" : "No notifications found"}
                    </Typography>
                </Box>
            ) : (
                <List>
                    {notifications.map(notification => (
                        <React.Fragment key={notification.id}>
                            <ListItem
                                button
                                onClick={() => {}}
                                style={{
                                    backgroundColor:
                                        notification.readAt == null
                                            ? "transparent"
                                            : "rgba(0, 0, 255, 0.05)"
                                }}
                            >
                                <Box mr={2}>{getNotificationIcon("SMS")}</Box>
                                <ListItemText
                                    primary={"some content"}
                                    secondary={formatDate(notification.created)}
                                />
                                {notification.readAt == null && (
                                    <ListItemSecondaryAction>
                                        <IconButton
                                            edge="end"
                                            aria-label="mark as read"
                                            onClick={e => {
                                                e.stopPropagation();
                                                markAsRead(notification.id);
                                            }}
                                        >
                                            <EmailUnreadIcon />
                                        </IconButton>
                                    </ListItemSecondaryAction>
                                )}
                            </ListItem>
                            <Divider component="li" />
                        </React.Fragment>
                    ))}
                </List>
            )}
        </Paper>
    );
};
