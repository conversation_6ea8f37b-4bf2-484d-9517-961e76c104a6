import * as React from "react";
import {FC, useState} from "react";
import {Badge, Icon, IconButton} from "@eccosolutions/ecco-mui";
import {EccoV3Modal} from "../EccoV3Modal";
import {EccoTheme} from "../theme";
import {NotificationList} from "./NotificationList";
import {useServicesContext} from "../ServicesContext";
import {useEffect} from "react";

export const NotificationMenu: FC<{uuid?: string}> = ({uuid}) => {
    const [open, setOpen] = useState(false);
    const [unreadCount, setUnreadCount] = useState(0);
    const {notificationRepository} = useServicesContext();

    // Load unread notification count on mount and when modal is closed
    useEffect(() => {
        if (!open) {
            loadUnreadCount();
        }
    }, [open]);

    // Load unread notification count
    const loadUnreadCount = () => {
        notificationRepository
            .countUnread()
            .then(count => {
                setUnreadCount(count);
            })
            .catch(err => {
                console.error("Failed to load unread notification count", err);
            });
    };

    return (
        <>
            {open && (
                <EccoTheme prefix="notify">
                    <EccoV3Modal
                        title={"Notifications"}
                        show={true}
                        onCancel={() => setOpen(false)}
                        action="close"
                        maxWidth="md"
                    >
                        <NotificationList />
                    </EccoV3Modal>
                </EccoTheme>
            )}
            <IconButton aria-label="notifications" onClick={() => setOpen(true)}>
                <Badge badgeContent={unreadCount} color="primary">
                    <Icon className={"fa fa-bell"} />
                </Badge>
            </IconButton>
        </>
    );
};
NotificationMenu.displayName = "NotificationMenu";
