import {EccoDate, EccoTime, SelectListOption} from "@eccosolutions/ecco-common";

import {
    Button,
    Checkbox,
    FormControlLabel,
    Link,
    MenuItem,
    TextField
} from "@eccosolutions/ecco-mui";
import {PropInjector} from "@material-ui/types";
import * as React from "react";
import {ComponentType, FC, MouseEventHandler, MutableRefObject, ReactChild, ReactElement, ReactNode} from "react";
import * as ReactDom from "react-dom";
import {render, unmountComponentAtNode} from "react-dom";
import {EccoV3EditPane, EccoV3Modal} from "./EccoV3Modal";

import {ErrorBoundary} from "./ErrorBoundary";
import {DialogAction} from "./forms";
import * as Notifications from "./notifications/NotificationBar";
import {DomElementContainer} from "./react-adapters";
import {calcPatternValidationState} from "./stateUtils";
import {useAutoSave} from "./hooks/useAutoSave";
import {EccoTheme} from "./theme";
import {
    createDateTimePickerInput,
    createTimePickerInput,
    DatePickerEccoDate,
} from "@eccosolutions/ecco-mui-controls";
// eslint-disable-next-line no-restricted-imports
import {InputProps as StandardInputProps} from "@material-ui/core/Input/Input";

/**Adapter to help with migration
 * e.g. return componentAsElement(<DateInput defaultDate={this.date && this.date.toLocalJsDate()} />, "startDate1").element();
 * @param reactElement element
 * @param elementId A DOM elementId to use, so that we can unmount previous component at this node when our JQueryish code re-renders
 * @param htmlTag tag
 */
export function componentAsElement<P>(
    reactElement: ReactElement<P>,
    elementId: string, // Will need to generate ids where component is used multiple times - but must be same between renders
    htmlTag = "div"): Element {
    let mountPoint = document.getElementById(elementId);
    if (mountPoint) {
        unmountComponentAtNode(mountPoint);
    }
    else {
        mountPoint = document.createElement(htmlTag);
        mountPoint.setAttribute("id", elementId);
    }

    ReactDom.render(reactElement, mountPoint);
    return mountPoint;
}


/*
 * This file is an attempt to isolate ourselves from the changing react-bootstrap API, and promote
 * our own standard ways of doing things, such that if we dropped react-bootstrap, we can use our own
 * components instead
 */

export function idNameToMenuItem(idName: SelectListOption) {
    return (
        <MenuItem key={idName.id}
                  value={String(idName.id)}
                  disabled={idName.disabled}
        >{idName.name}</MenuItem>);
}

/** @returns No validation state if not required, otherwise "success" or "error" */
function calcValidationState(required: boolean | undefined, value: EccoDate | string | number | boolean | null) {
    return required ? value == null ? "error" : "success" : null;
}

function numberFromHtmlInput(eventTarget: EventTarget, decimalPlaces: number): number | null {
    let value = (eventTarget as HTMLInputElement).value;
    if (!value) {
        return null;
    }

    // parse and take the number as-is to the required dp (at least for positive numbers - otherwise use .round)
    let valueNum = parseFloat(value);
    valueNum = Math.floor(valueNum * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
    return valueNum;
}

// copied from stateUtils
function stringFromHtmlInput(eventTarget: EventTarget): string | null {
    let value = (eventTarget as HTMLInputElement).value;
    return value ? value.toString() : null;
}

/**
 * Filter out disabled options that aren't the current value (so the current value displays)
 * NB We don't pass the current value in as the Client model refers to strings.
 * @param {number} currentValue to remain in the list so that the correct data is displayed for existing data
 * @param indexes list
 * @return filtered list or empty list if indexes was null
 */
function getOptionsNotDisabled(indexes: SelectListOption[] | null, currentValue: number | string | null): SelectListOption[] {
    return indexes ? indexes.filter(item => !item.disabled || item.id == currentValue) : [];
}

function resolvePath<T, R>(obj: T, key: Extract<keyof T, string>) {
    return obj[key] as unknown as R;
}

function cloneWithValue<STATE>(state: STATE, propertyKey: string, value: any) {
    // return update(state, createDelta<STATE>(propertyPath, value));
    return {...state, [propertyKey]:value};
}
/** newState is a clone of state with state[propertyKey] updated to the new value */
export function textInput<STATE>(
    propertyKey: Extract<keyof STATE, string>,
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    placeholder?: string,
    disabled?: boolean,
    required = false,
    maxLength?: number,
    size?: "small" | "medium"
): JSX.Element {
    return createTextInput(
        propertyKey,
        label,
        resolvePath(state, propertyKey),
        value => {
            stateSetter(cloneWithValue(state, propertyKey, value));
        },
        "text",
        {placeholder, disabled, required, maxLength, size}
    );
}

export function passwordInput<STATE>(
    propertyKey: Extract<keyof STATE, string>,
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    placeholder?: string,
    disabled?: boolean,
    required = false,
    autocompleteOff = false
): JSX.Element {
    return createTextInput(
        propertyKey,
        label,
        resolvePath(state, propertyKey),
        value => {
            stateSetter(cloneWithValue(state, propertyKey, value));
        },
        "password",
        {placeholder, disabled, required, autocompleteOff}
    );
}

/** newState is a clone of state with state[propertyKey] updated to the new value */
export function emailInput<STATE>(
    propertyKey: Extract<keyof STATE, string>,
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    placeholder?: string,
    disabled?: boolean,
    required = false
): JSX.Element {
    return createTextInput(propertyKey, label,
                           state[propertyKey] as unknown as string,
                           value => {stateSetter({...state, [propertyKey]:value})},
                           "text", {placeholder, disabled, required},
                           calcPatternValidationState(/^.+@.+\..+$/));
}

/** newState is a clone of state with state[propertyKey] updated to the new value */
export function phoneInput<STATE>(
    propertyKey: Extract<keyof STATE, string>,
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    placeholder?: string,
    disabled?: boolean,
    required = false
): JSX.Element {
    return createTextInput(propertyKey, label,
                           state[propertyKey] as unknown as string, // TODO: Hacked because we cannot make propertyKey just those that map to EccoDate (AFAIK)
                           value => {stateSetter({...state, [propertyKey]:value})},
                           "text",
                           {placeholder, disabled, required},
                           calcPatternValidationState(/^\+?[0-9 ]+$/));
}

/** newState is a clone of state with state[propertyKey] updated to the new value */
export function timePicker<STATE>(propertyKey: Extract<keyof STATE, string>,
                                label: ReactNode,
                                stateSetter: (newState: STATE) => void,
                                state: STATE,
                                disabled?: boolean,
                                required = false): JSX.Element {
    let value = resolvePath(state, propertyKey);
    if (value instanceof EccoTime) {
        value = value.formatHoursMinutes();
    }
    return createTimePickerInput(propertyKey, label, value as string,
        value => {stateSetter(cloneWithValue(state, propertyKey, value))},
        disabled, required);
}

/** newState is a clone of state with state[propertyKey] updated to the new value */
export function textArea<STATE>(
    propertyKey: Extract<keyof STATE, string>,
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    placeholder?: string,
    disabled?: boolean,
    required = false
): JSX.Element {
    return createTextInput(
        propertyKey,
        label,
        resolvePath(state, propertyKey) as string,
        value => {
            stateSetter(cloneWithValue(state, propertyKey, value));
        },
        "textarea",
        {placeholder, disabled, rows: 8, required}
    );
}

/** If rows is specified then this becomes a multi-line text input, even if rows=1 */
export function createTextInput(
    propertyKey: string,
    label: ReactNode,
    value: number | string | null,
    onChange: (val: string | null) => void,
    type: FieldGroupTypes,
    options: {
        placeholder?: string;
        disabled?: boolean;
        rows?: number;
        maxLength?: number;
        required?: boolean;
        helperText?: string;
        size?: "small" | "medium";
        autoSaveKey?: string;
        autocompleteOff?: boolean;
    } = {},
    validator: (
        required: boolean | undefined,
        value: string | null
    ) => "error" | "success" | undefined | null = calcValidationState
): JSX.Element {
    const valid = validator(options.required, value == null ? null : String(value));
    return (
        <TextField
            autoComplete={options.autocompleteOff ? "new-password" : undefined} // see commit msg
            name={propertyKey}
            label={label}
            type={type}
            fullWidth={true}
            size={options.size}
            placeholder={options.placeholder}
            disabled={options.disabled}
            rowsMax={options.disabled ? undefined : options.rows}
            required={options.required}
            error={valid == "error"}
            helperText={valid == "error" && options.helperText}
            multiline={!!options.rows}
            rows={options.disabled ? undefined : options.rows}
            onChange={event => onChange(stringFromHtmlInput(event.target))}
            value={value}
        />
    );
}

export interface TextInputProps {
    propertyKey: string;
    label: ReactNode;
    value: number | string | null;
    onChange: (val: string | null) => void;
    type: FieldGroupTypes;

    validator?: (
        required: boolean | undefined,
        value: EccoDate | string | number | boolean | null
    ) => "error" | "success" | undefined | null;
    placeholder?: string;
    disabled?: boolean;
    rows?: number;
    maxLength?: number;
    required?: boolean;
    size?: "small" | "medium";
    autoSaveKey?: string;
    InputProps?: Partial<StandardInputProps>;
}

export const EccoTextInput: FC<TextInputProps> = props => {
    const value = props.value == null ? "" : String(props.value)
    const validator = props.validator || calcValidationState
    const valid = validator(props.required, value)
    const autoSaveStatus = useAutoSave(props.value ? String(props.value) : null, props.onChange, props.autoSaveKey)

    return (
        <TextField
            name={props.propertyKey}
            label={props.label}
            type={props.type}
            fullWidth={true}
            size={props.size}
            placeholder={props.placeholder}
            disabled={props.disabled}
            rowsMax={props.rows}
            required={props.required}
            error={valid == "error"}
            helperText={autoSaveStatus}
            multiline={!!props.rows}
            rows={props.rows}
            onChange={event => props.onChange(stringFromHtmlInput(event.target))}
            value={value}
            InputProps={props.InputProps}
        />
    );

}

/** Expects value at propertyKey to be an EccoDate */
export function datePickerInput<STATE>(propertyKey: Extract<keyof STATE, string>,
                                       label: ReactNode,
                                       stateSetter: (newState: STATE) => void,
                                       state: STATE,
                                       disabled = false,
                                       required = false): JSX.Element {
    return createDatePickerInput(propertyKey, label,
        state[propertyKey] as unknown as EccoDate, // TODO: Hacked because we cannot make propertyKey just those that map to EccoDate (AFAIK)
        value => {stateSetter(cloneWithValue(state, propertyKey, value))}, disabled, undefined, required);
}

/** As datePickerInput, but accepts and writes state as an ISO8601 string
 * This copes with text input, as does DatePicker in datePickers.tsx.
 */
export function datePickerIso8601Input<STATE>(
    propertyKey: Extract<keyof STATE, string>,
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    disabled = false,
    required = false,
    minDate?: EccoDate,
    maxDate?: EccoDate
): JSX.Element {
    return createDatePickerInput(
        propertyKey,
        label,
        // undefined value gets converted to null - though createDatePickerInput expects an EccoDate
        EccoDate.parseIso8601(resolvePath<STATE, string>(state, propertyKey)),
        value => {
            stateSetter(
                cloneWithValue(
                    state,
                    propertyKey,
                    value && value.isValid() && value.formatIso8601()
                )
            );
        },
        disabled,
        undefined,
        required,
        minDate,
        maxDate
    );
}

function createDatePickerInput(
    name: string,
    label: ReactNode,
    value: EccoDate,
    onChange: (date: EccoDate | null) => void,
    disabled: boolean,
    placeholder?: string,
    required = false,
    minDate?: EccoDate,
    maxDate?: EccoDate
): JSX.Element {
    const error = calcValidationState(required, value);
    return (
        <DatePickerEccoDate
            name={name}
            label={label} // gets super-imposed so show as helper text
            value={value}
            disabled={disabled}
            required={required}
            onChange={onChange}
            minDate={minDate}
            maxDate={maxDate}
            error={error == "error"}
        />
    );
}

/** Accepts and writes state as an ISO8601 string */
export function dateTimeIso8601Input<STATE>(propertyKey: Extract<keyof STATE, string>,
                                            label: ReactNode,
                                            stateSetter: (newState: STATE) => void,
                                            state: STATE,
                                            disabled = false,
                                            required = false): JSX.Element {
    const currValue = state[propertyKey] as unknown as string;

        return createDateTimePickerInput(
            propertyKey, label, currValue,
            value => {stateSetter({...state, [propertyKey]:value})},
            disabled,
            required);
}

/** newState is a clone of state with state[propertyKey] updated to the new value */
export function numberInput<STATE>(
    propertyKey: Extract<keyof STATE, string>,
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    disabled?: boolean,
    decimalPlaces = 0,
    minValue = 0,
    maxValue = 1e9,
    required = false,
    validator: (
        required: boolean | undefined,
        value: string | null
    ) => "error" | "success" | undefined | null = calcValidationState
): JSX.Element {
    return createNumberInput(
        propertyKey,
        label,
        resolvePath(state, propertyKey),
        value => {
            stateSetter(cloneWithValue(state, propertyKey, value));
        },
        disabled,
        undefined,
        decimalPlaces,
        minValue,
        maxValue,
        required
    );
}

export function createNumberInput(
    name: string,
    label: ReactNode,
    value: number | null,
    onChange: (id: number | null) => void,
    disabled?: boolean,
    placeholder?: string,
    decimalPlaces = 0,
    minValue = 0,
    maxValue = 1e9,
    required = false
): JSX.Element {
    // NB should probably call createTextInput for helperText etc
    const valid = calcValidationState(required, value);
    return (
        <TextField
            name={name}
            label={label}
            fullWidth={true}
            type="number"
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            error={valid == "error"}
            onChange={event => {
                let number = numberFromHtmlInput(event.target, decimalPlaces);
                if (number !== null) {
                    number = number > maxValue ? maxValue : number < minValue ? minValue : number;
                }
                onChange(number);
            }}
            value={value == null ? "" : String(value)}
        />
    );
}


export function createButtonGroup<K extends string = string>(name: string,
                                  label: ReactNode,
                                  valueMap: {[key in K]: boolean},
                                  onChange: (key: K, state: boolean) => void,
                                  disabled = false): JSX.Element {
    return (
        <div className="MuiFormControl-root">
            <label className="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-filled MuiInputLabel-shrink MuiFormLabel-filled">{label}</label>
            <div className="MuiInputBase-root MuiFilledInput-root MuiInputBase-formControl MuiInput-formControl"
                style={{padding:"20px 12px 4px"}}>
                {Object.entries(valueMap).map(([key, value]) =>
                    <Button key={key} onClick={() => onChange(key as K, !value)} disabled={disabled}>
                        {key}&nbsp;<i className={"fa fa-check-circle" + (value ? ' selected' : '')}/>
                    </Button>
                )}
            </div>
        </div>
    );
}

export function checkBox<STATE>(propertyKey: Extract<keyof STATE, string>,
                                label: ReactNode,
                                stateSetter: (newState: STATE) => void,
                                state: STATE,
                                disabled?: boolean): JSX.Element {
    return createCheckBox(propertyKey, label, resolvePath(state, propertyKey),
        () => {
            const value = !resolvePath(state, propertyKey);
            stateSetter(cloneWithValue(state, propertyKey, value))
        }, disabled);
}


export function createCheckBox(name: string,
                        label: ReactNode,
                        value: boolean,
                        onChange: (checked: boolean) => void,
                        disabled?: boolean): JSX.Element {
    return (
        <FormControlLabel
            key={name}
            control={
                <Checkbox
                    name={name}
                    onChange={(_, checked) => onChange(checked)}
                    checked={value}
                    disabled={disabled}
                />
            }
            label={label}
        />
    );
}

/** newState is a clone of state with state[propertyKey] updated to the new value */
export function dropdownList<STATE>(
    label: ReactNode,
    stateSetter: (newState: STATE) => void,
    state: STATE,
    propertyKey: Extract<keyof STATE, string>,
    valueArray: SelectListOption[] | null,
    inputProps: any = {}, // FIXME: InputProps
    disabled?: boolean,
    required?: boolean
): JSX.Element {
    return createDropdownList(
        propertyKey,
        label,
        resolvePath(state, propertyKey),
        value => {
            stateSetter(cloneWithValue(state, propertyKey, value));
        },
        valueArray,
        inputProps,
        disabled,
        required
    );
}

export function createDropdownList(
    name: string,
    label: ReactNode,
    value: number | string | null,
    onChange: (id: number | string) => void,
    valueArray: SelectListOption[] | null,
    defaultInputProps: object = {}, // TODO: Could narrow to TextFieldProps if we export it from ecco-mui
    disabled?: boolean,
    required?: boolean
): JSX.Element {
    const error = calcValidationState(required, value);
    return (
        // TODO? floatingLabelFixed={true}
        <TextField
            select={true}
            required={required}
            fullWidth={true}
            key={name}
            name={name}
            label={label}
            {...defaultInputProps}
            value={
                value == null
                    ? ""
                    : String(
                          value
                      ) /* FIXME Need to make ths controlled by setting state in event handler */
            }
            onChange={event => {
                const targetAsNum = parseInt(event.target.value);
                onChange(isNaN(targetAsNum) ? event.target.value : targetAsNum);
            }}
            disabled={disabled}
            error={error == "error"}
        >
            <MenuItem key="null" disabled={required} value="">
                {required ? "- required: please select one -" : "- none -"}
            </MenuItem>
            {getOptionsNotDisabled(valueArray, value).map(idNameToMenuItem)}
        </TextField>
    ); /* TODO: debug this to get get value/state working */
}

export type ButtonStyle = "default" | "primary" | "info" | "link" | "danger";

export function button(label: ReactNode,
                       onClick: () => void,
                       style?: ButtonStyle,
                       disabled?: boolean) : JSX.Element {
    return (
        <Button
            variant='contained'
            disabled={disabled}
            color={style == "danger" ? "secondary" : style == "primary" ? style : undefined}
            onClick={() => onClick()}>{label}</Button>
    );
}

/**
 * Provides an element with onClick, but an optional href is actioned if ctrl is also pressed, then a new tab is opened with the href.
 */
export function link(
    label: ReactChild,
    onClick?: (e: React.MouseEvent<HTMLAnchorElement> | React.MouseEvent<HTMLSpanElement>) => void,
    href?: string,
    newTab = true
): JSX.Element {
    const targetStr = `${newTab ? "_blank" : "_self"}`;
    return href ? (
        <Link
            component="button"
            color="primary"
            href={href}
            target={targetStr}
            // don't register onClick if not provided - in which case we'll just be plain href
            onClick={
                !onClick
                    ? undefined
                    : (
                          e: React.MouseEvent<HTMLAnchorElement> | React.MouseEvent<HTMLSpanElement>
                      ) => {
                          if (!e.ctrlKey) {
                              e.preventDefault();
                              onClick(e);
                          }
                      }
            }
        >
            {label}
        </Link>
    ) : (
        <Button id={label.toString()} variant="text" onClick={e => onClick!(e)}>
            {label}
        </Button>
    );
}


export function showNotification(type: "info" | "warning" | "error", message: string) {
    Notifications.add(type, message);
}

/**
 * Good function to use all over the place to standardise and make it easy to allow something to be in place or modal.
 * If showAsModal is true it cannot close itself because it has to assume it can be embeddable in something else.
 * If wanting a modal that handles its own close, use showReactInModal.
 * NB Only modal true shows the help.
 */
export function possiblyModalForm(
    title: string,
    showAsModal: boolean,
    show: boolean,
    onCancel: () => void,
    onSave: MouseEventHandler<any>,
    disableSave: boolean,
    noFooterButtons: boolean,
    children: ReactNode,
    saveAction: DialogAction = "save",
    helpCallback?: () => void,
    helpContent?: ReactNode | Promise<ReactNode>,
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl",
    minHeight?: number,
    fullScreen?: boolean
): ReactElement | null {
    // Wrap children in error boundary so we see any errors in the content
    children = <ErrorBoundary>{children}</ErrorBoundary>;
    if (showAsModal) {
        return (
            <EccoV3Modal
                title={title}
                saveEnabled={!disableSave}
                show={show}
                maxWidth={maxWidth}
                minHeight={minHeight}
                fullScreen={fullScreen}
                children={children}
                /* <EccoModal below uses DialogTitleWithClose which always has a close icon (or back arrow) at the top */
                /* It would be nice to provide a 'close' on read-only, as well as at the top */
                /* BUT our EvidenceDelegatingForm does a fudge to put the 'save' there, and we don't want 'close' next to it */
                action={noFooterButtons ? "none" : saveAction}
                onCancel={onCancel}
                onSave={onSave}
                helpCallback={helpCallback}
                helpContent={helpContent}
            />
        );
    }

    // possibly no action at all if for view/print purposes
    const actionWhenEmbedded =
        !onSave && !onCancel && noFooterButtons ? "none" : noFooterButtons ? "close" : saveAction;

    return show ? (
        <EccoV3EditPane
            title={title}
            saveEnabled={!disableSave}
            show={show}
            children={children}
            action={actionWhenEmbedded}
            onCancel={onCancel}
            onSave={onSave}
        />
    ) : null;
}

/** Put non-React DOM content such as JQuery managed content into a MUI edit control or modal form */
export function possiblyModalFormDom(
    title: string,
    showAsModal: boolean,
    show: boolean,
    onCancel: () => void,
    onSave: MouseEventHandler<any>,
    disableSave: boolean,
    readOnly: boolean,
    content: HTMLElement,
    afterNextPaint?: () => void,
    helpCallback?: () => void,
    helpContent?: ReactNode | Promise<ReactNode>,
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl",
    minHeight?: number,
    fullScreen?: boolean
): ReactElement | null {
    return possiblyModalForm(
        title,
        showAsModal,
        show,
        onCancel,
        onSave,
        disableSave,
        readOnly,
        <DomElementContainer content={content} afterNextPaint={afterNextPaint} />,
        undefined,
        helpCallback,
        helpContent,
        maxWidth,
        minHeight,
        fullScreen
    );
}

/**
 * Put DOM element into non-react page using React modal
 * Compliments showReactInModal
 * See also react-adapters.tsx
 */
export function showInModalDom(
    title: string,
    content: HTMLElement,
    onDismiss = () => {},
    onSave?: () => void,
    mountPoint: HTMLElement = document.createElement("div"),
    unmountCallbackRef?: MutableRefObject<() => void>
) {
    const unmount = () => {
        unmountComponentAtNode(mountPoint);
    };

    if (unmountCallbackRef) {
        unmountCallbackRef.current = unmount;
    }

    const saveClicked: MouseEventHandler<any> = () => {
        onSave && onSave();
        unmount();
    };

    const cancelOrCloseClicked = () => {
        onDismiss();
        unmount();
    };

    render(possiblyModalFormDom(title, true, true, cancelOrCloseClicked, saveClicked,
            false, !onSave, content) as ReactElement,
        mountPoint);
}

interface ModalOptions {
    onAction: () => void
    action: DialogAction
    saveEnabled: boolean
    mountPoint: HTMLElement
    onCancel?: () => void
    unmountCallbackRef?: MutableRefObject<() => void>
    maxWidth?: "sm" | "md" | "lg"
}

/** Put React element into non-react page using React,
 * or into a React page but the callee doesn't want to handle the close.
 * Compliments showInModalDom.
 * See also react-adapters.tsx
 */
export function showReactInModal(
    title: string,
    content: ReactNode,
    options: Partial<ModalOptions>
) {
    const defaults: ModalOptions = {
        onAction: () => {},
        action: "save" as DialogAction,
        saveEnabled: true,
        mountPoint: document.createElement("div")
    }

    const {onAction, action, saveEnabled, mountPoint, onCancel, unmountCallbackRef, maxWidth}
        = {...defaults, ...options}

    const unmount = () => {
        unmountComponentAtNode(mountPoint);
    };

    if (unmountCallbackRef) {
        unmountCallbackRef.current = unmount;
    }

    const doActionAndUnmount = () => {
        onAction();
        unmount();
    };

    const doCancelAndUnmount = () => {
        onCancel && onCancel();
        unmount();
    };

        render(
            <EccoTheme>
                <EccoV3Modal
                    title={title}
                    action={action}
                    saveEnabled={saveEnabled}
                    show={true}
                    children={content}
                    onCancel={doCancelAndUnmount}
                    onSave={doActionAndUnmount}
                    maxWidth={maxWidth}
                />
            </EccoTheme>,
        mountPoint);
}

type FieldGroupTypes = 'text' | 'email' | 'file' | 'textarea' | 'number' | 'time' | 'password';

export interface ContextProps<T> {readonly context: T}

/** Allows export default = withContext(ContextToUse)(MyComponent)
 * so that MyComponent receives an additional prop, context:T.
 * @param ContextToUse - context as created by React.createContext()
 */
export function withContext<T, P>(ContextToUse: React.Context<T>):
    PropInjector<ContextProps<T>> {

    return ((ComponentToWrap: ComponentType<P & ContextProps<T>>) =>
        function WithContext(props: P) {
            return <ContextToUse.Consumer>
                {(context: T) => <ComponentToWrap context={context} {...props}/>}
            </ContextToUse.Consumer>;
        }
    ) as PropInjector<ContextProps<T>>;
}
