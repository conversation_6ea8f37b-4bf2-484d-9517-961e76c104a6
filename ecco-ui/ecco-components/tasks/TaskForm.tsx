import {CommandQueue, CommandSource} from "ecco-commands";
import * as React from "react";
import {FC, Reducer, useReducer, useMemo, ReactElement, useState} from "react";
import {ModalCommandForm, useCommandSourceRegistration} from "../cmd-queue/CommandForm";
import {Grid} from "@eccosolutions/ecco-mui";
import {checkBox, datePickerInput} from "../MUIComponentUtils";
import {TaskDto} from "ecco-dto/workflow-dto";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {useTask, useWorkersWithAccessToFileOrSameAccess} from "../data/entityLoadHooks";
import {EditTaskCommand} from "ecco-commands";
import {useServicesContext} from "../ServicesContext";
import {SelectList} from "../SelectList";

export const EditTaskSubform: FC<{taskHandle: string; serviceAllocationId?: number}> = ({
    taskHandle,
    serviceAllocationId
}) => {
    const {task} = useTask(taskHandle);
    return task ? <TaskSubform task={task} serviceAllocationId={serviceAllocationId} /> : null;
};

interface Props {
    taskHandle: string;
    /** Callback to allow a new user form to let callee know what username was entered */
    show: boolean;
    setShow: (show: boolean) => void;
    serviceAllocationId?: number; // if we can provide a context, we can reduce the staff list to those with access
}

// TaskDto
type State = {
    completed: boolean;
    taskHandle: string;
    dueDate: EccoDate | null;
    assignedTo: string | null;
};
//type Action<K extends keyof State = keyof State> = { propertyKey: K, value: State[K] };
type Action = Partial<State>;
type TaskReducer = Reducer<State, Action>;

// @Exemplar Example of using a FC with form inputs and commands
const TaskSubform: FC<{task: TaskDto; serviceAllocationId?: number}> = ({
    task,
    serviceAllocationId
}) => {
    const {sessionData} = useServicesContext();

    const {workers} = useWorkersWithAccessToFileOrSameAccess(
        serviceAllocationId || null,
        "ROLE_STAFF",
        false
    );
    /*
        const workersList = workers?.map(s => {
        const idNameDisabled: SelectListOption = {
            id: s.username,
            name: s.name,
            disabled: s.disabled
        };
        return idNameDisabled;
    });*/

    // We use this fixed object to hold the state we're mutating
    const stateHolder = useMemo<{state: State}>(
        () => ({
            state: {
                completed: task.isCompleted,
                taskHandle: task.taskHandle,
                dueDate: task.dueDate ? EccoDate.parseIso8601FromDateTime(task.dueDate) : null,
                assignedTo: task.assignedTo
            }
        }),
        []
    );

    const reducer: TaskReducer = (prevState, action) => {
        stateHolder.state = {...stateHolder.state, ...action};
        return stateHolder.state;
    };

    const [state, dispatch] = useReducer<TaskReducer>(reducer, stateHolder.state);

    const commandSource: CommandSource = {
        // Note: The closure will freeze whatever values are seen here,
        emitChangesTo: function (cmdQ: CommandQueue): void {
            const {serviceRecipientId, taskDefId, taskInstanceUuid} =
                EditTaskCommand.fromLinearTaskHandle(task.taskHandle);
            const cmd = new EditTaskCommand(
                "update",
                serviceRecipientId,
                taskInstanceUuid!,
                task.taskHandle!
            )
                .changeAssignee(task.assignedTo, stateHolder.state.assignedTo!)
                .changeDueDate(
                    EccoDateTime.parseIso8601(task.dueDate),
                    stateHolder.state.dueDate
                        ? stateHolder.state.dueDate.toDateTimeMidnight()
                        : null
                );

            // a scenario exists where there is a 2w timer on a task that is showing orange and due in two
            // days but is also completed, where the task history has only 2 entries both years old with
            // both marked complete, and since we can't save as incomplete it means we can't re-save as
            // complete to trigger the timer again.
            // we therefore put this clause in to allow us to change to incomplete BUT keep the completedStatus to identify it
            if (task.isCompleted && !stateHolder.state.completed) {
                // we can't save from the completed date easily, so just use today's date
                // which may be useful in diagnostics
                cmd.changeCompletion(EccoDateTime.nowUtc(), null);
            }

            if (!task.isCompleted && stateHolder.state.completed) {
                cmd.changeCompletion(null, EccoDateTime.nowUtc());
            }

            if (cmd.hasChanges()) {
                cmdQ.addCommand(cmd);
            }
        },
        getErrors(): string[] {
            const errors: string[] = [];
            // if (!stateHolder.state.firstName) {
            //     errors.push("first name is required");
            // }
            // if (!stateHolder.state.lastName) {
            //     errors.push("last name is required");
            // }
            return errors;
        }
    };
    useCommandSourceRegistration(commandSource);

    const disabledIfNotPermission = !sessionData?.hasRoleEditTask();
    const disabledDueIfNotPermission = !sessionData?.hasRoleEditDueTask();

    const workerOptions =
        workers?.map(w => {
            return {
                value: w.username,
                label: w.name
            };
        }) || [];
    const workerSelectedFound = workerOptions.find(w => w.value === state.assignedTo);
    const workerSelectedNotFound = state.assignedTo
        ? {value: state.assignedTo, label: `- id ${state.assignedTo} -`}
        : null;
    const workerSelected = workerSelectedFound || workerSelectedNotFound;
    const workerChange = (o: {value: string; label: string}) => {
        dispatch({...state, assignedTo: o?.value || null});
    };

    return (
        <Grid container direction="row" justify="center" alignItems="center">
            {/*{this.props.task.created && ("task created " + this.props.task.created)}*/}
            {/*{textInput("description", "description", state => this.setState(state), this.state)}*/}
            <Grid item xs={8}>
                {workerOptions && (
                    <SelectList
                        createNew={false}
                        isDisabled={state.completed || disabledIfNotPermission}
                        options={workerOptions}
                        value={workerSelected}
                        placeholder={state.assignedTo ? "reassign to" : "assign to"}
                        onChange={v => workerChange(v as {value: string; label: string})}
                    />
                )}
                {/*{dropdownList(
                        state.assignedTo ? "reassign to" : "assign to",
                        state => dispatch(state),
                        state,
                        "assignedTo",
                        workersList || [],
                        {
                        helperText:
                            state.assignedTo != task.assignedTo
                                ? `was ${task.assignedTo}`
                                : undefined
                    },
                        state.completed || disabledIfNotPermission
                )}*/}
            </Grid>
            <Grid item xs={8}></Grid>
            <Grid item xs={8}>
                {datePickerInput(
                    "dueDate",
                    "due date",
                    state => dispatch(state),
                    state,
                    disabledDueIfNotPermission
                )}
            </Grid>
            <Grid item xs={8}>
                {checkBox("completed", "completed", state => dispatch(state), state)}
            </Grid>
        </Grid>
    );
};

/* @Exemplar */
export const TaskForm: FC<Props> = ({taskHandle, show, setShow, serviceAllocationId}) => {
    return (
        <ModalCommandForm
            title={taskHandle ? `edit task` : "new task"}
            action={taskHandle ? "update" : "save"}
            show={show}
            setShow={setShow}
            helpTitle="managing tasks"
            helpContent={undefined}
            maxWidth="sm"
        >
            <EditTaskSubform taskHandle={taskHandle} serviceAllocationId={serviceAllocationId} />
        </ModalCommandForm>
    );
};

export function showInModalEditTask(taskHandle: string, serviceAllocationId: number): ReactElement {
    return (
        <EditTaskModalWrapper taskHandle={taskHandle} serviceAllocationId={serviceAllocationId} />
    );
}
interface ModalIdProps {
    taskHandle: string;
    serviceAllocationId?: number;
}
export const EditTaskModalWrapper: FC<ModalIdProps> = props => {
    const [show, setShow] = useState(true);
    return (
        <TaskForm
            taskHandle={props.taskHandle}
            show={show}
            setShow={setShow}
            serviceAllocationId={props.serviceAllocationId}
        />
    );
}
