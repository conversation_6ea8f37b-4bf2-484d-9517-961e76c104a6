import {bus, <PERSON>Handler} from "@eccosolutions/ecco-common";
import {CommandDtoSentEvent, CommandQueue, CommandSource} from "ecco-commands";
import {OfflineSyncStatus, OfflineSyncStatusEvent, showErrorAsAlert} from "ecco-offline-data";
import * as React from "react";
import {Component, MouseEventHandler, ReactNode, useContext, useEffect, useState} from "react";
import {DialogAction} from "../forms";
import {showNotification} from "../MUIComponentUtils";
import {EccoV3Modal} from "../EccoV3Modal";
import {useInterval} from "../hooks";
import {hashCode} from "ecco-dto";

const Context = React.createContext<{ commandForm: CommandForm}>({commandForm: null!}); // oh yes, null! what a hack

interface FormProps {
    /** Callback to make when form has saved it's data */
    onFinished: () => void;
    onCancel: () => void;
    commandQueue?: CommandQueue;
    commandQueueDraft?: CommandQueue;
}

interface FormState {
}

/** A component that allows commands to be collected and submitted */
export class CommandForm extends Component<FormProps, FormState> {
    protected subforms: CommandSource[] = [];

    protected commandQueue: CommandQueue;
    protected commandQueueDraft: CommandQueue;

    /** callback should be called when the Form has successfully saved it's content and the dialog can close.
     *  This is usually in response to a "done" button being clicked in the footer, for example */
    private onFinishedBus = bus<void>();


    constructor(props: FormProps) {
        super(props);
        this.commandQueue = props.commandQueue ? props.commandQueue : new CommandQueue();
        this.commandQueueDraft = props.commandQueueDraft
            ? props.commandQueueDraft
            : new CommandQueue();
        this.onFinishedBus.addHandler(props.onFinished);
        this.state = {};
    }

    public getErrors(): string[] {
        return this.subforms.reduce<string[]>(
            (flattenedErrors, subform) =>
                // If getErrors is implemented on the subform, add them
                subform.getErrors ? flattenedErrors.concat(subform.getErrors()) : flattenedErrors,
            []
        );
    }

    /** return some hash of the commands, or null if none **/
    public draftSave(currentHash: string | null): Promise<string | null> {
        // check the existing queue, because we don't want to overlap draft and save
        return this.commandQueue.getCommands().then(cmds => {
            // pretend there is no draft change this cycle if there is something saving
            if (cmds.length > 0) {
                return currentHash;
            }

            // otherwise, put draft commands in queue (a different queue to avoid all the events)
            // TODO flushCommands below still fires 'commandSubmittedEventBus' and commandDtoSentEventBus with wrapper CommandDtoSentEvent
            this.commandQueueDraft.clear();
            this.subforms.forEach(source => source.emitChangesTo(this.commandQueueDraft));
            return this.commandQueueDraft.getCommands().then(cmds => {
                if (cmds.length == 0) {
                    return null;
                }
                const cmdDtosIgnoreProps = cmds.map(c => {
                    return {...c.toCommandDto(), uuid: null, timestamp: null};
                });
                const json = JSON.stringify(cmdDtosIgnoreProps);
                const hash = hashCode(json).toString();

                // do a save if we're different
                if (hash != currentHash) {
                    // loop through setting 'draft' true
                    cmds.forEach(c => (c.toCommandDto().draft = true));
                    return this.commandQueueDraft.flushCommands(false).then(() => hash);
                }
                return hash;
            });
        });
    }

    public submitForm(): Promise<void> {
        // don't save with errors
        const errors = this.getErrors();
        if (errors.length > 0) {
            showNotification("error", errors.join("; "));
            return Promise.reject(errors);
        }

        // wait for any drafts
        this.commandQueue.clear();
        if (this.subforms.length == 0) {
            throw new Error(
                "CommandForm found no registered subforms. Have you extended CommandSubform and called super.componentDidMount() if overridden?"
            );
        }
        this.subforms.forEach(source => source.emitChangesTo(this.commandQueue));

        if (!this.onFinishedBus.hasHandlers()) {
            throw new Error(
                "no onFinished handers have been set - this doesn't make sense - you should notify the user/re-enable etc"
            );
        }
        this.preSubmitCommandsHook();
        const drafts = this.commandQueueDraft.getCommands();
        return drafts.then(() =>
            this.commandQueue
                .flushCommands()
                .then(() => showNotification("info", "change saved"))
                .then(() => {
                    this.onFinishedBus.fire();
                    OfflineSyncStatusEvent.bus.fire(
                        new OfflineSyncStatusEvent(
                            OfflineSyncStatus.COMMANDS_SAVED,
                            0,
                            "Commands saved"
                        )
                    );
                    // this.submitButton.enable();
                })
                .catch(e => showErrorAsAlert(e))
        );
    }

    public cancelForm() {
        this.props.onCancel();
    }

    /** Override this to populate the command queue only on save - prob good idea to empty it first in case of errors */
    protected preSubmitCommandsHook(): void {}

    addPreSubmitHandler(handler: EventHandler<CommandDtoSentEvent>): void {
        this.commandQueue.addCommandDtoSentEventHandler(handler);
    }
    removePreSubmitHandler(handler: () => void): void {
        this.commandQueue.removeCommandDtoSentEventHandler(handler);
    }

    addPostSubmitHandler(handler: () => void): void {
        this.onFinishedBus.addHandler(handler);
    }
    removePostSubmitHandler(handler: () => void): void {
        this.onFinishedBus.removeHandler(handler);
    }

    /** Components register with us to be queried for data in onSubmit */
    registerSubform(commandSource: CommandSource) {
        // WARNING: Don't try doing this with a functional component instead of CommandSubform - classes sometimes are right
        this.subforms.push(commandSource);
    }

    render() {
        return (
            <Context.Provider value={{commandForm: this}}>{this.props.children}</Context.Provider>
        );
    }
}

export interface CommandSubformProps {
    commandForm: CommandForm,
    readOnly?: boolean
}

/**
 * CommandQueue can return results (eg id from a create command)
 * CommandForm doesn't since there could be mane commands in a form
 * So, we use this hook to pass-through to the commandQueue
*/
export function useCommandFormPreSubmitHandler(preSubmitHandler: EventHandler<CommandDtoSentEvent> | undefined) {
    const form = useCommandForm();
    useEffect(() => {
        if (preSubmitHandler) {
            form.addPreSubmitHandler(preSubmitHandler);
            return () => {
                form.removePreSubmitHandler(preSubmitHandler);
            };
        }
        return () => {};
    }, []);
}

export function useCommandFormPostSubmitHandler(postSubmitHandler: (() => void) | undefined) {
    const form = useCommandForm();
    useEffect(() => {
        if (postSubmitHandler) {
            form.addPostSubmitHandler(postSubmitHandler);
            return () => {
                form.removePostSubmitHandler(postSubmitHandler);
            };
        }
        return () => {};
    }, []);
}

export const useCommandSourceRegistration = (
    commandSource: CommandSource,
    postSubmitHandler?: () => void
) => {
    const form = useCommandForm();
    useEffect(() => {
        if (!form) {
            throw new Error("DEV error - CommandForm not provided");
        }
        form.registerSubform(commandSource);
    }, []);
    useCommandFormPostSubmitHandler(postSubmitHandler);
};
/*
export const useFormDraftSave = () => {
    const [draftHash, setDraftHash] = useState<string | null>(null);
    const form = useCommandForm();

    const checkDraft = () => {
        // check if there is something to save
        // perhaps we could listen to events, to save the processing time
        // ** ?? check our form (per workUuid) still exists - we could have saved already and have no reference (but we are in the form, so may clear together)
        // ** ?? check our commands haven't already saved?
        form.draftSave(draftHash).then(hash => {
            setDraftHash(hash);
        });
    };
    useInterval(checkDraft, 120);
};*/

/**
 * It's idea to have any CommandSource implement this as it's then ready to work with larger command-based forms
 */
export abstract class CommandSubform<P, S> extends Component<P & CommandSubformProps, S> implements CommandSource {

    public componentDidMount() {
        this.commandForm = this.props.commandForm;
        if (this.commandForm) {
            this.commandForm.registerSubform(this);
        }
    }

    private commandForm: CommandForm = null!;

    abstract emitChangesTo(commandQueue: CommandQueue): void;

    abstract render(): ReactNode;
}

/** Allows components below to access the CommandForm (non-UI) component which allows form to be passed to
 * subforms so that they can register to be queried to emitChangesTo(cmdQ) when form.submitForm() is triggered.
 *
 * NOTE: Consider using showInCommandForm() to nicely manage providing a containing CommandForm component and nicely
 * mounting and unmounting the whole lot (usually a modal).
 */
export function withCommandForm(callback: (form: CommandForm) => ReactNode) {
    return <Context.Consumer>
        {context => callback(context.commandForm)}
    </Context.Consumer>;
}

export const useCommandForm = () => useContext(Context).commandForm;

interface ModalCommandFormProps extends ModalForCommandProps {
    setShow: (show: boolean) => void;
    // submitHandler
}

export const ModalCommandForm = (props: ModalCommandFormProps) => {
    const hide = () => props.setShow(false);

    const {setShow, ...otherProps} = props;
    return props.show ? (
        // saving ? <LoadingSpinner/> :
        <CommandForm onCancel={hide} onFinished={hide}>
            <ModalForCommandForm {...otherProps}>{props.children}</ModalForCommandForm>
        </CommandForm>
    ) : (
        <></>
    );
};

interface ModalForCommandProps {
    children: ((form: CommandForm) => ReactNode) | ReactNode;
    /** If omitted, modal is always shown */
    show?: boolean;
    title: string;
    action: DialogAction;
    maxWidth?: "xs" | "sm" | "md" | "lg" | "xl";
    saveEnabled?: boolean;
    /*onCancel: () => void;*/
    onSave?: MouseEventHandler<any>;
    isLegacy?: true;
    helpTitle?: ReactNode;
    helpContent?: ReactNode;
    showPrint?: true;
    afterSave?: () => void;
}

/**
 * Use the same Modal ideas of ModalCommandForm but assume a CommandForm is already in plance
 */
export const ModalForCommandForm = (props: ModalForCommandProps) => {
    const [saving, setSaving] = useState(false);
    const form = useCommandForm();

    return (
        <EccoV3Modal
            {...props}
            saveEnabled={!saving}
            onCancel={() => form.cancelForm()}
            onSave={() => {
                setSaving(true);
                const onFinished = () => setSaving(false);
                return form.submitForm().then(onFinished, onFinished).then(props.afterSave);
            }}
        >
            {typeof props.children === "function" ? props.children(form) : props.children}
        </EccoV3Modal>
    );
};
