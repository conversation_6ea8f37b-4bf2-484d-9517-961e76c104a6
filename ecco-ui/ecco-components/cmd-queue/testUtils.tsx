import {FC, ReactNode, useMemo, useState} from "react";
import {Command, CommandAjaxRepository, CommandQueue, MergeableCommand} from "ecco-commands";
import {Button, Paper, Typography} from "@eccosolutions/ecco-mui";
import {ApiClient, BaseServiceRecipientCommandDto, EvidenceGroup} from "ecco-dto";
import {HateoasResource, Result} from "@eccosolutions/ecco-common";
import * as React from "react";
import {CommandForm, useCommandForm, withCommandForm} from "./CommandForm";

interface Props {
    delayClearOnFinishFlush?: number;
}
/**
 * Wraps a command form so we can see commands output.
 * Also see CommandFormTestOutput.
 */
export const CommandFormTest: FC<Props> = (props: {
    delayClearOnFinishFlush?: number;
    children?:
        | ((form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => ReactNode)
        | ReactNode;
}) => {
    // Mainly useful when not using a context.
    const [cmd, setCmd] = useState<Command[]>([]);
    const [cmdDraft, setCmdDraft] = useState<Command[]>([]);

    const testCmdQueue = useMemo(() => new CommandQueue(new DummyCommandRepo()), []);
    testCmdQueue.addCommandSubmittedEventHandler(cmdIn => {
        if (cmdIn) {
            setCmd(cmd.concat(cmdIn));
        } else {
            console.error("cmdIn undefined");
        }
    });
    const testCmdQueueDraft = useMemo(() => new CommandQueue(new DummyCommandRepo()), []);
    testCmdQueueDraft.addCommandSubmittedEventHandler(cmdIn => {
        if (cmdIn) {
            setCmdDraft(cmdDraft.concat(cmdIn));
        } else {
            console.error("cmdIn undefined");
        }
    });

    const onSubmitFinishFlush = () => {
        if (props.delayClearOnFinishFlush) {
            setTimeout(() => setCmd([]), props.delayClearOnFinishFlush);
        }
    };

    return (
        <>
            {/*not using EmbeddedCommandForm as we don't want the fom around it*/}
            {/*TODO we will still need save/cancel*/}
            <CommandForm
                onCancel={() => {}}
                onFinished={onSubmitFinishFlush}
                commandQueue={testCmdQueue}
                commandQueueDraft={testCmdQueueDraft}
            >
                {withCommandForm(form => (
                    <>
                        {typeof props.children === "function"
                            ? props.children(form, cmd, cmdDraft)
                            : props.children}
                    </>
                ))}
            </CommandForm>
        </>
    );
};

/*
export const EmbeddedCommandForm: FC<{testCmdQueue: CommandQueue}> = props => {
    return (
            <CommandForm onCancel={() => {}} onFinished={() => {}} commandQueue={props.testCmdQueue}>
                {withCommandForm(form => (
                        <Paper elevation={4}>
                            <Box m={2} p={2}>
                                <EccoV3EditPane
                                        title="EccoV3EditPane"
                                        saveEnabled={true}
                                        show={true}
                                        action="update"
                                        onCancel={() => alert("cancelled")}
                                        onSave={() => form.submitForm()}
                                >
                                    {props.children}
                                </EccoV3EditPane>
                            </Box>
                        </Paper>
                ))}
            </CommandForm>
    );
};*/

/**
 * Clone of EvidencePageTestLayout in EvidencePageTest
 */
export const CommandFormTestOutput: FC<{
    cmdEmitted: Command[];
    cmdEmittedDraft: Command[];
}> = props => {
    const form = useCommandForm();
    const [errorText, setErrorText] = useState<string>("");

    const errorsOrSubmit = () => {
        setErrorText(form.getErrors().join(","));
        // noinspection JSIgnoredPromiseFromCall
        form.submitForm();
    };
    return (
        <>
            <Paper id={"eccotest-cmdQueue"} elevation={2} style={{marginTop: "25px"}}>
                <Typography>CommandFormTestOutput:</Typography>
                <Button color="primary" onClick={errorsOrSubmit}>
                    submit
                </Button>
                <br />
                emitted cmd errors (if using CommandForm)
                <br />
                <br />
                {errorText}
                <br />
                <br />
                <br />
                <br />
                emitted {props.cmdEmitted.length} cmd (if using CommandForm)
                <br />
                <br />
                {props.cmdEmitted.map(c => (
                    <Typography key={c.toCommandDto().uuid}>
                        {JSON.stringify(c.toCommandDto())}
                    </Typography>
                ))}
            </Paper>
            <Paper id={"eccotest-cmdQueueDraft"} elevation={2} style={{marginTop: "25px"}}>
                <Typography>CommandFormDraftTestOutput:</Typography>
                {/*TODO submit draft*/}
                <br />
                emitted draft {props.cmdEmittedDraft?.length} cmd (if using CommandForm)
                <br />
                <br />
                {props.cmdEmittedDraft?.map(c => (
                    <Typography key={c.toCommandDto().uuid}>
                        {JSON.stringify(c.toCommandDto())}
                    </Typography>
                ))}
            </Paper>
            {props.children}
        </>
    );
};

/**
 * Stubbed interface to avoid sendCommand.
 * Mainly useful when not using a context, but still allows us to see the emitted command.
 */
export class DummyCommandRepo extends CommandAjaxRepository {
    public constructor() {
        super({} as ApiClient);
    }
    findAll(serviceRecipientId: number): Promise<BaseServiceRecipientCommandDto[]> {
        return Promise.resolve([]);
    }
    findOne(uuid: string, optional?: boolean): Promise<BaseServiceRecipientCommandDto> {
        return Promise.resolve({} as BaseServiceRecipientCommandDto);
    }
    findCommandsByServiceRecipientIdAndEvidenceGroup(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return Promise.resolve([]);
    }
    findCommandsByServiceRecipientIdAndEvidenceGroupAndTaskName(
        serviceRecipientId: number,
        evidenceGroup: EvidenceGroup,
        taskName: string
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return Promise.resolve([]);
    }
    exchangeCommand<T extends HateoasResource>(command: Command | MergeableCommand): Promise<T> {
        return Promise.resolve({} as T);
    }
    sendCommand<T extends HateoasResource | undefined = Result>(
        command: Command | MergeableCommand
    ): Promise<T> {
        return Promise.resolve({} as T);
    }
}
