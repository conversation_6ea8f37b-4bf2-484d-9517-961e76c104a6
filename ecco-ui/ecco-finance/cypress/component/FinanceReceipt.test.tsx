import * as React from "react";
import {mount} from "@cypress/react";
import {
    FinanceReceipt,
    FinanceReceiptCommandForm,
    FinanceReceiptState
} from "../../receipt/FinanceReceipt";
import {FC, useState} from "react";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {CommandForm} from "ecco-components";
import {Command} from "ecco-commands";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";

const FinanceReceiptLayout: FC = () => {
    const [state, setState] = useState<FinanceReceiptState>({
        amount: undefined,
        received: undefined,
        description: undefined
    });
    return <FinanceReceipt data={state} setData={setState} />;
};

describe("FinanceReceipt layout", () => {
    it("layout", () => {
        mount(<FinanceReceiptLayout />);

        //cy.get('[name*="taskName"]').type("my new task...");
    });
});

describe("FinanceReceipt tests", () => {
    it("it mounts", () => {
        mount(
            <TestServicesContextProvider overrides={{}}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[], cmdEmittedDraft: Command[]) => (
                        <>
                            <FinanceReceiptCommandForm
                                init={{
                                    serviceRecipientId: 99,
                                    receiptId: null,
                                    amount: undefined,
                                    received: undefined,
                                    description: undefined
                                }}
                            />
                            <CommandFormTestOutput
                                cmdEmitted={cmdEmitted}
                                cmdEmittedDraft={cmdEmittedDraft}
                            />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        //cy.findByLabelText("due date").should("be.disabled");
    });
});
