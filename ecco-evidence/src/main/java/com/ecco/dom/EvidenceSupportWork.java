package com.ecco.dom;

import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.dom.Action;
import com.ecco.serviceConfig.dom.Outcome;
import com.querydsl.core.annotations.QueryInit;
import org.hibernate.annotations.BatchSize;
import org.joda.time.DateTime;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static javax.persistence.CascadeType.PERSIST;
import static javax.persistence.CascadeType.REMOVE;

@Entity
@Table(name="evdnc_supportwork")
@NamedQueries(@NamedQuery(name = EvidenceSupportWork.BULK_UPDATE_AUTHOR_QUERY,
        query = "update EvidenceSupportWork set author = :newContact where author = :oldContact"))
public class EvidenceSupportWork extends BaseWorkWithChild {

    private static final long serialVersionUID = 1L;

    public static final String BULK_UPDATE_AUTHOR_QUERY = "evidenceSupportWork.bulkUpdateAuthor";

    public static class Builder {

        /** The new work being built */
        private final EvidenceSupportWork work;

        /** Latest actions from previous work - needed where we carry forward targetDate, comment etc */
        private List<EvidenceSupportAction> previousActions;

        public Builder(EvidenceSupportWork work) {
            this.work = work;
        }

        public Builder usingLatestActions(List<EvidenceSupportAction> actions) {
            previousActions = actions;
            return this;
        }

        public Builder withComment(String comment, int minutesSpent, Integer typeId, boolean requiresThreatManagement) {
            return withComment(comment)
                    .withMinutesSpent(minutesSpent)
                    .withType(typeId)
                    .withRequiresThreatManagement(requiresThreatManagement);
        }

        public Builder withComment(String comment) {
            getWorkComment().setComment(comment);
            return this;
        }

        /** mins spent - zero for not specified */
        public Builder withMinutesSpent(int minutesSpent) {
            getWorkComment().setMinutesSpent(minutesSpent);
            return this;
        }

        public Builder withType(Integer typeId) {
            getWorkComment().setTypeDefId(typeId);
            return this;
        }

        public Builder withRequiresThreatManagement(boolean requiresThreatManagement) {
            getWorkComment().setRequiresThreatManagement(requiresThreatManagement);
            return this;
        }

        public Builder mileageTo(BigDecimal mileageTo) {
            getWorkComment().setMileageTo(mileageTo);
            return this;
        }

        public Builder mileageDuring(BigDecimal mileageDuring) {
            getWorkComment().setMileageDuring(mileageDuring);
            return this;
        }

        protected EvidenceSupportComment getWorkComment() {
            if (work.getComment() != null) {
                return work.getComment();
            }
            EvidenceSupportComment c = new EvidenceSupportComment(work.getServiceRecipientId());
            c.setAuthor((Individual) work.getAuthor());
            c.setCreated(work.getCreated());
            c.setWork(work);
            work.setComment(c);
            return c;
        }

        public Builder withOutcome(Outcome o, int status) {
            EvidenceSupportOutcome go = new EvidenceSupportOutcome(work.getServiceRecipientId());
            go.setOutcome(o);
            go.setAuthor(work.getAuthor());
            go.setCreated(work.getCreated());
            go.setStatus(status);
            // we mimic existing fn, but don't support statusChangeDate anymore
            //if (displayOutcome.getStatusChangeDate() == null)
                go.setStatusChangeDate(work.getWorkDate());
            // nothing extra required on support
            //toWorkLogicExtra(go, displayOutcome);
            // needs an identifier since in the set, the equals method compares the id, or collectionId
            go.setCollectionId(work.getOutcomes().size());
            work.addOutcome(go);

            return this;
        }

        public Builder withNewActionInstance(Action a, Integer actionStatus, Boolean actionStatusChange) {
            UUID instanceUuid = UUID.randomUUID();
            return withAction(a, instanceUuid, actionStatus, actionStatusChange, null);
        }

        // assumes no target date
        public Builder withAction(Action a, UUID instanceUuid, Integer actionStatus, Boolean actionStatusChange) {
            return withAction(a, instanceUuid, actionStatus, actionStatusChange, null);
        }

        public Builder withAction(Action a, UUID instanceUuid, Integer actionStatus, Boolean actionStatusChange,
                                  DateTime targetDate) {
            return withAction(a, instanceUuid, actionStatus, actionStatusChange, targetDate, null, null, 0, null);
        }

//        public Builder withActionCommentChange(Action actionDef, String comment) {
//            GenericTypeSupportAction action = getWorkActionOrPrevWithSmartStepId(actionDef.getId());
//        UNUSED and clearly didn't use comment so broken too...
//            return this;
//        }

        // all properties
        public Builder withAction(Action a, UUID instanceUuid, Integer actionStatus, Boolean actionStatusChange,
                DateTime targetDate, DateTime expiryDate, String commentUnderSmartStep, int activity, Integer score) {
            EvidenceSupportAction sa = createNewAction(a, instanceUuid, actionStatus, actionStatusChange);

            sa.setTarget(targetDate);
            sa.setExpiryDate(expiryDate);
            sa.setActivity(activity);
            sa.setGoalName(commentUnderSmartStep);
            sa.setScore(score);

            return this;
        }

        /** Creates a new action with relevant info inherited from parent work item */
        protected EvidenceSupportAction createNewAction(Action a, UUID instanceUuid, Integer actionStatus,
                                                        Boolean actionStatusChange) {
            EvidenceSupportComment c = work.getComment();
            EvidenceSupportAction sa = new EvidenceSupportAction(c.getServiceRecipientId());

            sa.setCollectionId(work.getActions().size()); // Use size() to create 'unique' IDs until saved
            work.addAction(sa);
            sa.setAction(a);
            sa.setActionInstanceUuid(instanceUuid);
            sa.setStatus(actionStatus);
            sa.setStatusChange(actionStatusChange);

            sa.setWorkDate(c.getWork().getWorkDate());
            sa.setAuthor(c.getAuthor());
            sa.setCreated(c.getCreated());
            return sa;
        }

        public Builder withAssociatedAction(Action a) {
            work.addAssociatedAction(a);
            return this;
        }

        public Builder withAuthor(Individual author) {
            work.setAuthor(author);
            return this;
        }

        public Builder withCreatedDate(DateTime createDate) {
            work.setCreated(createDate);
            return this;
        }

        public Builder withAuthor(ContactImpl author) {
            work.setAuthor(author);
            return this;
        }

        public Builder withClient(Long clientContactId) {
            if (clientContactId != null) {
                ContactImpl individual = new Individual();
                individual.setId(clientContactId);
                work.setIndividual(individual);
            }
            return this;
        }

        public Builder withReview(Review review) {
            if (review != null) {
                work.setReview(review);
                work.setOutcomePage(review.getReviewPage());
            }
            return this;
        }


        public Builder withUuid(UUID randomUUID) {
            work.setId(randomUUID);
            return this;
        }

        public Builder withWorkDate(DateTime workDate) {
            work.setWorkDate(workDate);
            return this;
        }

        public EvidenceSupportWork build() {
            // FIXME: This would be correct but breaks tests -> ensureActionsAreMarkedAssociated();
            return work;
        }

        /**
         * Must ensure that all actions that are modified are marked as associated.
         * This should be part of build() but current breaks some tests
         */
        public void ensureActionsAreMarkedAssociated() {
            Set<EvidenceSupportAction> swaList = work.getActions();
            for (EvidenceSupportAction swa : swaList) {
                withAssociatedAction(swa.getAction());
            }
        }

        /** Get work action matching the actionDefId */
        public EvidenceSupportAction getExistingWorkActionWithSmartStepId(long actionDefId, UUID instance) {
            for (EvidenceSupportAction action : work.actions) {
                if (action.getActionId() == actionDefId && action.getActionInstanceUuid().equals(instance)) {
                    return action;
                }
            }
            return null;
        }

        public EvidenceSupportAction getPreviousActionWithSmartStepId(long actionDefId, UUID instance) {
            Assert.notNull(previousActions, "Cannot use this unless you've called usingLatestActions() first");
            for (EvidenceSupportAction action : previousActions) {
                if (action.getActionId() == actionDefId && action.getActionInstanceUuid().equals(instance)) {
                    return action;
                }
            }
            return null;
        }

        /**
         * Return a {@link EvidenceSupportAction} which is either the existing match that is part of this
         * support work, a clone of match from {@link #previousActions}, or a completely new one.
         * If new, the action will have been added via work.addAction(action).
         */
        public EvidenceSupportAction getWorkActionOrPrevWithSmartStepId(long actionDefId, UUID instance) {
            EvidenceSupportAction action = getExistingWorkActionWithSmartStepId(actionDefId, instance);
            if (action != null) {
                return action;
            }

            EvidenceSupportAction prev = getPreviousActionWithSmartStepId(actionDefId, instance);
            if (prev != null) {
                action = createNewAction( prev.action, prev.actionInstanceUuid, prev.getStatus(), false); // NOTE: not statusChanged - that should be set if we do change it
                action.setGoalName(prev.goalName);
                action.setActivity(prev.activity);
                action.setExpiryDate(prev.expiryDate);
                action.setTarget(prev.target);
                action.setScore(prev.score);
            }
            else {
                action = createNewAction(new Action(actionDefId), instance, EvidenceAction.isRelevant, true);
            }
            work.addAction(action);
            return action;
        }
    }

    public Builder build() {
        return new Builder(this);
    }

    public static Builder builder(Boolean asRisk, BaseServiceRecipient sr, EvidenceTask taskDefId, EvidenceGroup evidenceGroupId) {

        EvidenceSupportWork w = new EvidenceSupportWork(sr.getId());
        w.setAsRisk(asRisk);
        w.setTaskDefId(TaskDefinitionNameIdMappings.fromTaskNameToTaskDefId(taskDefId.getTaskName()));
        w.setEvidenceGroupId(evidenceGroupId.getId());
        return new Builder(w);
    }

    // NOTE: DON'T PULL UP TO BASE WORK.  QueryDSL doesn't like that due to comment.threatWork giving circ ref
    @QueryInit("*")
    @OneToOne(mappedBy="work", cascade={PERSIST, REMOVE})
    EvidenceSupportComment comment;

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceSupportOutcome> outcomes = new HashSet<>();

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceSupportAction> actions = new HashSet<>();

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<ServiceRecipientActivity> activities = new HashSet<>();

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    @OrderBy("question")
    Set<EvidenceSupportAnswer> answers = new HashSet<>();

    @OneToMany(mappedBy="work", cascade={PERSIST, REMOVE}, fetch=FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceSupportFlag> flags = new HashSet<>();

    // we use a m2m because we want a set of Action - which already belongs elsewhere
    // don't have any cascade, since we don't want to save the other side (the action) - simply the join table
    // 'persist' is reflected in the jointable syntax, such as insertable and updatable...
    @ManyToMany(fetch=FetchType.LAZY)
    @JoinTable(name = "evdnc_supportwork_actions",
        joinColumns = @JoinColumn(name = "workUuid", columnDefinition="CHAR(36)"),
        inverseJoinColumns = @JoinColumn(name = "actionId"))
    @BatchSize(size = 20)
    Set<Action> associatedActions = new HashSet<>();

    @OneToMany(mappedBy = "work", cascade = {PERSIST, REMOVE}, fetch = FetchType.LAZY)
    @BatchSize(size = 20)
    Set<EvidenceSupportAttachment> attachments = new HashSet<>();

    // one review has many support items, each of which indicates which outcome was saved against
    @ManyToOne(fetch=FetchType.LAZY)
    @JoinColumn(name="reviewId")
    Review review;

    /** Relates to calendar cosmo_item.event_uid, but could be some other calendar system UID in future */
    @Column
    String eventId;

    // in review mode, we save a piece of work per outcome (the reviewPage)
    int outcomePage;

    boolean asRisk = false;
    //Outcome reviewOutcome;

    Integer invoiceId;

    public EvidenceSupportWork() {
        // for Hibernate etc
        super();
    }

    public EvidenceSupportWork(int serviceRecipientId) {
        super(serviceRecipientId);
    }

    public String getEventId() {
        return eventId;
    }
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }


    public int getOutcomePage() {
        return outcomePage;
    }
    public void setOutcomePage(int outcomePage) {
        this.outcomePage = outcomePage;
    }

    public Review getReview() {
        return review;
    }
    public void setReview(Review review) {
        this.review = review;
    }

    // whilst we save different 'types' of support (eg evidenceThreatWork) in their own tables
    // we are unsure how the 'social impact' screen will work out, and if any support wants to show
    // so we simply have a setting which we can 'filter' on for now in the overview (to avoid cross-polluting the comments)
    public void setAsRisk(boolean asRisk) {
        this.asRisk = asRisk;
    }
    public boolean isAsRisk() {
        return asRisk;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }
    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public DateTime getDate() {
        return getWorkDate();
    }

    // get the set of actions which haven't been acted upon
    public Set<Action> associatedActionsRemaining() {
        Set<Action> remainingActions = new HashSet<>(associatedActions);
        for (EvidenceSupportAction da : actions) {
            remainingActions.remove(da.getAction());
        }
        return remainingActions;
    }

    @Override
    public Set<EvidenceSupportOutcome> getOutcomes() {
        return outcomes;
    }
    @Override
    public Set<EvidenceSupportAction> getActions() {
        return actions;
    }
    @Override
    public EvidenceSupportComment getComment() {
        return comment;
    }
    @Override
    public Set<ServiceRecipientActivity> getActivities() {
        return activities;
    }
    public Set<EvidenceSupportAnswer> getAnswers() {
        return answers;
    }
    public Set<EvidenceSupportAttachment> getAttachments() {
        return attachments;
    }
    public Set<EvidenceSupportFlag> getFlags() { return flags; }

    @Override
    public Set<Action> getAssociatedActions() {
        return associatedActions;
    }

    @Override
    public boolean addAssociatedAction(Action action) {
        // FIXME: This is a workaround to avoid unknown side-effects of Actions.hachCode/equals ignoring id
        for (Action existing : associatedActions) {
            if (existing.getId().equals(action.getId()))
             {
                return false; // i.e. do what would work
            }
        }
        return associatedActions.add(action);
    }

    public boolean removeAssociatedAction(Action action) {
        return associatedActions.remove(action);
    }

    @Override
    public void addOutcome(EvidenceOutcome outcome) {
        EvidenceSupportOutcome spo = (EvidenceSupportOutcome) outcome;
        spo.setWork(this);
        outcomes.add(spo);
    }
    @Override
    public void addAction(EvidenceAction action) {
        EvidenceSupportAction spa = (EvidenceSupportAction) action;
        spa.setWork(this);
        actions.add(spa);
    }
    @Override
    public void addComment(EvidenceComment comment) {
        EvidenceSupportComment spc = (EvidenceSupportComment) comment;
        spc.setWork(this);
        this.comment = spc;
    }

    @Override
    public void addAttachment(EvidenceAttachment attachment) {
        EvidenceSupportAttachment spa = (EvidenceSupportAttachment) attachment;
        spa.setWork(this);
        attachments.add(spa);
    }

    public void addFlag(EvidenceSupportFlag flag) {
        flag.setWork(this);
        flags.add(flag);
    }

    @Override
    public void addActivity(EvidenceActivity activity) {
        ServiceRecipientActivity ra = (ServiceRecipientActivity) activity;
        ra.setWork(this);
        activities.add(ra);
    }
    @Override
    public void addAnswer(EvidenceAnswer answer) {
        EvidenceSupportAnswer spa = (EvidenceSupportAnswer) answer;
        spa.setWork(this);
        answers.add(spa);
    }

    public void setComment(EvidenceSupportComment comment) {
        this.comment = comment;
    }

    public String getTaskName() {
        return TaskDefinitionNameIdMappings.fromTaskDefIdToTaskName(getTaskDefId());
    }
}
