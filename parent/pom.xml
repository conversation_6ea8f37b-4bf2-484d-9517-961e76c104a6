<?xml version="1.0" encoding="UTF-8"?>

<!--suppress MavenModelInspection, MavenModelInspection -->
<project    xmlns="http://maven.apache.org/POM/4.0.0"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <artifactId>parent</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0.CI-SNAPSHOT</version>

    <parent>
        <groupId>org.eccosolutions</groupId>
        <artifactId>parent-spring-boot</artifactId>
        <version>1.0.0.CI-SNAPSHOT</version>
        <relativePath>../parent-spring-boot/pom.xml</relativePath>
    </parent>

    <properties>
        <cargo.tomcat.version>8.5.95</cargo.tomcat.version>
        <tomcat.version>8.5.95</tomcat.version>

        <m2e.jaxrs.activation>false</m2e.jaxrs.activation>
        <m2e.jpa.activation>false</m2e.jpa.activation>
        <m2e.jsf.activation>false</m2e.jsf.activation>
        <m2e.wro4j.wtp.integration>false</m2e.wro4j.wtp.integration>

        <!-- so we can override to target-ide for m2e -->
        <target.dir>target</target.dir>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- Spring IO Platform version overrides (these should be minimised) -->
        <!-- see ? https://www.baeldung.com/spring-security-with-maven for difficulty with maven, eg 340329ca -->
        <json-path.version>2.2.0</json-path.version>
        <json-patch.version>1.9</json-patch.version>
        <jackson.version>2.11.4</jackson.version>
        <hibernate.version>5.4.32.Final</hibernate.version>
        <hibernate-validator.version>6.0.22.Final</hibernate-validator.version>
        <lombok.version>1.18.20</lombok.version>

        <!-- Non-Spring IO Platform dependency versions -->
        <querydsl.version>4.4.0</querydsl.version>
        <activiti.version>5.23.0</activiti.version>
        <args4j.version>2.0.16</args4j.version>
        <batik.version>1.17</batik.version>
        <cloudfoundry.version>0.8.2</cloudfoundry.version>
        <dbunit.version>2.4.8</dbunit.version>
        <jasypt.version>1.9.2</jasypt.version>
        <jsoup.version>1.15.3</jsoup.version>
        <jtds.version>1.3.1</jtds.version>
        <sqljdbc4.version>4.2</sqljdbc4.version>
        <esendex.version>4.0.3</esendex.version>
        <liquibase-slf4j.version>1.2.1</liquibase-slf4j.version>
        <mbassador.version>1.2.4</mbassador.version>
        <mbassador-spring.version>1.0.0.beta-SNAPSHOT</mbassador-spring.version>
        <ojdbc8.version>12.2.0.1</ojdbc8.version>
        <opencsv.version>2.3</opencsv.version>
        <p6spy.version>1.3</p6spy.version>
        <spring-security-kerberos.version>1.0.1.RELEASE</spring-security-kerberos.version>
        <spring-test-dbunit.version>1.1.0</spring-test-dbunit.version>
        <synyx-messagesource.version>0.6.1</synyx-messagesource.version>
        <usertype-core.version>7.0.0.CR1</usertype-core.version>
        <usertype-jodatime.version>USE_USERTYPE-CORE_INSTEAD</usertype-jodatime.version>
        <unitils.version>3.3</unitils.version>
        <ognl.version>2.6.11</ognl.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency><!-- TODO: We should consume BOM like this so that properties give the versions we want -->
                <groupId>org.eccosolutions</groupId>
                <artifactId>parent-spring-boot</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <!-- THINGS WE WANT TO EXCLUDE TO AVOID DUPLICATES -->
            <dependency>
                <groupId>asm</groupId>
                <artifactId>asm</artifactId>
                <version>EXCLUDE_USE_asm_4</version>
            </dependency>
            <dependency>
                <groupId>opensymphony</groupId>
                <artifactId>quartz</artifactId>
                <version>ExcludeAndUse-org.quartz-scheduler</version>
            </dependency>
            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>ExcludeAndUse-org.opensymphony-ognl</version>
            </dependency>
            <dependency>
                <groupId>bouncycastle</groupId>
                <artifactId>bcmail-jdk14</artifactId>
                <version>ExcludeAndUse-org.bouncycastle</version>
            </dependency>
            <dependency>
                <groupId>bouncycastle</groupId>
                <artifactId>bcprov-jdk14</artifactId>
                <version>ExcludeAndUse-org.bouncycastle</version>
            </dependency>
            <dependency>
                <groupId>net.sf.jasperreports</groupId>
                <artifactId>jasperreports</artifactId>
                <version>4.7.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk14</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.lowagie</groupId>
                <artifactId>itext</artifactId>
                <version>2.1.7</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcmail-jdk14</artifactId>
                        <groupId>bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bcprov-jdk14</artifactId>
                        <groupId>bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bctsp-jdk14</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <!--Repeat of what is in platform bom as it doesn't actually override properties reliably with 3.5.3-->
                <!-- annotation processing tool for Lombok goodies -->
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>99.0.0.EMPTY</version> <!-- This is an empty jar as the simplest way of global exclusion -->
                <type>jar</type>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>EXCLUDE-use-ehcache-core</version>
            </dependency>
            <dependency>
                <groupId>net.sf.ehcache.internal</groupId>
                <artifactId>ehcache-core</artifactId>
                <version>USE_ORG_EHCACHE:EHCACHE_INSTEAD</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>DONT_USE_EXCLUDE</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>DONT_USE_EXCLUDE</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>DONT_USE_EXCLUDE</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-incidents</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-repairs</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-buildings</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-config</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-messaging</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-notifications</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-data-client</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-dao</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-service-config</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-dom</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-servicerecipient</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-evidence</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-service</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-group-support</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-infrastructure</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.ecco.integration</groupId>
                <artifactId>ecco-int-api-default</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-hr</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-reports</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-calendar</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-rota</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-contracts</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-finance</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-submissions-sp</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-web</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-calendar-core</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-calendar-cosmo</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>quartz</artifactId>
                        <groupId>opensymphony</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-contacts</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-security-core</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-security</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-security-ldap</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>test-support</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>test-entities</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-upload-dom</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-upload-web</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.dbunit</groupId>
                <artifactId>dbunit</artifactId>
                <version>${dbunit.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jadira.usertype</groupId>
                <artifactId>usertype.jodatime</artifactId>
                <version>${usertype-jodatime.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jadira.usertype</groupId>
                <artifactId>usertype.core</artifactId>
                <version>${usertype-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jdbc</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>sqljdbc4</artifactId>
                <version>${sqljdbc4.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>esendex.sdk.java</groupId>
                <artifactId>esendex</artifactId>
                <version>${esendex.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc8</artifactId>
                <!--
                 download from https://www.oracle.com/database/technologies/appdev/jdbc-downloads-2.html
                 install with mvn install:install-file -DgroupId=com.oracle -DartifactId=ojdbc7 -Dversion=12.2.0.1 -Dpackaging=jar -Dfile=ojdbc8.jar -DgeneratePom=true
                -->
                <version>${ojdbc8.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-guava</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-kotlin</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-jsonSchema</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <!-- requires jackson 2.2 -->
                <groupId>com.github.fge</groupId>
                <artifactId>json-patch</artifactId>
                <version>${json-patch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.9</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.28.0-GA</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-ehcache</artifactId>
                <version>${hibernate.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sf.ehcache</groupId>
                        <artifactId>ehcache</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.jknack</groupId>
                <artifactId>handlebars-springmvc</artifactId>
                <version>4.3.0</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-entitymanager</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-envers</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-jpamodelgen</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-offline</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-web-api</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-workflow</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.eccosolutions</groupId>
                <artifactId>ecco-workflow-activiti</artifactId>
                <version>1.0.0.CI-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-engine</artifactId>
                <version>${activiti.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun.xml.bind</groupId>
                        <artifactId>jaxb-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.xml.bind</groupId>
                        <artifactId>jaxb-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring</artifactId>
                <version>${activiti.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-dbcp</groupId>
                        <artifactId>commons-dbcp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.xml.bind</groupId>
                        <artifactId>jaxb-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.xml.bind</groupId>
                        <artifactId>jaxb-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.security.kerberos</groupId>
                <artifactId>spring-security-kerberos-web</artifactId>
                <version>${spring-security-kerberos.version}</version>
            </dependency>
            <dependency>
                <!-- Overridden version due to CVE -->
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-rsa</artifactId>
                <version>1.0.11.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-transcoder</artifactId>
                <version>${batik.version}</version>
            </dependency>
            <dependency>
                <groupId>args4j</groupId>
                <artifactId>args4j</artifactId>
                <version>${args4j.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mattbertolini</groupId>
                <artifactId>liquibase-slf4j</artifactId>
                <version>${liquibase-slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark</artifactId>
                <version>0.62.2</version>
            </dependency>
            <dependency>
                <groupId>org.jasypt</groupId>
                <artifactId>jasypt</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jasypt</groupId>
                <artifactId>jasypt-spring31</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.eccosolutions</groupId>
                <artifactId>jasypt-hibernate5</artifactId>
                <version>bea53d4e9f</version>
            </dependency>
            <dependency>
                <groupId>net.engio</groupId>
                <artifactId>mbassador</artifactId>
                <version>${mbassador.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mbassy</groupId>
                <artifactId>mbassador-spring</artifactId>
                <version>${mbassador-spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjweaver</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.synyx</groupId>
                <artifactId>messagesource</artifactId>
                <version>${synyx-messagesource.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>org.springframework.jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>org.springframework.context.support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>org.springframework.beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging-api</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <!-- just ensure scope is test -->
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.springtestdbunit</groupId>
                <artifactId>spring-test-dbunit</artifactId>
                <version>${spring-test-dbunit.version}</version>
            </dependency>
            <dependency> <!-- Now only used for ReflectionAssert -->
                <groupId>org.unitils</groupId>
                <artifactId>unitils-core</artifactId>
                <version>${unitils.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>embedded</id>
            <properties>
                <db>h2</db>
                <env>prod</env>
                <liquibase>embedded</liquibase>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>dev-oracle</id>
            <properties>
                <spring.profiles.active>dev-oracle</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>remote</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>fast</id>
            <properties>
                <skipTests>true</skipTests>
                <!-- skipITs is included in skipTests - see http://maven.apache.org/plugins/maven-failsafe-plugin/examples/skipping-test.html -->
                <!--<skipITs>true</skipITs>-->
                <!-- this would skip compiling all the tests - a bit overkill -->
                <!-- <maven.test.skip>true</maven.test.skip> -->
            </properties>
        </profile>
        <!-- one attempt to knock-out ecco-acceptance-tests on the fast profile - however maven looks for a property, not a profile name - see ecco-acceptance-tests for full explanation
        <profile>
            <id>activate-ecco-acceptance-tests</id>
              <activation>
                  <property><name>!fast</name></property>
              </activation>
              <dependencies>
                <dependency>
                    <groupId>org.eccosolutions</groupId>
                    <artifactId>ecco-acceptance-tests</artifactId>
                    <version>1.0.0.CI-SNAPSHOT</version>
                    <type>war</type>
                </dependency>
            </dependencies>
        </profile>
        -->
    </profiles>

    <build>
        <!-- the other option is <directory>${target.dir}</directory> -->
        <outputDirectory>${basedir}/${target.dir}/classes</outputDirectory>
        <testOutputDirectory>${basedir}/${target.dir}/test-classes</testOutputDirectory>

        <!-- include non-java files in the target/test-classes -->
        <!-- since unitils expects the DataSet xml files to be there -->
        <!-- they are placed there by a project -> clean, but that doesn't help maven consistency -->
        <!-- http://stackoverflow.com/questions/4221285/maven-surefire-copy-test-resources-from-src-test-java -->
        <testResources>
            <testResource>
                <directory>${project.basedir}/src/test/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
            <testResource>
                <directory>${project.basedir}/src/test/resources</directory>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <executions>
                    <execution>
                        <id>install-ojdbc</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>install-file</goal>
                        </goals>
                        <inherited>false</inherited>
                        <configuration>
                            <groupId>com.oracle</groupId>
                            <artifactId>ojdbc8</artifactId>
                            <version>12.2.0.1</version>
                            <packaging>jar</packaging>
                            <file>../setup/ojdbc8-12.2.0.1.jar</file>
                        </configuration>
                    </execution>
                    <execution>
                        <id>install-sqljdbc4</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>install-file</goal>
                        </goals>
                        <inherited>false</inherited>
                        <configuration>
                            <groupId>com.microsoft.sqlserver</groupId>
                            <artifactId>sqljdbc4</artifactId>
                            <version>4.2</version><!-- sqljdbc_4.2.6420.100_enu.tar.gz -->
                            <packaging>jar</packaging>
                            <file>../setup/sqljdbc42.jar</file>
                        </configuration>
                    </execution>
                    <!--
                    NB required: commons-logging-1.2.jar / mxparser-1.2.2.jar / xmlpull-1.1.3.1.jar / xstream-1.4.19.jar
                    -->
                    <execution>
                        <id>install-esendex</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>install-file</goal>
                        </goals>
                        <inherited>false</inherited>
                        <configuration>
                            <groupId>esendex.sdk.java</groupId>
                            <artifactId>esendex</artifactId>
                            <version>4.0.3</version>
                            <packaging>jar</packaging>
                            <file>../setup/esendex-java-sdk-4.0.3.jar</file>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <!-- configure the plugins for utf-8 -->
                <!-- http://maven.apache.org/plugins/maven-resources-plugin/examples/encoding.html -->
                <!-- http://maven.apache.org/general.html#special-characters-site -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.8.2</version>
                    <configuration>
                        <outputEncoding>UTF-8</outputEncoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-maven-plugin</artifactId>
                    <version>${kotlin.version}</version>
                    <configuration>
                        <jvmTarget>11</jvmTarget>
                        <compilerPlugins>
                            <plugin>spring</plugin>
                        </compilerPlugins>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.jetbrains.kotlin</groupId>
                            <artifactId>kotlin-maven-allopen</artifactId>
                            <version>${kotlin.version}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>compile</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>compile</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>test-compile</id>
                            <phase>test-compile</phase>
                            <goals>
                                <goal>test-compile</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                    <executions>
                        <!-- Replacing default-compile as it is treated specially by maven -->
                        <execution>
                            <id>default-compile</id>
                            <phase>none</phase>
                        </execution>
                        <!-- Replacing default-testCompile as it is treated specially by maven -->
                        <execution>
                            <id>default-testCompile</id>
                            <phase>none</phase>
                        </execution>
                        <execution>
                            <id>java-compile</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>compile</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>java-test-compile</id>
                            <phase>test-compile</phase>
                            <goals>
                                <goal>testCompile</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- add to manifest - perhaps useful later on -->
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.0</version>
                    <configuration>
                        <archive>
                            <manifestFile>src/main/resources/META-INF/MANIFEST.MF</manifestFile>
                            <manifest>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <!-- war config from cosmo-event-integration -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>3.3.1</version>
                    <configuration>
                        <archive>
                            <!-- exclude META-INF/maven -->
                            <addMavenDescriptor>false</addMavenDescriptor>
                        </archive>
                        <!-- don't fail because we don't need a web.xml -->
                        <failOnMissingWebXml>false</failOnMissingWebXml>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version>
                    <configuration>
                        <!-- MAVEN_OPTS are not passed on to the forked test-runner
                             so our jvm for the forked tests takes the jvm default
                             see http://stackoverflow.com/a/4074196.
                             Also this can't be added to the systemProperties as it needs to be at jvm startup,
                             see http://stackoverflow.com/a/23466219
                        -->
                        <!-- set the memory according to local scripts, but could try using ${env.MAVEN_OPTS}
                             which would be more flexible and take the existing values
                             see - http://stackoverflow.com/a/4074196
                        -->
                        <argLine>${env.MAVEN_OPTS}</argLine>
                        <systemProperties>
                            <spring.profiles.active>${spring.profiles.active}</spring.profiles.active>
                        </systemProperties>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default-test</id>
                            <phase>test</phase>
                            <goals>
                                <goal>test</goal>
                            </goals>
                            <configuration>
                                <excludes>
                                    <exclude>**/*IT.class</exclude>
                                    <exclude>**/*IntegrationTest.class</exclude>
                                </excludes>
                            </configuration>
                        </execution>
                        <!-- use *IT naming convention for long-running integration tests -->
                        <execution>
                            <id>integration-test</id>
                            <phase>integration-test</phase>
                            <goals>
                                <goal>test</goal>
                            </goals>
                            <configuration>
                                <skipTests>${skipITs}</skipTests>
                                <includes>
                                    <include>**/*IT.class</include>
                                    <include>**/*IntegrationTest.class</include>
                                </includes>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.cargo</groupId>
                    <artifactId>cargo-maven2-plugin</artifactId>
                    <version>1.6.9</version>
                    <configuration>
                        <container>
                            <!-- containerId determines the adapter used to run things. e.g. tomcat8x -->
                            <containerId>tomcat8x</containerId>
                            <artifactInstaller>
                                <groupId>org.apache.tomcat</groupId>
                                <artifactId>tomcat</artifactId>
                                <version>${cargo.tomcat.version}</version>
                            </artifactInstaller>
                            <timeout>180000</timeout>
                            <systemProperties>
                                <env>${env}</env> <!-- NOTE: Must be empty for spring.profiles.active to work -->
                                <db>${db}</db>
                                <log4j.configuration>log4j-dev.xml</log4j.configuration>
                                <liquibase>${liquibase}</liquibase>
                                <db.extraContexts>acceptanceTests</db.extraContexts>
                                <spring.profiles.active>${spring.profiles.active}</spring.profiles.active>
                                <user.timezone>UTC</user.timezone>
                                <java.locale.providers>JRE,SPI</java.locale.providers>
                            </systemProperties>
                            <timeout>180000</timeout>
                        </container>
                        <configuration>
                            <type>standalone</type>
                            <home>
                                ${project.build.directory}/apache-tomcat-${cargo.tomcat.version}
                            </home>
                            <properties>
                                <cargo.servlet.port>${httpPort}</cargo.servlet.port>
                                <cargo.tomcat.copywars>true</cargo.tomcat.copywars>
                                <!--<cargo.logging>high</cargo.logging>-->
                                <!-- For debugging using "Remote debug" run config in IntelliJ
                                     See https://stackoverflow.com/a/36449492/1998186 for instructions -->
                                <!--<cargo.start.jvmargs>-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=5005
                                     -Xnoagent -Djava.compiler=NONE</cargo.start.jvmargs>-->
                            </properties>
                        </configuration>
                        <deployables>
                            <!--<deployable>-->
                                <!--<groupId>org.eccosolutions</groupId>-->
                                <!--<artifactId>ecco-war</artifactId>-->
                                <!--<version>1.0.0.CI-SNAPSHOT</version>-->
                                <!--<type>war</type>-->
                                <!--<properties>-->
                                    <!--<context>/ecco-war</context>-->
                                <!--</properties>-->
                            <!--</deployable>-->
                        </deployables>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.3.2</version>
                    <configuration>
                        <releaseProfiles>release,central</releaseProfiles>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <tagNameFormat>release-@{project.version}</tagNameFormat>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>1.0.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <!-- for milestones
        <repository>
            <id>spring-milestones</id>
            <url>http://maven.springframework.org/milestone</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository> -->
    </repositories>

    <dependencies>
        <!-- spring security version means we need this to avoid this error on 365 -->
        <!--  java.lang.NoSuchMethodError: 'java.util.Map com.nimbusds.jose.Header.toJSONObject()' -->
        <!-- see https://github.com/spring-projects/spring-security/issues/9120 -->
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>9.0.1</version>
        </dependency>

        <!-- also have the latest simple logging framework for java -->
        <!-- this is used in hibernate who made the switch from commons-logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!-- include the logging wrapper which goes to log4j -->
        <!-- match the version number of the slf4j-api -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
<!--            <version>${log4j2.version}</version>-->
        </dependency>


        <!-- "The Apache Taglibs project contains a reference implementation of JSTL" -->
        <!-- jstl is provided in all but tomcat -->
        <!-- its a convenient addition to provide c: fmt: etc, -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- allows annotations but using a correct maven package from spring -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <version>${querydsl.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <scope>provided</scope>
            <version>${querydsl.version}</version>
            <classifier>jpa</classifier>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <version>${querydsl.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>

        <!-- we sometimes have cache settings and cache enabled in the config for tests -->
        <!-- useful at the top level because our include test jars means unitils picks up the hibernate config -->

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
            <scope>provided</scope>
            <!--<scope>compile</scope> &lt;!&ndash; We provide it because Tomcat 7 doesn't provide el-api 3.0.0 &ndash;&gt;-->
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <scope>provided</scope><!-- provided by aspectjweaver -->
        </dependency>

        <!-- Dependencies now needed to be supplied by us, as Java 9 onwards don't ahve them by default.
     see https://stackoverflow.com/a/43574427/1998186 -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.5</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>javax.activation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.messaging.saaj</groupId>
            <artifactId>saaj-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
