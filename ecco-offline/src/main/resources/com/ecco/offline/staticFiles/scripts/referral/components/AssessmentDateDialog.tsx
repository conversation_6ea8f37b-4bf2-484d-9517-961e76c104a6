import * as React from "react"
import {ReactNode} from "react";
import {
    CommandSubform,
    datePickerInput,
    dateTimeIso8601Input,
    dropdownList,
    Loading,
    numberInput,
    possiblyModalForm,
    textArea,
    textInput,
    TaskSummary,
    useCommandForm,
    useCurrentServiceRecipientWithEntities,
    WithLatestCommands
} from "ecco-components";

import {ReferralDto, ReferralSummaryWithEntities, TaskNames} from "ecco-dto";
import {AssessmentDateCommand, CommandQueue} from "ecco-commands";
import {EccoDate, EccoDateTime, IdNameDisabled, StringUtils} from "@eccosolutions/ecco-common";
import {Grid} from '@eccosolutions/ecco-mui';

import services = require("ecco-offline-data");
import {TaskWithTitle} from "ecco-dto/workflow-dto";

export function AssessmentDateDialog({task}: {task: TaskWithTitle}) {
    const {resolved, reload} = useCurrentServiceRecipientWithEntities()
    const commandForm = useCommandForm()
    return <AssessmentDate
            referral={resolved.referral!}
            taskHandle={task.taskHandle}
            commandForm={commandForm}
            afterSave={reload}
    />
}


interface Props {
    referral: ReferralSummaryWithEntities;
    taskHandle: string;
    afterSave: () => void
}

interface State {
    interviewer1?: number;
    interviewer2?: number;
    decisionDate?: string;
    firstOfferedInterviewDate?: EccoDate;
    location?: string;
    interviewSetupComments?: string;
    interviewDna: number;
    interviewDnaComments?: string;
    workersWithAccess?: IdNameDisabled[];
    referral: ReferralDto;
}
export class AssessmentDate extends CommandSubform<Props, State> {

    constructor(props) {
        super(props);
        let referral = this.props.referral;
        this.state = {
            interviewer1: referral.interviewer1ContactId,
            decisionDate: referral.decisionDate,
            firstOfferedInterviewDate: EccoDate.parseIso8601FromDateTime(referral.firstOfferedInterviewDate!),
            interviewDna: referral.interviewDna,
            referral: null!
        };

        services.getReferralRepository().findOneReferral(referral.referralId).then(referral =>
            services.getWorkerRepository().findWorkersWithSameAccess(this.props.referral.features).then(workersWithAccess => {
                this.setState({
                    workersWithAccess,
                    location: referral.interviewLocation,
                    interviewSetupComments: referral.interviewSetupComments,
                    interviewer2: referral.interviewer2ContactId,
                    interviewDnaComments: referral.interviewDnaComments,
                    referral
                });
            })
        );
    }

    public emitChangesTo(commandQueue: CommandQueue) {
        const cmd = new AssessmentDateCommand(this.props.referral.serviceRecipientId, this.props.taskHandle)
            .changeInterviewer1ContactId(this.props.referral.interviewer1ContactId, this.state.interviewer1)
            .changeInterviewer2ContactId(this.state.referral.interviewer2ContactId, this.state.interviewer2)
            .changeDecisionDate(EccoDateTime.parseIso8601(this.props.referral.decisionDate), EccoDateTime.parseIso8601IgnoringTimezone(this.state.decisionDate))
            .changeFirstOfferedInterviewDate(EccoDate.parseIso8601FromDateTime(this.props.referral.firstOfferedInterviewDate), this.state.firstOfferedInterviewDate)
            .changeInterviewDna(this.props.referral.interviewDna, this.state.interviewDna)
            .changeInterviewDnaComments(this.state.referral.interviewDnaComments, this.state.interviewDnaComments)
            .changeInterviewSetupComments(this.state.referral.interviewSetupComments, this.state.interviewSetupComments)
            .changeLocation(this.state.referral.interviewLocation, this.state.location);


        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    private getFieldsToShow() {
        const serviceType = this.props.referral.features.getServiceTypeByServiceCategorisationId(this.props.referral.serviceAllocationId);
        // assume all fields
        let fieldsToShow: (keyof State)[] = ["interviewer1","interviewer2","decisionDate","firstOfferedInterviewDate","location","interviewSetupComments","interviewDna","interviewDnaComments","workersWithAccess"];
        const fieldsCsv: string = serviceType.getTaskDefinitionSetting(TaskNames.assessmentDate,  "hideAssessmentDateFields");
        if (fieldsCsv) {
            const fieldsToHide = StringUtils.csvToArray(fieldsCsv) as (keyof State)[];
            fieldsToShow = fieldsToShow.filter(f => fieldsToHide.indexOf(f) == -1);
        }
        return fieldsToShow;
    }

    render() {
        const messages = this.props.referral.features.getMessages();

        const fieldsToShow = this.getFieldsToShow();
        const allowedField = (field: keyof State) => {
            return fieldsToShow.indexOf(field) > -1;
        };
        const showInput = (propertyKey: keyof State, elm: ReactNode) =>
            allowedField(propertyKey) && <Grid item xs={12}>
                {elm}
            </Grid>

        return possiblyModalForm(
            "initial assessment",
            true, true,
            () => this.props.commandForm.cancelForm(),
            () => this.props.commandForm.submitForm().then(this.props.afterSave),
            false,
            false,
            <Loading loaded={this.state.workersWithAccess != null}>
                {this.state.workersWithAccess && <Grid container>
                    <Grid item xs={12}>
                        <WithLatestCommands srId={this.props.referral.serviceRecipientId}>
                            <TaskSummary srId={this.props.referral.serviceRecipientId}
                                     taskName={TaskNames.assessmentDate} useLatestAudit={true}/>
                        </WithLatestCommands>
                    </Grid>
                    {showInput("interviewer1",
                        /*as per StartOnService*/
                        dropdownList("interviewer 1", state => this.setState(state), this.state, "interviewer1",
                            this.state.workersWithAccess)
                    )}
                    {showInput("interviewer2",
                       /*as per StartOnService*/
                        dropdownList("interviewer 2", state => this.setState(state), this.state, "interviewer2",
                            this.state.workersWithAccess)
                    )}
                    {showInput("decisionDate",
                        dateTimeIso8601Input("decisionDate", "interview date & time", state => this.setState(state), this.state)
                    )}
                    {showInput("firstOfferedInterviewDate",
                        datePickerInput("firstOfferedInterviewDate", messages["form.assessmentDate.firstOfferedInterviewDate"], state => this.setState(state), this.state)
                    )}
                    {showInput("location",
                        textInput("location", "interview location", state => this.setState(state), this.state)
                    )}
                    {showInput("interviewSetupComments",
                        textArea("interviewSetupComments", "interview setup comments", state => this.setState(state), this.state)
                    )}
                    {showInput("interviewDna",
                        numberInput("interviewDna", "number of times client did not attend", state => this.setState(state), this.state)
                    )}
                    {showInput("interviewDnaComments",
                        textArea("interviewDnaComments", "did not attend comments", state => this.setState(state), this.state, "comments relating to client missing appointments")
                    )}
                </Grid>}
            </Loading>
        );
    }
}
