import {CommandForm, ModalCommandForm} from "ecco-components";
import * as React from "react";
import {ReactChild, ReactNode, useState} from "react";
import {unmountComponentAtNode} from "react-dom";
import {mountWithServices} from "../offline/ServicesContextProvider";
import {Button, PropTypes} from "@eccosolutions/ecco-mui";

type ButtonProps = {
    buttonLabel: ReactNode
    modalTitle: string
    children: ReactChild
    color?: PropTypes.Color
}
export const CommandFormButton = ({buttonLabel, modalTitle, children, color}: ButtonProps) => {

    const [show, setShow] = useState(false);

    return <>
        <Button variant="text" size="small" color={color} onClick={() => setShow(true)}>{buttonLabel}</Button>
        <ModalCommandForm
                title={modalTitle}
                action="save"
                show={show}
                setShow={setShow}
        >
            {children}
        </ModalCommandForm>
    </>
}

/**
 * Show the content that may contain a possibly modal form or other content that uses CommandForm, and on completion
 * unmount everything.
 * This will mean that a modal or in place editor will disappear on save or cancel.
 * @param content React content to mount
 * @param onCompleted This is called after the form is saved or cancelled and has been unmounted
 * @param mountPoint where to mount content.  Just adds a div to document if this is not provided (e.g. for modals)
 */
export function showInCommandForm(content: ReactNode, onCompleted = () => {}, mountPoint?: HTMLElement) {
    const mount = mountPoint || document.createElement("div");
    const unmount = () => {
        unmountComponentAtNode(mount);
        onCompleted();
    };


    mountWithServices(
        <CommandForm
            onCancel={unmount}
            onFinished={unmount}>
            {content}
        </CommandForm>,
        mount);
}

/*
export class ExampleSubform extends CommandSubform<{commandForm: CommandForm, formUuid: string}, {}> implements Component {
    constructor(props) {
        super(props);
    }

    emitChangesTo(cmdQ: CommandQueue) {
        // not needed
    }

    render() {
        return <div>Example subform</div>;
    }
}
 */

// noinspection JSUnusedGlobalSymbols
/*
export class ExampleCompoundForm extends Component<{}, {}> {
    render() {

        // What's missing here is the modal and validation.
        // Clearly CommandForm (or some other parent) needs to be able to validate all the subforms and decide if
        // there are any changes.  Or.. the subforms report that they are valid or invalid <by key?>
        return (
            <CommandForm onCancel={() => {}} onFinished={() => {}}>
                {withCommandForm(form => <React.Fragment>
                    <EccoV3Modal title="sdf" action="update" saveEnabled={true} show={true}
                        onCancel={() => {}} onSave={() => form.submitForm()}>
                        <ExampleSubform key={1} commandForm={form} formUuid="uuid1"/>
                        <ExampleSubform key={2} commandForm={form} formUuid="uuid2"/>
                    </EccoV3Modal>
                </React.Fragment>)}
            </CommandForm>
        );
    }
}
*/