import * as React from "react"
import {FC, useMemo} from "react";

import {EccoDate, IdNameDisabled} from "@eccosolutions/ecco-common";
import {
    CommandQueue,
    CommandSource, ReferralDetailsCommand,
    IncidentDetailCommand,
    RepairDetailCommand
} from "ecco-commands";
import {
    CommandSubform,
    datePickerInput, EditTaskSubform, getGuidanceUrlCallback, LoadingOrError,
    possiblyModalForm,
    SelectList, stringFromHtmlInput,
    useCurrentServiceRecipientWithEntities, useReloadHandler, useRepairRates,
    useServicesContext,
    withCommandForm
} from "ecco-components";
import {
    ApiClient,
    ListDefinitionEntry,
    listDefToIdName,
    PrefixType, RepairRateDto,
    SessionData,
    SessionDataAjaxRepository,
    TaskNames
} from "ecco-dto";

import {Grid, RadioGroup, Radio, FormControlLabel, TextField} from '@eccosolutions/ecco-mui';
import {CustomForm} from "./CustomForm";
import ListDefHierarchicalSharedValueAutoSelectList from "../../data-attr/ListDefHierarchicalSharedValueAutoSelectList";
import ControlWrapper from "../../components/ControlWrapper";
import ListDefSelectList from "../../data-attr/ListDefSelectList";
import {applicationRootPath} from "application-properties";

interface ReferralDetailsEditorProps {
    taskHandle: string;
}

export default function ReferralDetailsForm(props: ReferralDetailsEditorProps) {
    const {resolved: context, reload} = useCurrentServiceRecipientWithEntities()
    const {sessionData} = useServicesContext()

    if (!context || !sessionData) return <LoadingOrError/>;

    const guidanceFormDefinitionUuid = context.serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(TaskNames.referralDetails, "guidanceFormDefinition");
    const guidanceCallback = guidanceFormDefinitionUuid ? getGuidanceUrlCallback(guidanceFormDefinitionUuid) : undefined;

    let dtoConverted: CommonDetailsDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoConverted = {
                serviceRecipientId: context.incident.serviceRecipientId,
                serviceAllocationId: context.incident.serviceAllocationId,
                receivedDate: context.incident.receivedDate,
                //srcGeographicAreaId?: undefined;
                incidentDetails: {
                    reportedById: context.incident.reportedById,
                    reportedBy: context.incident.reportedBy,
                    reportedByContact: context.incident.reportedByContact,
                    categoryId: context.incident.categoryId,
                    emergencyServicesInvolved: context.incident.emergencyServicesInvolved,
                    hospitalisationInvolved: context.incident.hospitalisationInvolved
                }
            }
            break;

        case "m":
            dtoConverted = {
                serviceRecipientId: context.repair.serviceRecipientId,
                serviceAllocationId: context.repair.serviceAllocationId,
                receivedDate: context.repair.receivedDate,
                //srcGeographicAreaId?: undefined;
                repairDetails: {
                    categoryId: context.repair.categoryId,
                    rateId: context.repair.rateId,
                    priorityId: context.repair.priorityId
                }
            }
            break;

        default:
            dtoConverted = context.referral
    }

    return withCommandForm(commandForm =>
        possiblyModalForm(
            "details",
            true, true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm().then(reload),
            false, // TODO could emitChangesTo and see if there are any commands
            false,
            <ReferralDetailsSubform
                    sessionData={sessionData}
                    dto={dtoConverted}
                    taskHandle={props.taskHandle}
                    readOnly={!sessionData.hasRoleReferralEdit()}
                    commandForm={commandForm}
                    type={context.serviceRecipient.prefix}
            />,
            undefined,
            guidanceCallback
        )
    )
}

export interface IncidentDetailsDto {
    reportedById: number | null;
    reportedBy: string | null;
    reportedByContact: string | null;
    categoryId: number | null;
    hospitalisationInvolved: boolean | null;
    emergencyServicesInvolved: boolean | null;
}
export interface RepairDetailsDto {
    categoryId: number | null;
    rateId: number | null;
    priorityId: number | null;
}
interface CommonDetailsDto {
    serviceRecipientId: number;
    serviceAllocationId: number;
    /** The date this referral was received by the service provider, or null
     * if that date has not been recorded in Ecco. */
    receivedDate?: string;
    srcGeographicAreaId?: number;
    incidentDetails?: IncidentDetailsDto;
    repairDetails?: RepairDetailsDto;
}

interface Props {
    readOnly: boolean;
    dto: CommonDetailsDto;
    taskHandle: string;
    sessionData: SessionData;
    type: PrefixType;
}

interface State {
    receivedDate: EccoDate;
    srcGeographicAreaId: number;
    incidentDetails: IncidentDetailsDto;
    repairDetails: RepairDetailsDto;
}

const Entry = (props) => <Grid item sm={6} xs={12} >{props.children}</Grid>;

const INCIDENT_CATEGORY_LISTNAME = "incident-category";
const REPAIR_CATEGORY_LISTNAME = "repair-category";
const REPAIR_PRIORITY_LISTNAME = "repair-priority";

const apiClientInbound = new ApiClient(`${applicationRootPath}api/inbound/`,
                                       null, () => true);
const sessionDataInboundRepository = new SessionDataAjaxRepository(apiClientInbound);

// better as useControl, but we started here with specific arguments
// also see LocationControl in StaffOverviewPane
const HierarchicalList: FC<{dto: IncidentDetailsDto | null, setter: (details: IncidentDetailsDto) => void}> = props => {
    //const [currValue, setCurrValue] = useState(props.dto?.categoryId);
    const onChange = value => props.setter({...props.dto, categoryId: value ? parseInt(value) : null});

    const control = useMemo(() => {
        // attach to the dom, as it's used to pick up the initial value
        //const element = document.createElement("div");
        //element.setAttribute("id", "categoryIdField");
        // ** CANT'T USE DOM 'initial-value-shared' but the container could be used
        return new ListDefHierarchicalSharedValueAutoSelectList(
                ListDefSelectList.createContainer("categoryIdField", INCIDENT_CATEGORY_LISTNAME, props.dto?.categoryId),
                sessionDataInboundRepository,
                onChange)
    }, []);


    /*useEffect(() => {
        control.setInitialValueAndProcess(props.dto?.categoryId);
    }, [props.dto?.categoryId]);*/

    /*useEffect(() => {
        control.load();
    }, []);*/

    useReloadHandler(() => control.load());

    /* dontLoad=true - so that we don't call load() each render as it uses the initial value which is empty - use useEffect instead */
    /* the alternative of re-creating ControlWrapper and 'control' causes more fields to appear - which we could work to resolved */
    return <ControlWrapper control={control} />
};

export const IncidentFields: FC<{dto: IncidentDetailsDto | null, setter: (details: IncidentDetailsDto) => void}> = props => {

    return <>
        <Grid item xs={12}>
            <HierarchicalList {...props}/>
        </Grid>
        <Grid item xs={12}>
            <TextField
                name={`reportedBy`}
                label={"reported by (email)"}
                fullWidth={true}
                placeholder={""}
                required={false}
                onChange={event => props.setter({...props.dto, reportedBy: stringFromHtmlInput(event.target)})}
                value={props.dto.reportedBy}
            />
        </Grid>
        <Grid item xs={12}>
            <TextField
                name={`reportedByContact`}
                label={"reported by (phone)"}
                fullWidth={true}
                placeholder={""}
                required={false}
                onChange={event => props.setter({...props.dto, reportedByContact: stringFromHtmlInput(event.target)})}
                value={props.dto.reportedByContact}
            />
        </Grid>
        <Grid item xs={12}>
            <Entry>{"did the incident result in hospitalisation?"}</Entry>
            <RadioGroup row={true}
                        name="hospitalisationInvolved"
                        value={props.dto.hospitalisationInvolved} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
                        onChange={(_, value) => props.setter({...props.dto, hospitalisationInvolved: "true" == value})}
            >
                <FormControlLabel key={"hosp-yes"} value={true} control={<Radio/>} label={"yes"}/>
                <FormControlLabel key={"hosp-no"} value={false} control={<Radio/>} label={"no"}/>
            </RadioGroup>
        </Grid>
        <Grid item xs={12}>
            <Entry>{"were emergency services contacted?"}</Entry>
            <RadioGroup row={true}
                        name="emergencyServicesInvolved"
                        value={props.dto.emergencyServicesInvolved} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
                        onChange={(_, value) => props.setter({...props.dto, emergencyServicesInvolved: "true" == value})}
            >
                <FormControlLabel key={"emer-yes"} value={true} control={<Radio/>} label={"yes"}/>
                <FormControlLabel key={"emer-no"} value={false} control={<Radio/>} label={"no"}/>
            </RadioGroup>
        </Grid>
    </>
}

export const RepairFields: FC<{dto: RepairDetailsDto | null, setter: (details: RepairDetailsDto) => void}> = props => {
    const {sessionData} = useServicesContext()
    const {rates} = useRepairRates();

    if (!rates) {
        return null;
    }

    return (<>
        <Grid item xs={12}>
            <Entry>{"priority"}</Entry>
            <RadioGroup row={true}
                name="priority"
                value={props.dto.priorityId} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
                onChange={(_, value) => props.setter({...props.dto, priorityId: parseInt(value)})}
            >
                {getEntries(sessionData, REPAIR_PRIORITY_LISTNAME).map(entry =>
                   <FormControlLabel key={entry.getId()} value={entry.getId()} control={<Radio/>} label={entry.getDisplayName()}/>
                )}
            </RadioGroup>
        </Grid>
        {/*<Grid item xs={12}>
            <SelectList
                createNew={false}
                placeholder={"category"}
                getOptionLabel={l => l.getDisplayName()}
                getOptionValue={l => l.getId().toString()}
                value={getEntries(sessionData, REPAIR_CATEGORY_LISTNAME).filter(l => l.getId() == props.dto?.categoryId)}
                options={getEntries(sessionData, REPAIR_CATEGORY_LISTNAME)}
                onChange={value => props.setter({...props.dto, categoryId: (value as ListDefinitionEntry).getId()})}
            />
        </Grid>*/}
        <Grid item xs={12}>
            <SelectList
                createNew={false}
                placeholder={"rate"}
                getOptionLabel={l => `${l.area} - ${l.code} - ${l.description.substring(0, 30)}`}
                getOptionValue={l => l.id.toString()}
                value={rates.filter(l => l.id == props.dto?.rateId)}
                options={rates}
                onChange={value => props.setter({...props.dto, rateId: (value as RepairRateDto).id})}
            />
        </Grid>
    </>);
}

// See ListDefSelect2List.ts
function getEntries(f: SessionData, listName: string): Array<ListDefinitionEntry> {
    const initialEntries = f.getListDefinitionEntriesByListName(listName)
    // NB avoid repeating entries by putting the children in a separate listName
    let allEntries = initialEntries.slice(0);
    initialEntries.forEach((entry) => {
        allEntries = allEntries.concat(f.getListDefinitionEntriesUnderParentRecursively(entry.getId()));
    });
    return allEntries;
}

function getListEntryName(entry: ListDefinitionEntry): IdNameDisabled {
    const idName: IdNameDisabled = listDefToIdName(entry);
    idName.name = entry.getFullName();
    return idName;
}

// TODO the legacy 'details of referral' allowed anyone to edit when first entering details (see referralView_tasks.jspf)
// TODO there was also a 'print' option here, but not very useful on details of referral?

export class ReferralDetailsSubform extends CommandSubform<Props, State> implements CommandSource {

    constructor(props) {
        super(props);
        let dto = this.props.dto;
        this.state = {
            receivedDate: EccoDate.parseIso8601FromDateTime(dto.receivedDate),
            srcGeographicAreaId: dto.srcGeographicAreaId,
            incidentDetails: {
                reportedById: dto.incidentDetails?.reportedById || null,
                reportedBy: dto.incidentDetails?.reportedBy || null,
                reportedByContact: dto.incidentDetails?.reportedByContact || null,
                categoryId: dto.incidentDetails?.categoryId || null,
                emergencyServicesInvolved: dto.incidentDetails?.emergencyServicesInvolved || null,
                hospitalisationInvolved: dto.incidentDetails?.hospitalisationInvolved || null},
            repairDetails: {
                categoryId: dto.repairDetails?.categoryId || null,
                rateId: dto.repairDetails?.rateId || null,
                priorityId: dto.repairDetails?.priorityId || null}
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        if (this.props.type == "i") {
            this.queueIncidentDetailsCommand(commandQueue);
        } else if (this.props.type == "m") {
            this.queueRepairDetailsCommand(commandQueue);
        } else {
            this.queueReferralDetailsCommand(commandQueue);
        }
        //this.queueFormDataCommand(commandQueue);
    }

    getErrors(): string[] {
        return this.state.receivedDate ? []
                : ["received date required"];
    }

    protected queueReferralDetailsCommand(commandQueue: CommandQueue) {

        const cmd = new ReferralDetailsCommand(this.props.dto.serviceRecipientId, this.props.taskHandle)
                .changeRecievedDate(EccoDate.parseIso8601FromDateTime(this.props.dto.receivedDate), this.state.receivedDate)
                .changeSrcGeographicArea(this.props.dto.srcGeographicAreaId, this.state.srcGeographicAreaId);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    protected queueIncidentDetailsCommand(commandQueue: CommandQueue) {
        const cmd = new IncidentDetailCommand(this.props.dto.serviceRecipientId, this.props.taskHandle)
                .changeReceivedDate(EccoDate.parseIso8601FromDateTime(this.props.dto.receivedDate), this.state.receivedDate)
                .changeReportedBy(this.props.dto.incidentDetails.reportedBy, this.state.incidentDetails.reportedBy)
                .changeReportedByContact(this.props.dto.incidentDetails.reportedByContact, this.state.incidentDetails.reportedByContact)
                .changeCategoryId(this.props.dto.incidentDetails.categoryId, this.state.incidentDetails.categoryId)
                .changeEmergencyServicesInvolved(this.props.dto.incidentDetails.emergencyServicesInvolved, this.state.incidentDetails.emergencyServicesInvolved)
                .changeHospitalisationInvolved(this.props.dto.incidentDetails.hospitalisationInvolved, this.state.incidentDetails.hospitalisationInvolved);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    protected queueRepairDetailsCommand(commandQueue: CommandQueue) {
        const cmd = new RepairDetailCommand(this.props.dto.serviceRecipientId, this.props.taskHandle)
                .changeReceivedDate(EccoDate.parseIso8601FromDateTime(this.props.dto.receivedDate), this.state.receivedDate)
                .changePriorityId(this.props.dto.repairDetails.priorityId, this.state.repairDetails.priorityId)
                .changeCategoryId(this.props.dto.repairDetails.categoryId, this.state.repairDetails.categoryId)
                .changeRateId(this.props.dto.repairDetails.rateId, this.state.repairDetails.rateId)

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    render() {

        const setter = state => this.setState(state);
        const AREA_LISTNAME = "country-list";
        const areaChange = (o: ListDefinitionEntry) => {
            setter({...this.state, srcGeographicAreaId: (o as ListDefinitionEntry).getId()})
        }

        return (
            <Grid container>
                <Grid item xs={12}>
                    {datePickerInput("receivedDate", "received date", setter, this.state, this.props.readOnly, true)}
                </Grid>
                {(this.props.sessionData.isEnabled("referralDetails.srcGeographicArea") ||
                    this.props.sessionData.getServiceTypeByServiceCategorisationId(this.props.dto.serviceAllocationId).taskDefinitionSettingHasFlag(TaskNames.referralDetails, "srcGeographicArea", 'y'))
                    && <Grid item xs={12}>
                    <SelectList
                        createNew={false}
                        getOptionLabel={l => l.getDisplayName()}
                        getOptionValue={l => l.getId().toString()}
                        value={getEntries(this.props.sessionData, AREA_LISTNAME).filter(l => l.getId() == this.state.srcGeographicAreaId)}
                        options={getEntries(this.props.sessionData, AREA_LISTNAME)}
                        onChange={value => areaChange(value as ListDefinitionEntry)}
                    />
                </Grid>}
                {this.props.type == "i" &&
                    <IncidentFields dto={this.state.incidentDetails} setter={dto => this.setState({incidentDetails: {...this.state.incidentDetails, ...dto}})} />
                }
                {this.props.type == "m" &&
                    <RepairFields dto={this.state.repairDetails} setter={dto => this.setState({repairDetails: {...this.state.repairDetails, ...dto}})} />
                }
                <Grid item xs={12}>
                    <div className="v-gap-15">
                        <CustomForm serviceRecipientId={this.props.dto.serviceRecipientId}
                                    taskName={TaskNames.referralDetails}
                                    taskNameGroup={TaskNames.referralDetails}
                                    page={'embedded'}
                                    taskHandle={this.props.taskHandle} readOnly={this.props.readOnly}/>
                        <EditTaskSubform
                            taskHandle={this.props.taskHandle}
                        />
                    </div>
                </Grid>
            </Grid>
        );

        /* NOTE: the order of this in the JSX is important.
         * See https://facebook.github.io/react/docs/transferring-props.html */
    }

}
