import {<PERSON>, <PERSON><PERSON>, <PERSON>, CardA<PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Container, Grid} from "@eccosolutions/ecco-mui";
import {applicationRootPath} from "application-properties";
import {
    EccoV3Modal,
    handleLazy,
    PrintResourceLineIcon,
    ServiceAgreementsView,
    SimpleSchemaFormRenderer,
    TabsBuilder,
    useServiceRecipientWithEntities
} from "ecco-components";
import {DemandResource as RotaWorkerJob} from "ecco-rota";
import * as React from "react";
import {FC, useState} from "react";
import {lazyControlWrapper} from "../../components/ControlWrapper";
import {AuditHistory} from "../../service-recipients/components/AuditHistory";
import {AvailabilityView} from "../availability/AvailabilityView";
import {StaffOverviewPane} from "./StaffOverviewPane";
import {ReloadEvent} from "@eccosolutions/ecco-common";
import {showCareRun} from "../../buildings/components/BuildingForm";
import {CommandFormButton} from "../../components/CommandForm";
import {CustomForm, getCustomFormWrapper} from "../../referral/components/CustomForm";

export const RotaWorkerJobView: FC<{workerJob: RotaWorkerJob, showAvailability: boolean,
                    showExpandActionCard: boolean, setShowExpandActionCard: (boolean) => void}> = props => {
    const {workerJob, showAvailability, showExpandActionCard, setShowExpandActionCard} = props;

    const AppointmentsView = lazyControlWrapper(
        () => import("../../referral/ServiceRecipientAppointmentsControl"),
        // @ts-ignore - because typescript
        workerJob.getServiceRecipientId(), workerJob.getCalendarId(), workerJob.getName());

    const [show, setShow] = useState(false);
    const [editAvailability, setEditAvailability] = useState<"today"|"month"|null>(null);

    const isWorker = workerJob.getServiceRecipientDiscriminator() === "wrkr";

    const {context, loading} = useServiceRecipientWithEntities(props.workerJob.getServiceRecipientId())

    const formDefinition = !loading && context.serviceType.getTaskDefinitionSetting("customForm1", "formDefinition")
    const showShiftGuidanceTab = !isWorker && !!formDefinition

    const shiftGuidanceContent = showShiftGuidanceTab && <>
        <span className="pull-right">
            <CommandFormButton buttonLabel="edit guidance" modalTitle="edit shift guidance" color="primary">
                <CustomForm
                        serviceRecipientId={props.workerJob.getServiceRecipientId()}
                        taskName="customForm1"
                        taskNameGroup="customForm1"
                        taskHandle={null} />
            </CommandFormButton>
        </span>
        {getCustomFormWrapper(
                props.workerJob.getServiceRecipientId(),
                "customForm1", "customForm1",
                null, null, 'embedded',true, SimpleSchemaFormRenderer)}
    </>;

    const overviewContent =
        <Container className="entityForm">
            {!isWorker
            &&
            <>
                run: {workerJob.getName()}
            </>
            }
            {/* TODO add 'contractedHours' and 'jobDescription' and 'employeeNumber' as worker.textMap */}
            {/* TODO we could add getter/setters so the audits look better? */}
            {isWorker
            &&
            <>
                staff:&nbsp;
                <a href={`${applicationRootPath}nav/service-recipient/${workerJob.getServiceRecipientId()}`}
                   target="_blank">
                    {workerJob.getName()}
                </a>
                <br/><br/>
                <Container>
                    {context?.worker && <StaffOverviewPane
                        srId={workerJob.getServiceRecipientId()}
                        workerId={context.worker.workerId}
                    />}
                </Container>
            </>
            }
        </Container>;

    const tabs = new TabsBuilder()
        .addTab("overview", overviewContent, undefined, "fa-user")
        .addTab("shift guidance", shiftGuidanceContent, showShiftGuidanceTab, "fa-warning")
        .addTab("appointments", handleLazy(<AppointmentsView/>), undefined, "fa-calendar")
        .addTab(
            "patterns",
                () => <ServiceAgreementsView
            serviceRecipientId={workerJob.getServiceRecipientId()}
        />,
            workerJob.getServiceRecipientDiscriminator() === "bldg", "fa-calendar")
        .addTab(
            "audit history",
                <AuditHistory serviceRecipientId={workerJob.getServiceRecipientId()} sessionData={context?.serviceRecipient.features}/>,
            undefined, "fa-history")
        .build();

    const overview = <>
        <Box p={2}>
            {tabs}
        </Box>
    </>;

    // TODO showing availability is a property of the Worker (building resource type 'care run') not isWorker
    // TODO and using isWorker prevents buildings rota from having availability
    const showEditAvailability = isWorker;

    const more = <Button style={{marginLeft: "20px"}} onClick={() => {
        setShowExpandActionCard(!showExpandActionCard)
    }}>{!showExpandActionCard ? `more` : `less`}</Button>

    return <div>
        <Card elevation={0}>
            <CardHeader
                title={<>
                    {isWorker ? "Staff: " : "Run: "}
                    <a onClick={() => setShow(true)}>{ workerJob.getName()}</a>
                    {more}
                </>}
                // subheader="could summarise hours so far this month/week.."
            />
            <CardContent style={{paddingBottom: "0px", paddingTop: "0px"}}>
                {showExpandActionCard &&
                <Grid container direction="row">
                    {showAvailability && <>
                        <Grid item xs={3} sm={2}>Available:</Grid>
                        <Grid item xs={9} sm={10}>{workerJob.getAvailability().describe()}</Grid>
                    </>}
                    {workerJob.getSkills() && <>
                        <Grid item xs={3} sm={2}>Skills:</Grid>
                        <Grid item xs={9} sm={10}>{workerJob.getSkills()}</Grid>
                    </>}
                    <Grid item xs={3} sm={2}>

                        {!isWorker && <Button onClick={() => showCareRun(workerJob.getServiceRecipientId())}>edit run</Button>}
                    </Grid>
                    <Grid item xs={3} sm={2}>Provides:</Grid>
                    <Grid item xs={9} sm={10}>
                        {workerJob.getProvidesAttributes().length > 0
                            ? workerJob.getProvidesAttributes().join(", ")
                            : "-"}
                    </Grid>
                </Grid>
                }
            </CardContent>

            {<CardActions>
                <Grid item>
                    {<PrintResourceLineIcon
                        resource={workerJob}
                        title={workerJob.getRota().getDate().formatIso8601() + " " + workerJob.getName()}
                        CustomFormReadOnlyRenderer={SimpleSchemaFormRenderer}
                    />}
                </Grid>
                <Grid item xs /*gives flex-grow: 1 for spacing*//>
              {/*<Grid item>*/}
              {/*    {!isWorker && <Button*/}
              {/*      onClick={() => showReactInModal("for debugging", <PrintableRun resource={worker}/>)}*/}
              {/*    >print run (show as popup)</Button>}*/}
              {/*</Grid>*/}
            </CardActions>}

            {showEditAvailability && <CardActions>
                <Grid container direction="row">
                    <Grid item xs /*give flex-grow: 1 for spacing*//>
                    <Grid item>
                        <Button onClick={() => setEditAvailability("month")}>edit availability</Button>
                    </Grid>
                    <Grid item>
                        <Button onClick={() => setEditAvailability("today")}>edit availability (today)</Button>
                    </Grid>
                </Grid>
            </CardActions>}
        </Card>

        <EccoV3Modal
            title={workerJob.getName()}
            show={show}
            onCancel={() => setShow(false)}
            action="close"
        >
            {overview}
        </EccoV3Modal>

        {editAvailability && <EccoV3Modal
            title={"edit availability for " + workerJob.getName()}
            show={true}
            onCancel={() => {
                ReloadEvent.bus.fire(); // This triggers any useXxx hook to reload
                setEditAvailability(null);
            }}
            action="none"
            fullScreen={true}
        >
            <AvailabilityView calendarId={workerJob.getCalendarId()} mode={editAvailability}/>
        </EccoV3Modal>}

    </div>;
}

export default RotaWorkerJobView;
