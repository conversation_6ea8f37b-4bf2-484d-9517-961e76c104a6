import environment = require("../../environment");
import * as React from "react"
import {ClassAttributes, FC} from "react";

import {CommandQueue, CommandSource, EditEmergencyDetailsCommand, WorkUuidResolver} from "ecco-commands";
import {
    CommandSubform, getGuidanceUrlCallback, LoadingOrError,
    LoadSRWithEntitiesContext,
    possiblyModalForm,
    TaskSummary,
    textArea,
    textInput,
    useCurrentServiceRecipientWithEntities,
    useServiceRecipientWithEntities,
    useServiceRecipientAuditAccess,
    useServicesContext,
    withCommandForm,
    WithLatestCommands
} from "ecco-components";
import {<PERSON><PERSON>, Button} from "react-bootstrap";
import {ClientDetailAbstractSecretFields} from "ecco-dto/client-dto";
import {showInCommandForm} from "../../components/CommandForm";
import {ServiceType, SessionData, TaskNames} from "ecco-dto";
import {StringUtils} from "@eccosolutions/ecco-common";
import {Box, Grid} from '@eccosolutions/ecco-mui';
import {CustomForm} from "./CustomForm";
import {SourceAudit} from "ecco-dto/evidence/evidence-command-dto";

export function emergencyDetails(serviceRecipientId: number, onCompleted = () => {}, readOnly = false) {
    showInCommandForm(

        <LoadSRWithEntitiesContext srId={serviceRecipientId}>
            <EmergencyDetailsForm serviceRecipientId={serviceRecipientId}
                                    taskHandle={null}
                                    readOnly={readOnly}
                                    />
        </LoadSRWithEntitiesContext>,
        onCompleted
    );
}

export const EmergencyDetailsForm: FC<{serviceRecipientId: number, taskHandle: string, readOnly: boolean, source?: SourceAudit}> = props => {
    const {context, error} = useServiceRecipientWithEntities(props.serviceRecipientId);
    const {reload} = useCurrentServiceRecipientWithEntities()
    const {sessionData} = useServicesContext();
    useServiceRecipientAuditAccess(props.serviceRecipientId, TaskNames.emergencyDetails, props.source);

    if (!context || !sessionData) return <LoadingOrError error={error}/>;

    const guidanceFormDefinitionUuid = context.serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(TaskNames.waitingListCriteria, "guidanceFormDefinition");
    const guidanceCallback = guidanceFormDefinitionUuid ? getGuidanceUrlCallback(guidanceFormDefinitionUuid) : undefined;

    return withCommandForm(commandForm =>
        possiblyModalForm(
            "emergency details",
            true, true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm().then(reload),
            false, // TODO could emitChangesTo and see if there are any commands
            props.readOnly,
            <EmergencyDetailsSubform
                    serviceRecipientId={props.serviceRecipientId}
                    taskHandle={props.taskHandle}
                    readOnly={props.readOnly}
                    commandForm={commandForm}
            />,
            undefined,
            guidanceCallback
        )
    );
}

/**
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
const EmergencyDetailsSubform: FC<{serviceRecipientId, taskHandle, commandForm, readOnly}> = ({serviceRecipientId, taskHandle, commandForm, readOnly}) => {

    const workUuidResolver = new WorkUuidResolver();
    // printable may not be the same as readOnly, so leave as-is for now
    // NB readOnly is only ever provided as false (from TasksControl)
    const printable = false;

    const {sessionData} = useServicesContext();
    const {resolved} = useCurrentServiceRecipientWithEntities()

    const st = sessionData.getServiceTypeByServiceCategorisationId(resolved.serviceRecipient.serviceAllocationId);
    const emergencyFormDef = st.getTaskDefinitionSetting("emergencyDetails", "formDefinition");

    return <>
        <Box my={2}>
            <WithLatestCommands srId={serviceRecipientId}>
                <TaskSummary srId={serviceRecipientId} taskName={"emergencyDetails"}/>
            </WithLatestCommands>
        </Box>

        {/* printable.jsp (used by /grabSheet) is still referral based */}
        {resolved.serviceRecipient.prefix == "r" &&
                <div style={{textAlign: "center"}}>
                    {readOnly &&
                            <Grid container justify={"center"}>
                                <Grid item xs={4}>
                                    <Alert style={{padding: "0px"}} bsStyle={"warning"}>read-only</Alert>
                                </Grid>
                            </Grid>
                    }
                    {/*also see ClientReferralsPopup#openReferral*/}
                    <Button
                            onClick={() => window.open(`${environment.baseURI}service-recipients/${resolved.referral.serviceRecipientId}/grabSheet/`, "_blank")}
                            bsStyle="link">
                        grab sheet
                    </Button>
                </div>
        }

        {emergencyFormDef &&
            <CustomForm serviceRecipientId={resolved.serviceRecipient.serviceRecipientId}
                        taskName={TaskNames.emergencyDetails}
                        taskNameGroup={TaskNames.emergencyDetails}
                        page={printable ? 'printable' : 'embedded'}
                        taskHandle={taskHandle}
                        readOnly={printable}
                        workUuidResolver={workUuidResolver}
            />
        }

        <Box paddingBottom={"50px"}>
            &nbsp;
        </Box>

        <EmergencyDetails
            srId={serviceRecipientId}
            taskHandle={taskHandle} // NB task could be filtered from context.workflowActiveRecord.getTasks() filter by name (see TaskList.tsx for the source)
            client={resolved.client || resolved.worker}
            sessionData={sessionData}
            serviceType={resolved.serviceType}
            commandForm={commandForm}
            readOnly={readOnly}
            displayOnlyPopulatedFields={!!emergencyFormDef}
        />
    </>
}

interface Props extends ClassAttributes<EmergencyDetails> {
    client: ClientDetailAbstractSecretFields;
    srId: number;
    taskHandle: string;
    sessionData: SessionData;
    serviceType: ServiceType;
    displayOnlyPopulatedFields?: boolean;
}
interface State {
    descriptionDetails: string;
    communicationNeeds: string;
    emergencyKeyword: string;
    emergencyDetails: string;
    medicationDetails: string;
    risksAndConcerns: string;
    doctorDetails: string;
    dentistDetails: string;
}

/** EmergencyDetails editor fields as a command subform supporting emitChanges */
export class EmergencyDetails extends CommandSubform<Props, State> implements CommandSource {

    constructor(props) {
        super(props);
        let client = this.props.client;
        this.state = {
            descriptionDetails: client.description,
            communicationNeeds: client.communicationNeeds,
            emergencyKeyword: client.emergencyKeyWord,
            emergencyDetails: client.emergencyDetails,
            medicationDetails: client.medicationDetails,
            risksAndConcerns: client.risksAndConcerns,
            doctorDetails: client.doctorDetails,
            dentistDetails: client.dentistDetails
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const cmd = new EditEmergencyDetailsCommand(this.props.srId, this.props.taskHandle)
            .changeCommunicationNeeds(this.props.client.communicationNeeds, this.state.communicationNeeds)
            .changeEmergencyDetails(this.props.client.emergencyDetails, this.state.emergencyDetails)
            .changeEmergencyKeyword(this.props.client.emergencyKeyWord, this.state.emergencyKeyword)
            .changeDescriptionDetails(this.props.client.description, this.state.descriptionDetails)
            .changeDentistDetails(this.props.client.dentistDetails, this.state.dentistDetails)
            .changeDoctorDetails(this.props.client.doctorDetails, this.state.doctorDetails)
            .changeMedicationDetails(this.props.client.medicationDetails, this.state.medicationDetails)
            .changeRisksAndConcerns(this.props.client.risksAndConcerns, this.state.risksAndConcerns);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    private getFieldsToShow() {

        // assume all fields
        let fieldsToShow: (keyof State)[] = ["descriptionDetails", "communicationNeeds", "risksAndConcerns", "emergencyKeyword", "emergencyDetails", "medicationDetails", "doctorDetails", "dentistDetails"];
        const fieldsCsv = this.props.serviceType.getTaskDefinitionSetting("emergencyDetails",  "formDefinitionFields");
        if (fieldsCsv) {
            fieldsToShow = StringUtils.csvToArray(fieldsCsv) as (keyof State)[];
        }
        return fieldsToShow;
    }

    render() {
        const messages = this.props.sessionData.getMessages()

        const stateSetter = state => this.setState(state);

        const fieldsToShow = this.getFieldsToShow();

        const allowedField = (field: keyof State) => {
            return fieldsToShow.indexOf(field) > -1;
        };

        const messageLookup = (key: string) => {
            return messages[key] || "[" + key + "]";
        };

        const displayField = (field: keyof State) => {
            return this.props.displayOnlyPopulatedFields == true
                    ? this.state[field]
                    : true;
        };

        const showInputTextArea = (propertyKey: keyof State, messageKey: string) =>
             allowedField(propertyKey) && displayField(propertyKey) && <Grid item xs={12}>
                {textArea(propertyKey, messageLookup(messageKey), stateSetter, this.state, undefined, this.props.readOnly)}
          </Grid>
        const showInputTextInput = (propertyKey: keyof State, messageKey: string) =>
                allowedField(propertyKey) && displayField(propertyKey) && <Grid item xs={12}>
                    {textInput(propertyKey, messageLookup(messageKey), stateSetter, this.state, undefined, this.props.readOnly)}
                </Grid>
        // NB we dont actually show the client details on a grab sheet?!?! except at the top - gender etc, but nhs/ni??
        //firstLanguageId, ethnicOriginId, nationalityId, religionId, disabilityId, genderAtBirthId, sexualOrientationId, maritalStatusId, ni, housingBenefit, nhs, clientCompletedDate
        return (
            <Grid container spacing={2}>
                {showInputTextArea("descriptionDetails", "form.emergencyDetails.description")}
                {showInputTextArea("communicationNeeds", "form.emergencyDetails.communicationNeeds")}
                {showInputTextArea("risksAndConcerns", "form.emergencyDetails.risksAndConcerns")}
                {showInputTextInput("emergencyKeyword", "form.emergencyDetails.communicationKeyword")}
                {showInputTextArea("emergencyDetails", "form.emergencyDetails.emergencyDetails")}
                {showInputTextArea("medicationDetails", "form.emergencyDetails.medicationDetails")}
                {showInputTextArea("doctorDetails", "form.emergencyDetails.doctorsDetails")}
                {showInputTextArea("dentistDetails", "form.emergencyDetails.dentistDetails")}
                {/* client details or on hbs file */}
            </Grid>
        );
    }
}
