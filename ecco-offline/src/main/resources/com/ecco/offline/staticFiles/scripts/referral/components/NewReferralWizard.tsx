import {ResizeEvent, StringToStringMap} from "@eccosolutions/ecco-common";
import {applicationRootPath} from "application-properties";
import {CommandDtoSentEvent, CommandQueue, CreateReferralCommand, ReferralDetailsCommand} from "ecco-commands";
import {
    apiClient,
    AsyncSessionData,
    CommandForm,
    showReactInModal,
    update,
    UpdateSpec,
    withSessionData
} from "ecco-components";
import * as dto from "ecco-dto";
import {
    ReferralAjaxRepository,
    ReferralDto,
    ReferralSummaryDto,
    SourceType,
    TaskNames
} from "ecco-dto";
import * as scDto from "ecco-dto/service-config-dto";
import {Button, ButtonToolbar, Nav, NavItem} from "react-bootstrap";
import {unmountComponentAtNode} from "react-dom";
import {AssociatedContactWizard} from "../../contacts/AssociatedContactWizard";
import SessionDataService from "../../feature-config/SessionDataService";
import {
    BaseServiceRecipientWizard,
    FileTypeName,
    SRProps,
    SRState as BaseState,
    Step
} from "./BaseServiceRecipientWizard";
import {ReferralDetailsSubform} from "./ReferralDetailsForm";
import {ReferralProject, ReferralProjectEditor} from "./ReferralProject";
import {ConsentOptions, DataProtectionOptions, SignedAgreement} from "./signedAgreements";
import {showErrorAsAlert} from "ecco-offline-data";
import * as React from "react"
import ReferralRegion = require("./ReferralRegion");
import {ChooseService} from "../../components/ChooseService";
import ServiceDto = scDto.ServiceDto;
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";
import {CustomSubform} from "./CustomForm";
import {ReferralSourceEditor} from "./ReferralSource";

const STATUS_LOADING_REFERRAL = "LOADING_REFERRAL";

interface Props extends SRProps {
    /** Callback when referral is selected */
    onSelected?: (referral: ReferralSummaryDto) => void;
    clientId: number;
    /** Specify this if you want to pre-select which service they are being added to */
    serviceId?: number;
}

const stepsNonDisplay: Step[] = ["autoStart"];
type ReferrerDetails = {
    referrerIndividualId?: number;
    referrerAgencyId?: number;
}

const compareNames = (a: {name: string}, b: {name: string}) => a.name.localeCompare(b.name);

interface ReferralDtoTransient extends ReferralDto {
    serviceIdTransient: number;
    projectIdTransient: number;
}

interface State extends BaseState {
    sourceType: SourceType;
    sourceTypeHolder: {
        professional: ReferrerDetails;
        individual: ReferrerDetails;
        selfReferral: ReferrerDetails;
    };
    projects: scDto.ProjectDto[];
    referral: Partial<ReferralDtoTransient>;
    regionNameTransient: string;
    /** True if user has permission to open referrals on the service that has been selected */
    referralOpenPermission: boolean;
}

export class NewReferralWizard extends BaseServiceRecipientWizard<Props, State> {

    private referralDetailsComponent: ReferralDetailsSubform;
    private referralDetailsCommandQueue = new CommandQueue();
    private dataProtectionFormComponent: CustomSubform;
    private dataProtectionCommandQueue = new CommandQueue();
    private consentFormComponent: CustomSubform;
    private consentCommandQueue = new CommandQueue();
    // instead of a sourceCommandQueue, we use this property to indicate a save is required
    private sourceNeedsSaving = false;
    private projectCommandQueue = new CommandQueue();

    // see https://stackoverflow.com/questions/37282159/default-property-value-in-react-component-using-typescript
    public static defaultProps: Partial<Props> = {
        subFormsAsModal: true,
        allowInboundServices: false
    };

    public static popup(clientId: number,
                        onSelected: (referral: ReferralSummaryDto) => void,
                        allowInboundServices = false,
                        fileTypeName: FileTypeName = "referral",
                        serviceId?: number
    ) {

        const modalElm = document.createElement("div");
        const onCompleted = () => {unmountComponentAtNode(modalElm)};

        const reactElem = <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
            {withSessionData(sessionData =>
                <NewReferralWizard
                    sessionData={sessionData}
                    subFormsAsModal={true}
                    allowInboundServices={allowInboundServices}
                    clientId={clientId}
                    serviceId={serviceId}
                    fileTypeName={fileTypeName}
                    onSelected={referral => {
                        console.debug("referral selected: %o", referral);
                        onCompleted();
                        window.onbeforeunload = null;
                        onSelected(referral);
                    }
                    }/>
            )}
        </AsyncSessionData>;

        showReactInModal(`new ${fileTypeName}`, reactElem,
            {
                onAction: onCompleted,
                action: "cancel",
                mountPoint: modalElm
            });
    }

    private repository = new ReferralAjaxRepository(apiClient);

    constructor(props: Props) {
        super(props);

        const serviceDto = props.serviceId && props.sessionData.getService(props.serviceId);

        const referral: Partial<ReferralDtoTransient> = {};
        referral.clientId = props.clientId;
        referral.serviceIdTransient = props.serviceId;
        const steps = this.buildSteps(serviceDto);
        this.state = {
            sourceType: 'professional',
            sourceTypeHolder: {
                professional: {},
                individual: {},
                selfReferral: {}
            },
            regionNameTransient: null,
            projects: [],
            referral: referral,
            referralOpenPermission: false,
            errors: NewReferralWizard.validate(referral, 'professional'),
            steps: steps,
            stepSelected: props.serviceId ? this.nextStep(steps, 'service') : 'service'
        };
    }

    public componentDidMount() {
        window.onbeforeunload = function() {
            return "Your data will be lost";
        };
        // see DEV-747
        ResizeEvent.bus.fire(new ResizeEvent());
    }

    public componentWillUnmount() {
        window.onbeforeunload = null;
    }

    /**
     * When 'done' - flush work and decide action
     */
    private handleDoneClick = () => {
        // without a serviceRecipientId we haven't saved the referral before, so save
        if (!this.state.referral.serviceRecipientId) {
            this.saveReferralAndLeave();
            // if we have saved before then flush the pending commands
            // details of referral which (at the top level) required a save first so its there for its context loading
        } else {
            const pendingQueue = new CommandQueue();
            pendingQueue.addQueue(this.referralDetailsCommandQueue);
            pendingQueue.addQueue(this.dataProtectionCommandQueue);
            pendingQueue.addQueue(this.consentCommandQueue);
            // ReferralSource popups up by default, and expects to be wrapped in a CommandForm for the queue
            if (this.sourceNeedsSaving) {
                var sourceCommandQueue = new CommandQueue();
                ReferralSourceEditor.emitChanges(sourceCommandQueue, this.state.referral.serviceRecipientId, null,
                        this.state.sourceType, false,
                        null, this.state.referral.referrerAgencyId,
                        null, this.state.referral.referrerIndividualId);
                pendingQueue.addQueue(sourceCommandQueue);
            }
            pendingQueue.addQueue(this.projectCommandQueue);
            pendingQueue.flushCommands()
                    .catch(showErrorAsAlert)
                    .then(() => this.handleOnSelectedAction(this.state.referral as ReferralSummaryDto));
        }
    };

    /**
     * When 'done' wth the wizard
     */
    private handleOnSelectedAction(referral: ReferralSummaryDto) {
        if (this.props.onSelected) {
            this.props.onSelected(referral);
        } else {
            if (!this.state.referralOpenPermission) {
                this.setState({
                    stepSelected: 'close'
                });
            } else {
                // redirect
                window.onbeforeunload = null;
                // also see ClientReferralsPopup#openReferral
                window.location.href = `${applicationRootPath}nav/referrals/${referral.referralId}/`;
            }
        }
    }

    /**
     * Provide a 'next' button if the page doesn't have a way to progress
     * NB NextButtonElement = null for pages such as agreements which have an 'accept' so don't need 'next'.
     */
    private handleNextClick = () => {
        // first check we are showing referralDetailsComponent, and if so, save its work to the queue before it goes and gets set to null
        if (this.referralDetailsComponent) {
            this.referralDetailsCommandQueue.clear();
            this.referralDetailsComponent.emitChangesTo(this.referralDetailsCommandQueue);
        }

        this.saveReferralMaybe().then(r => {
            this.setState({
                stepSelected: this.nextStep(),
                referral: r}
            );
        });
    };

    private handleReferralEntityChange<T>(updater: (entity: T) => UpdateSpec<ReferralDtoTransient>, entity: T) {
        const referral = update(this.state.referral, updater(entity));
        const errors = NewReferralWizard.validate(referral, this.state.sourceType);

        this.saveReferralMaybe(referral).then(r => {
            this.setState({
                referral: r,
                errors: errors
            });
        });
    }

    private handleReferralEntityNextChange<T>(updater: (entity: T) => UpdateSpec<ReferralDtoTransient>, entity: T) {
        const referral = update(this.state.referral, updater(entity));
        const errors = NewReferralWizard.validate(referral, this.state.sourceType);

        let stepSelected = this.isStepValid(errors) && this.nextStep() || this.state.stepSelected;

        this.saveReferralMaybe(referral).then(r => {
            this.setState({
                referral: r,
                errors: errors,
                stepSelected: stepSelected
            });
        });
    }

    private handleServiceChange = (service: ServiceDto, hasAccessPermission: boolean) => {
        if (service) {
            const steps = this.buildSteps(service);
            let referral: Partial<ReferralDtoTransient>;

            // instead of checking service's projects.length, we simply check the wizard (as it could be projects aren't part of the wizard anyway - causing serviceAllocationId to be null)
            // but it could also be the other way around... we could have config in the pathway with none or one project - so we need to do both, otherwise no serviceAllocationId is set
            // NB need to capture a single project otherwise the projects wizard step is skipped leaving saveReferralMaybe to fail on the next page (since svcCat is not set) - see buildSteps 'if (projects.length > 0)'
            const svcProjectInWizard = this.props.sessionData.getServiceTypeByServiceIdHack(service.id).getWizardTasks().indexOf(TaskNames.project) != -1;
            const projects = this.props.sessionData.getServiceCategorisationProjects(service.id, true) || [];
            // if not in wizard or no projects
            const useSvcCatNow = !svcProjectInWizard || projects.length <= 1;

            if (useSvcCatNow) {
                // if we don't have a project - just try null
                // if we have one project, use that
                const projectOrNull = projects.length == 1 ? projects[0].id : null;
                const svcCat = this.props.sessionData.getServiceCategorisationByIds(service.id, projectOrNull);
                referral = update(this.state.referral, {serviceIdTransient: {$set: service.id}, projectIdTransient: {$set: projectOrNull}, serviceAllocationId: {$set: svcCat.id}});
            } else {
                referral = update(this.state.referral, {serviceIdTransient: {$set: service.id}, projectIdTransient: {$set: null}, serviceAllocationId: {$set: null}});
            }
            const errors = NewReferralWizard.validate(referral, this.state.sourceType);
            const stepSelected = this.isStepValid(errors) && this.nextStep(steps) || this.state.stepSelected;

            if (useSvcCatNow) {
                this.saveReferralMaybe(referral).then(r => {
                    this.setState({
                          projects: projects,
                          referral: r,
                          referralOpenPermission: hasAccessPermission,
                          errors: errors,
                          steps: steps,
                          stepSelected: stepSelected
                      });
                });
            } else {
                this.setState({
                      projects: projects,
                      referral: referral,
                      referralOpenPermission: hasAccessPermission,
                      errors: errors,
                      steps: steps,
                      stepSelected: stepSelected
                  });
            }
        }
    };

    private handleSourceTypeChange = (sourceType: SourceType) => {
        const sourceTypeHolder = update(this.state.sourceTypeHolder, {
            [this.state.sourceType]: {
                referrerIndividualId: {$set: this.state.referral.referrerIndividualId},
                referrerAgencyId: {$set: this.state.referral.referrerAgencyId}
            }
        });

        const referral = update(this.state.referral, {
            selfReferral: {$set: sourceType == 'selfReferral'},
            referrerIndividualId: {$set: sourceTypeHolder[sourceType].referrerIndividualId},
            referrerAgencyId: {$set: sourceTypeHolder[sourceType].referrerAgencyId}
        });

        const errors = NewReferralWizard.validate(referral, sourceType);

        this.saveReferralMaybe(referral).then(r => {
            this.setState({
                sourceType: sourceType,
                sourceTypeHolder: sourceTypeHolder,
                referral: r,
                errors: errors
            }, );
        });
    };

    private saveReferralMaybe = (referralIn?: Partial<ReferralDto>) => {

        // get next step
        // but if we're in the process of changing service, re-get the steps
        let nextStep = this.nextStep();
        if (!this.state.referral.serviceAllocationId) {
            // referralIn may not exist if we haven't yet chosen a project
            const svcId = referralIn ? this.props.sessionData.getServiceCategorisation(referralIn.serviceAllocationId).serviceId : this.state.referral.serviceIdTransient;
            const serviceDto = this.props.sessionData.getService(svcId);
            const steps = this.buildSteps(serviceDto);
            nextStep = this.nextStep(steps);
        }

        // if we are something requiring id's to save, then save the referral so far, and update the state to render the form
        const ref = referralIn ? referralIn : this.state.referral;

        return (nextStep == 'source' || nextStep == 'referralDetails' ||
                nextStep == 'protection' || nextStep == 'consent')
            ? this.saveReferralAndStay(ref as ReferralDtoTransient)
            : Promise.resolve(ref);
    }

    private saveReferralAndLeave(referral?: Partial<ReferralDto>) {
        referral = referral || this.state.referral;

        return this.saveReferralToId(referral as ReferralDtoTransient).then((referralId) => {
            this.setState({status: STATUS_LOADING_REFERRAL});
            return this.repository.findOneReferralSummaryAsSummary(referralId).then((referralSummary) => {
                this.handleOnSelectedAction(referralSummary);
            });
        });
    }

    private saveReferralToId(referral: ReferralDtoTransient): Promise<number> {

        // if we are providing a props.serviceId (eg relationships) then we can be done instantly in the wizard
        // so we need to move the serviceIdTransient to a serviceAllocationId
        // this could be done in the constructor, perhaps
        if (!referral.serviceAllocationId) {
            // we don't have a project - so just find null where we can
            const svcCat = this.props.sessionData.getServiceCategorisationByIds(referral.serviceIdTransient, null);
            referral.serviceAllocationId = svcCat.id;
        }

        // Return promise that resolves with the referralId when
        // the command has been sent
        return new Promise<number>((resolve, reject) => {

            const handler = (event: CommandDtoSentEvent) => {
                if (event.command.toCommandDto().commandName == CreateReferralCommand.discriminator) {
                    const referralId = ReferralAjaxRepository.createReferralIdExtractor(event.returnDto);
                    resolve(referralId);
                }
            };

            const q = new CommandQueue();
            q.addCommandDtoSentEventHandler(handler);
            const cmd = new CreateReferralCommand(referral);
            q.addCommand(cmd);
            q.flushCommands().catch(reject); // Effectively this promise is resolved via handler calling resolve
        });
    }

    private saveReferralAndStay(referralIn: ReferralDtoTransient): Promise<Partial<ReferralDto>> {
        return referralIn.serviceRecipientId
            ? Promise.resolve(referralIn)
            : this.saveReferralToId(referralIn as ReferralDtoTransient).then((referralId) => {
                return this.repository.findServiceRecipientIdFromReferralId(referralId).then((serviceRecipientId) => {
                    const setIdUpdater = (referralId: number, serviceRecipientId: number) => ({referralId: {$set: referralId}, serviceRecipientId: {$set: serviceRecipientId}});
                    return update(referralIn, setIdUpdater(referralId, serviceRecipientId));
                })
            });
    }

    public static validate(referral: Partial<ReferralDtoTransient>, sourceType: SourceType): StringToStringMap {
        // const errors: PartialTo<Client, string> = {};
        const errors: StringToStringMap = NewReferralWizard.validateSource(referral, sourceType);
        if (!referral.serviceIdTransient) errors['serviceIdTransient'] = 'required';
        if (!referral.clientId) errors['clientId'] = 'required';
        return errors;
    }
    public static validateSource(referral: Partial<ReferralDto>, sourceType: SourceType): StringToStringMap {
        // const errors: PartialTo<Client, string> = {};
        const errors: StringToStringMap = {};
        if (sourceType == 'professional') {
            if (!referral.referrerAgencyId) errors['referrerAgencyId'] = 'required';
            //if (!referral.referrerIndividualId) errors['referrerIndividualId'] = 'required';
        } else if (sourceType == 'individual') {
            if (!referral.referrerIndividualId) errors['referrerIndividualId'] = 'required';
        } else if (sourceType != "selfReferral") {
            // just indicate nothing is chosen, and that is invalid
            errors['something'] = 'required';
        }
        return errors;
    }

    private getRegions(): string[] {
        const regionIndex = {};
        this.state.projects.forEach((project: scDto.ProjectDto) => {
            if (project.regionName) {
                regionIndex[project.regionName] = null;
            }
        });
        return Object.keys(regionIndex);
    }

    private getProjects(): scDto.ProjectDto[] {
        // const svcCat = this.props.sessionData.getServiceCategorisation(this.state.referral.serviceAllocationId);
        // let region = this.props.sessionData.getProject(svcCat.projectId).regionNameTransient;
        let region = this.state.regionNameTransient;
        if (!region) return this.state.projects
            .filter(p => p.name != null).sort(compareNames);

        const projects = new Array<scDto.ProjectDto>();
        this.state.projects.forEach((project: scDto.ProjectDto) => {
            if (project.regionName == region) {
                projects.push(project);
            }
        });
        return projects.sort(compareNames);
    }

    private findProject(projectId: number | null) {
        return projectId
            ? this.getProjects().filter(project => project.id == projectId)[0]
            : null;
    }

    render() {
        let StepElement: React.ReactElement;
        let StepHeaderElement: React.ReactElement;
        let NextButtonElement: React.ReactElement;

        // show 'next' or 'done'
        if (this.hasNext()) {
            NextButtonElement = (
                <Button
                    onClick={this.handleNextClick}
                    disabled={!this.isStepValid()}
                    bsStyle="primary">
                    next
                </Button>
            );
        } else {
            NextButtonElement = (
                <Button
                    onClick={this.handleDoneClick}
                    disabled={!this.isStepValid() || this.state.status == STATUS_LOADING_REFERRAL}
                    bsStyle="primary">
                    {this.state.status == STATUS_LOADING_REFERRAL && `opening ${this.props.fileTypeName} file...` || 'save'}
                </Button>
            );
        }

        const dummyCommandForm = new CommandForm({onCancel: () => {}, onFinished: () => {}});
        const serviceType = this.state.referral.serviceIdTransient && this.props.sessionData.getServiceTypeByServiceIdHack(this.state.referral.serviceIdTransient);

        switch (this.state.stepSelected) {
            case 'autoStart':
                this.handleReferralEntityNextChange((autoStart: boolean) => ({callAcceptOnService: {$set: autoStart}}), true);
                break;
            case 'service':
                StepHeaderElement = (
                    <h3>service</h3>
                );
                StepElement = (
                    <ChooseService
                        allowInboundServices={this.props.allowInboundServices}
                        onChange={this.handleServiceChange} />
                );
                NextButtonElement = null;
                break;
            case 'region':
                StepHeaderElement = (
                    <h3>region</h3>
                );
                StepElement = (
                    <ReferralRegion
                        regions={this.getRegions()}
                        onChange={region => {
                            this.setState({
                                  regionNameTransient: region,
                                  // this got introduced?
                                  stepSelected: this.nextStep()
                              });
                        }} />
                );
                NextButtonElement = null;
                break;
            case 'project':
                StepHeaderElement = (
                    <h3>project</h3>
                );
                StepElement = (
                    <ReferralProject
                        sessionData={this.props.sessionData}
                        initialProjectId={null}
                        projects={this.getProjects()}
                        currProject={this.findProject(this.state.referral.projectIdTransient)}
                        onProjectChange={projectId => {
                            // if we're already saved, set up a commandQueue on 'done'
                            if (this.state.referral.serviceRecipientId) {
                                this.projectCommandQueue.clear();
                                ReferralProjectEditor.emitCommand(this.projectCommandQueue, this.state.referral.serviceRecipientId, null, null, projectId);
                            }
                            const svcRec = this.props.sessionData.getServiceCategorisationByIds(this.state.referral.serviceIdTransient, projectId);
                            this.handleReferralEntityNextChange(e => ({projectIdTransient: {$set: projectId}, serviceAllocationId: {$set: e}}), svcRec.id);
                        }
                    }/>
                );
                NextButtonElement = null;
                break;
            case 'source':
                StepHeaderElement = (
                    <h3>source</h3>
                );


                StepElement = (
                    <AssociatedContactWizard
                        sessionData={this.props.sessionData}
                        serviceRecipientId={this.state.referral.serviceRecipientId}
                        title={this.props.fileTypeName == "referral" ? "source of referral" : "source"}
                        showSelf={true}
                        sourceType={this.state.sourceType}
                        agencyId={this.state.referral.referrerAgencyId}
                        professionalId={this.state.referral.referrerAgencyId && this.state.referral.referrerIndividualId}
                        individualId={this.state.referral.referrerIndividualId}
                        onSourceTypeChange={type => {
                            this.sourceNeedsSaving = true;
                            this.handleSourceTypeChange(type)
                        }}
                        onAgencyProfessionalChange={(agency, individual) => {
                            this.sourceNeedsSaving = true;
                            this.handleReferralEntityChange(dto.setReferralReferrerUpdater, individual);
                            if (!individual) {
                                this.handleReferralEntityChange(dto.setReferralAgencyUpdater, agency)
                            }
                        }}
                        onIndividualChange={individual => {
                            this.sourceNeedsSaving = true;
                            this.handleReferralEntityChange(dto.setReferralReferrerUpdater, individual)
                        }} />
                );
                break;
            case 'referralDetails':
                StepHeaderElement = (
                    <h3>details</h3>
                );

                StepElement = (
                    <div>
                        {/* we need to save the referral first to get the srId to save the referral details */}
                        {/* TODO provide a taskHandle - for now mimic other usage here of AUTO */}
                        {this.state.referral.serviceRecipientId
                            ? <ServicesContextProvider>
                                <ReferralDetailsSubform
                                        ref={c => this.referralDetailsComponent = c}
                                        sessionData={this.props.sessionData}
                                        dto={this.state.referral as ReferralSummaryDto}
                                        taskHandle={"AUTO"}
                                        readOnly={false}
                                        commandForm={dummyCommandForm}
                                        type={this.state.referral.prefix}
                                />
                              </ServicesContextProvider>
                            : null
                        }
                    </div>
                );
                break;
            case 'protection':
                StepHeaderElement = (
                    <h3>data protection</h3>
                );
                const splitAgreementDP = serviceType && serviceType.taskDefinitionSettingHasFlag(TaskNames.dataProtection,  "splitAgreement", "y");
                // NB we can't use signed agreements with custom forms as getAgreement expects an srId which we don't have to save against yet
                StepElement = (
                    <>
                    <CustomSubform serviceRecipientId={this.state.referral.serviceRecipientId}
                                   readOnly={false}
                                   historyLink={false}
                                   printableLink={false}
                                   taskName={"dataProtection"}
                                   taskNameGroup={"dataProtection"}
                                   taskHandle={"AUTO"}
                                   ref={r => this.dataProtectionFormComponent = r}
                                   commandForm={dummyCommandForm}
                    />
                    <br/>
                    <SignedAgreement
                        options={new DataProtectionOptions(this.state.referral as ReferralDto, "AUTO", splitAgreementDP)}
                        onChange={agreement => {
                            this.dataProtectionCommandQueue.clear();
                            agreement.emitChangesTo(this.dataProtectionCommandQueue);
                            this.dataProtectionFormComponent.emitChangesTo(this.dataProtectionCommandQueue);
                            this.setState({stepSelected: this.nextStep()});
                        }}
                        commandForm={dummyCommandForm}
                        readOnly={false}
                    />
                    </>
                );
                NextButtonElement = null;
                break;
            case 'consent':
                StepHeaderElement = (
                    <h3>consent</h3>
                );
                // NB we can't use signed agreements with custom forms as getAgreement expects an srId which we don't have to save against yet
                const splitAgreementConsent = serviceType && serviceType.taskDefinitionSettingHasFlag(TaskNames.consent,  "splitAgreement", "y");
                StepElement = (
                    <>
                    <CustomSubform serviceRecipientId={this.state.referral.serviceRecipientId}
                                   readOnly={false}
                                   historyLink={false}
                                   printableLink={false}
                                   taskName={"consent"}
                                   taskNameGroup={"consent"}
                                   taskHandle={"AUTO"}
                                   ref={r => this.consentFormComponent = r}
                                   commandForm={dummyCommandForm}
                    />
                    <br/>
                    <SignedAgreement
                        options={new ConsentOptions(this.state.referral as ReferralDto, "AUTO", splitAgreementConsent)}
                        onChange={agreement => {
                            this.consentCommandQueue.clear();
                            agreement.emitChangesTo(this.consentCommandQueue);
                            this.consentFormComponent.emitChangesTo(this.consentCommandQueue);
                            this.setState({stepSelected: this.nextStep()});
                        }}
                        commandForm={dummyCommandForm}
                        readOnly={false}
                    />
                    </>
                );
                NextButtonElement = null;
                break;
            case 'complete':
                StepHeaderElement = (
                    <h3>data capture completed</h3>
                );
                StepElement = (
                    <div className='row'>
                        <div className='col-xs-12'>
                            <div className='page-header'>
                                <h3>new {this.props.fileTypeName} is ready to be saved</h3>
                            </div>
                        </div>
                    </div>
                );
                break;
            case 'close':
                StepHeaderElement = (
                    <h3>thank you</h3>
                );
                StepElement = (
                    <div className='row'>
                        <div className='col-xs-12'>
                            <div className='page-header'>
                                <p>your request has been saved</p>
                            </div>
                        </div>
                    </div>
                );
                break;
        }

        return (
            <div>
                <div className='row ecco-rounded'>
                    <div className='col-xs-12 text-center'>
                        <Nav
                            className='nav-steps'
                            activeKey={this.state.stepSelected}
                            onSelect={this.handleStepClick}>
                            {this.state.steps.filter(s => stepsNonDisplay.indexOf(s) == -1)
                                .map( (step, index) => (
                                /* TODO use messages for each technical NavItem 'step' */
                                <NavItem
                                    key={index}
                                    eventKey={step}
                                    disabled={!this.isEnabledStep(step)}>
                                    {step}
                                </NavItem>
                            ))}
                        </Nav>
                    </div>
                </div>
                {StepElement}
                <div className='pull-right'>
                    <ButtonToolbar>
                        {NextButtonElement}
                    </ButtonToolbar>
                </div>
            </div>
        );
    }

}
