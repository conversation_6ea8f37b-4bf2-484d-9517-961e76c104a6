import {EccoDate, EccoTime, ReloadEvent, ResizeEvent, StringToObjectMap, StringUtils} from "@eccosolutions/ecco-common";
import {
    AnchorLeft,
    button,
    CreateAppointmentModal,
    createCheckBox,
    DomElementContainer,
    EccoTheme,
    link,
    LoadingSpinner,
    SelectBuilding,
    ToggleMenuItem,
    useApiClientProgress,
    useAppBarContext,
    useAppBarOptions,
    useBuildings,
    useCounter,
    useDebounce, useEventHandler,
    useInterval,
    useQuery,
    useResultNotification,
    useRota,
    useRotaLiveLoneWorkerStatus,
    useServicesContext,
    useTimeout
} from "ecco-components";
import * as React from "react";
import {ChangeEvent, FC, Reducer, useEffect, useMemo, useReducer, useState} from "react";
import {useHistory, useLocation} from "react-router";
import {BuildingViewPopup} from "./components/BuildingView";
import {RotaDayView} from "./components/RotaDayView";
import {
    Box,
    Button,
    ButtonGroup,
    Card,
    CardActions,
    CardContent,
    Checkbox,
    FormControlLabel,
    Grid,
    MenuItem,
    Paper
} from '@eccosolutions/ecco-mui';
import {DateNavEccoDate, FabMenu} from "@eccosolutions/ecco-mui-controls"
import type {History} from "history";
import RotaWorkerJobView from "./components/RotaWorkerJobView";
import {RotaWeekView} from "./weekView/RotaWeekControl";
import {Activity, DemandResource} from 'ecco-rota';
import {ActivityView} from './components/ActivityView';
import {showCareRun} from "../buildings/components/BuildingForm";
import {EventSnapshotDto} from "ecco-dto";
import {ScheduleEvent} from "ecco-components";

/*
<div id="content">
    <div id="availability-demandType" data-demandType="${demandType}"></div> <%-- NB so availability knows what to load in ddl --%>
    <div id="rota" data-date="<c:out value="${dateToShow}"/>"
        data-demandedResourceFilter="<c:out value="${demandedResourceFilter}"/>"
        data-serviceRecipientFilter="<c:out value="${serviceRecipientFilter}"/>"
        ${empty param.weekView ? "" : 'data-weekview' }></div>
*/

interface Props {
    resourceFilter: string
    demandFilter: string
    type: "day" | "week"
}

function navToRota(history: History, runOrRota: string, type: "day" | "week", buildingId: number, svccatIds: number[], date: EccoDate) {
    if (buildingId) {
        history.push({
            pathname: `/nav/w/welcome/buildings/${buildingId}/${runOrRota}/${type}`,
            search: date ? "date=" + date.formatIso8601() : undefined
        });
    } else if (svccatIds) {
        const resourceFilter = runOrRota == "rota" ? "workers:all" : "careruns:all";
        history.push({
            // NB as handled by WelcomeAppBar's Route
            pathname: `/nav/w/welcome/${runOrRota}/${type}/${resourceFilter}/svccats:${svccatIds.join(",")}`,
            search: date ? "date=" + date.formatIso8601() : undefined
        });
    } else {
        // wasn't referrals, but referrals:all wanted
        history.push({
            pathname: `/nav/w/welcome/rota/all/${type}`,
            search: date ? "date=" + date.formatIso8601() : undefined
        });
    }
}

const RotaMenu: FC<{
    date: EccoDate,
    demandFilter: string,
    resourceFilter: string
}> = ({date, demandFilter, resourceFilter}) => {
    const [open, setOpen] = useState(false);
    const [showAdHoc, setShowAdHoc] = useState(false);

    return <span>
        <FabMenu open={open} setOpen={setOpen}>
            <MenuItem onClick={() => {
                setOpen(false);
                setShowAdHoc(true);
            }}>
                new ad-hoc appointment
            </MenuItem>
        </FabMenu>
        {showAdHoc && <EccoTheme prefix="rv">
            <CreateAppointmentModal
                serviceId={null}
                demandFilter={demandFilter}
                resourceFilter={resourceFilter}
                date={date}
                setShow={setShowAdHoc}
            />
        </EccoTheme>}
    </span>;
};

interface ItemViewState {
    activity?: Activity
    resource?: DemandResource
    showAvailability?: boolean
}
type ItemViewAction = ItemViewState // we just want

const itemViewReducer: Reducer<ItemViewState, ItemViewAction> = (prevState, action) => {
    if (action.activity) {
        // FIXME: Will need rota for replacing activity with updated one from rota
        return {activity: action.activity};
    }
    if (action.resource != prevState.resource || action.showAvailability != prevState.showAvailability) {
        return action;
    }
    return {};
};


const CareRunEditFromSvcRecIds: FC<{svccatIds: number[]}> = ({svccatIds}) => {
    const {sessionData} = useServicesContext();
    const userSvcRecs = sessionData.getRestrictedServiceCategorisations();
    const svcRecBldgIds = userSvcRecs
            .filter(svcrec => svccatIds.indexOf(svcrec.id) > -1)
            .map(svcrec => svcrec.buildingId);
    return (

        link("new care run", () => showCareRun(null, svcRecBldgIds))
    )
}

export const RotaView: FC<Props> = (props) => {
    const {resourceFilter, demandFilter, type} = props;

    const {setExtraMenuItems} = useAppBarContext();
    const {sessionData, rotaRepository} = useServicesContext();
    const params = useQuery();
    const date = useMemo(() => EccoDate.parseIso8601(params.get("date")) || EccoDate.todayLocalTime(), [params.get("date")])
    const loadDate = useDebounce(date, 750) // NOTE: date must not be a fresh object each render else re-renders repeatedly
    const [control, setControl] = useState<RotaDayView | RotaWeekView | undefined>();
    const [newApptOptions, setNewApptOptions] = useState<{resource: DemandResource, date: EccoDate} | undefined>();
    const [recurringAllocate, setRecurringAllocate] = useState(false);
    const [loadDemandTriggerCount, loadDemandTrigger] = useCounter();
    const [loadingDemand, setLoadingDemand] = useState(false);
    const [loadDemandWithRota, setLoadDemandWithRota] = useState(localStorage["defer-unassigned-dropped"] != 'y');
    const [itemViewState, itemViewDispatcher] = useReducer(itemViewReducer, {});
    const [liveOverlay, setLiveOverlay] = useState(0);
    const [rotaLiveEventStatuses, setRotaLiveEventStatuses] = useState<EventSnapshotDto[]>(undefined);
    const [showExpandActionCard, setShowExpandActionCard] = useState(false);
    const [nowLineTestingMins, setNowLineTestingMins] = useState(0);

    // **** TESTING THE NOW LINE - ENABLE THIS SECTION
    // enable this, click 'live' and watch the now line progress quickly through the day
    /*useEffect( () => {
        if (liveOverlay > 0) {
            setNowLineTestingMins(nowLineTestingMins + 20);
        }
    }, [liveOverlay]);*/
    // **** TESTING THE NOW LINE - ENABLE THIS SECTION

    useResultNotification();

    useEffect( () => {
        setExtraMenuItems(<ToggleMenuItem title="defer loading unassigned" storageType="localStorage" storageKey="defer-unassigned-dropped"
        onChange={enabled => setLoadDemandWithRota(!enabled)} variant="checkbox"/>);
        return () => setExtraMenuItems(null);
    }, []);

    const onActivityClick = activity => {
        // Toggle off if we click same one again
        if (itemViewState.activity == activity) {
            activity = null;
        }
        itemViewDispatcher({activity});
    };

    const onDragEndShowActivity = activity => {
        itemViewDispatcher({activity});
    };

    const onResourceClick = (resource, showAvailability) => {
        itemViewDispatcher({resource, showAvailability});
    };

    const runOrRota = resourceFilter.startsWith("careruns") ? "runs" : "rota";

    useEffect( () => {
        setControl(type == "day"
            ? new RotaDayView(onActivityClick, onDragEndShowActivity, onResourceClick)
            : new RotaWeekView(onActivityClick, onResourceClick, sessionData,
                (resource, apptDate) => {
                    setNewApptOptions({resource, date: apptDate});
                }
            ));
        setRecurringAllocate(false);
    }, [type]);

    useEffect( () => {
        control?.updateRecurringAllocation(recurringAllocate);
    }, [recurringAllocate]);

    useEffect(() => {
        // If date changes, hide activity preview
        itemViewDispatcher(({activity: null}));
    }, [date.getDate()])


    const startDate = type == "day" ? loadDate
        : loadDate.subtractDays((loadDate.getDayOfWeek() - 1) % 7);
    const endDate = type == "day" ? loadDate
        : startDate.addDays(6);

    // LOAD ROTA (RESOURCE & DEMAND PER srId)
    // DEFAULT LOAD
    // reload:
    //      NB useRota uses usePromise which uses ReloadEvent by default
    //      however, we're steering clear of global reloads, and instead call the reload directly
    //      also, it would be best to pass many buses which consist of rota reloads (perhaps see activityAllocatedEventBus)
    // rota changes cause the effect to render again see '// draw the rota data'
    const {rota, loading, reload: reloadRotaView} = useRota(startDate, endDate, resourceFilter, demandFilter, true, loadDemandWithRota);

    // when we have a ScheduleEvent fired, we reload the rota
    // NB ideally we don't reload the whole rota and do very specific changes - eg ActivityView
    // but its fair amount of work when an entire schedule can change (remove the whole srId, and re-allocate)
    useEventHandler(ScheduleEvent, () => reloadRotaView());

    // LOAD ROTA DEMAND (per srId)
    // FOR DEFERRED LOAD - when using 'defer-unassigned-dropped'
    // NB we've already loaded the resources
    useEffect(() => {
        // if triggered to load...
        if (loadDemandTriggerCount > 0) {
            setLoadingDemand(true);
            // NB clone of useRota - except we resolve each promise not the whole lot
            rotaRepository.findRotaDemands(startDate, endDate, resourceFilter, demandFilter)
                .then(uniqueSrIds => {
                    uniqueSrIds.forEach((srId, i) => {
                        const demandSpecificSrId = `${demandFilter}:${srId}`;
                        // would be nice to useRota here, but we resolve each promise, not the whole lot
                        // and this is only used for 'defer-unassigned-dropped' - where we defer loading of demand
                        rotaRepository.findRotaByDate(startDate, endDate, resourceFilter, demandSpecificSrId, false, true)
                            .then(rotaForDemand => {
                                control.updateRotaDemand(rotaForDemand);
                                // //control.updateRecurringAllocate(allocateRecurring);
                                // refreshSelectedItems();
                                // ResizeEvent.bus.fire(); // This probably should be after setControl
                            })
                            .finally(() => {
                                if (uniqueSrIds.length == i + 1) {
                                    setLoadingDemand(false);
                                }
                            })
                    })
                });
        }
    },
    [loadDemandTriggerCount]);

    function refreshSelectedItems() {
        if (!rota) return;

        if (itemViewState.activity) {
            const activity = rota.getAllActivities().find(a => a.getRef() == itemViewState.activity.getRef());
            itemViewDispatcher({activity});
        }
        else if (itemViewState.resource) {
            const resource = rota.getResources()
                .find(r => r.getServiceRecipientId() == itemViewState.resource.getServiceRecipientId());
            itemViewDispatcher({resource});
        }
    }



    const history = useHistory();
    const {pathname} = useLocation();

    const navDate = (date: EccoDate | null) => {
        history.push({
            pathname,
            search: date ? "date=" + date.formatIso8601() : undefined
        });
    };

    const updateBuilding = (buildingId: number | null) => {
        navToRota(history, runOrRota, type, buildingId, null, date);
    };

    const buildingId = demandFilter.startsWith("buildings:")
        ? parseInt(demandFilter.split(":")[1])
        : null;
    const svccatIds = demandFilter.startsWith("svccats:")
        ? StringUtils.csvToArray(demandFilter.split(":")[1]).map(s => parseInt(s))
        : null;

    const hasCareRuns = (demandFilter: string) => {
        return demandFilter.startsWith("buildings:") || demandFilter.startsWith("svccats:")
    }

    const updateRunOrRota = (runOrRota: string) => {
        navToRota(history, runOrRota, type, buildingId, svccatIds, date);
    };

    const updateType = (type: "day" | "week") => {
        navToRota(history, runOrRota, type, buildingId, svccatIds, date);
    };

    const {buildings} = useBuildings();

    const isToday = EccoDate.todayLocalTime().equals(date);

    // LONE WORKER STATUS
    // loading all initially, then recent status
    const rotaLiveEventStatusesAllById: StringToObjectMap<EventSnapshotDto> = useMemo(() => {return {}}, []);

    const loadEventStatus = type == "day" && isToday && liveOverlay > 0;
    const {rotaLiveEventStatuses: rotaLiveEventStatusesInitial, loading: rotaLiveEventStatusesInitialLoading} = useRotaLiveLoneWorkerStatus(loadEventStatus, false, [])
    const {rotaLiveEventStatuses: rotaLiveEventStatusesRecent} = useRotaLiveLoneWorkerStatus(loadEventStatus, true, nowLineTestingMins > 0 ? [] : [liveOverlay])

    // set up data structure primed with initial
    useEffect(() => {
        if (rotaLiveEventStatusesInitial) {
            rotaLiveEventStatusesInitial.forEach(e => rotaLiveEventStatusesAllById[`${e.serviceRecipientId}-${e.eventUid}`] = e);
        }
    }, [rotaLiveEventStatusesInitial]);
    // update data with recent
    useEffect(() => {
        // overwrite anything we load which will keep things cumulative as we go through the day
        // NB loading is true by default, then turns false once resolved
        if (!rotaLiveEventStatusesInitialLoading && rotaLiveEventStatusesRecent) {
            rotaLiveEventStatusesRecent.forEach(e => rotaLiveEventStatusesAllById[`${e.serviceRecipientId}-${e.eventUid}`] = e);
            const all: EventSnapshotDto[] = [];
            for (const key in rotaLiveEventStatusesAllById) {
                all.push(rotaLiveEventStatusesAllById[key])
            }
            setRotaLiveEventStatuses(all);
        }
    }, [rotaLiveEventStatusesInitialLoading, rotaLiveEventStatusesRecent]);
    // turn off and clear data
    useEffect(() => {
        if (liveOverlay == 0) {
            setRotaLiveEventStatuses(undefined);
        }
    }, [liveOverlay]);
    // LONE WORKER STATUS


    const Actions = () =>
        <Grid container alignItems="baseline">
            <Grid item xs // xs implies xs={true} which gives us flex-grow: 1 to take up space
            />
            {type == "day" && sessionData.isEnabled("rota.liveOverlay") &&
            <Grid item>
                <FormControlLabel control={
                    <Checkbox name={"liveOverlay"} checked={liveOverlay > 0}
                              onChange={(event: ChangeEvent<HTMLInputElement>, checked) => setLiveOverlay(checked ? 1 : 0)}
                    />
                } label={"live"} disabled={!isToday}/>
            </Grid>
            }
            {hasCareRuns(demandFilter) && sessionData.isEnabled("rota.shifts") &&
            <Grid item>
                <ButtonGroup size="small" aria-label="switch task">
                    <Button onClick={() => updateRunOrRota("rota")}
                            style={runOrRota == "rota" ? {textDecoration: "underline"} : undefined}
                    >
                        staff
                    </Button>
                    <Button onClick={() => updateRunOrRota("runs")}
                            style={runOrRota == "runs" ? {textDecoration: "underline"} : undefined}
                    >
                        runs
                    </Button>
                </ButtonGroup>
            </Grid>
            }
            {!demandFilter.startsWith("svccats:") && <Grid item>
                <span style={{paddingRight: 16}}>at</span>
                <span style={{width: 260, height: 38}}>
                    <SelectBuilding
                        buildings={buildings}
                        buildingId={buildingId}
                        nullLabel={sessionData.isEnabled("rota.whole-org.enable") ? "whole organisation" : undefined}
                        onChange={updateBuilding}
                    />
                </span>
                {buildingId && <BuildingViewPopup buildingId={buildingId}/>}
            </Grid>}
            <Grid item>
                <DateNavEccoDate date={date} onChange={navDate} week={type == "week"}/>
            </Grid>
            <Grid item>
                <ButtonGroup size="small" aria-label="switch view">
                    <Button onClick={() => updateType("day")}
                            style={type == "day" ? {textDecoration: "underline"} : undefined}
                    >
                        day
                    </Button>
                    <Button onClick={() => updateType("week")}
                            style={type == "week" ? {textDecoration: "underline"} : undefined}
                    >
                        week
                    </Button>
                </ButtonGroup>
            </Grid>
            <Grid item xs/>
            <Grid item>
                <RotaMenu date={date} demandFilter={demandFilter}
                    resourceFilter={resourceFilter}
                />
            </Grid>
        </Grid>;

    // Note: This causes a fresh tree of components when something changes so causes things that haven't changed to reload
    useAppBarOptions(<Actions key="header-actions" />,
        [liveOverlay > 0, buildings, runOrRota, buildingId, type, date.formatIso8601()])

    // draw the rota data
    useEffect(() => {
        if (rota) {
            control.renderRota(rota, liveOverlay > 0, rotaLiveEventStatuses);
            //control.updateRecurringAllocate(allocateRecurring);
            refreshSelectedItems();
            //ResizeEvent.bus.fire(); // This probably should be after setControl
        }
    },
    [rota, rotaLiveEventStatuses, nowLineTestingMins]);

    const updateNowLine = () => {
        if (control instanceof RotaDayView) {
            control.updateNowLine(nowLineTestingMins > 0, nowLineTestingMins > 0 ? nowLineTestingMins : undefined);
        }
    }

    // page resize - capture toggling the side menu (we need this because AnchorLeft is hooked into it)
    useEffect(() => {
        ResizeEvent.bus.addHandler(updateNowLine)
        return () => ResizeEvent.bus.removeHandler(updateNowLine)
    }, [control]) // depend on control to avoid a stale closure of 'control' never changing

    /**
     * AnchorLeft is used to pin the action/status bar at the top of the page (position: fixed)
     * It sets its left and height based on most interactions. This pushes the table down, which is
     * within a DomElementContainer. We want to update the nowLine when the table is fully drawn,
     * but despite all useEffects, useLayoutEffects and componentDidUpdate, it seems that react is holding a queue
     * which is only released by the trick of setTimeout in DomElementContainer. When the rota is drawn in the browser,
     * we now get a consistent callback.
     */
    const afterRotaPainted = () => {
        updateNowLine()
    }

    // redraw the line a few seconds after the minute boundary - to ensure our line moves on
    const minuteBoundarySecsLeft = 60+2 - EccoTime.nowLocalTime().getSeconds()
    const [updateNowLineIntervalStart, setUpdateNowLineIntervalStart] = React.useState(false);
    // reset the interval to be 60 seconds once we get to 0
    const updateNowLineAndStartEveryMinute = () => {
        updateLiveRota()
        setUpdateNowLineIntervalStart(true)
    }
    // kick off the useInterval
    useTimeout(updateNowLineAndStartEveryMinute, nowLineTestingMins > 0 ? 1 : minuteBoundarySecsLeft) // once after x seconds

    // 'live' click sets liveOverlay 1, then each minute we reload
    // which in turn triggers a paint, to draw the line
    const updateLiveRota = () => {
        if (liveOverlay > 0) {
            setLiveOverlay(liveOverlay+1);
        }
    }
    useInterval(updateLiveRota, nowLineTestingMins > 0 ? 1 : 60, updateNowLineIntervalStart) // every 60, a few seconds after the minute boundary

    const demandType = rota?.getDemandType();
    return <>
        <AnchorLeft deps={[itemViewState.activity, itemViewState.resource, showExpandActionCard]}>
            <div className="clearfix" style={{background: "white"}}>
                <Box m={1}>
                    <Paper elevation={2}>
                        <Box p={1}>
                            {itemViewState.activity && <ActivityView
                                activity={itemViewState.activity}
                                showRecurring={true} // runOrRota == "runs"}
                                recurringAllocate={recurringAllocate}
                                setRecurringAllocate={setRecurringAllocate}
                                weekView={type == "week"}
                                showExpandActionCard={showExpandActionCard}
                                setShowExpandActionCard={setShowExpandActionCard}
                            />}
                            {itemViewState.resource && <RotaWorkerJobView workerJob={itemViewState.resource} showAvailability={itemViewState.showAvailability}
                                                                       showExpandActionCard={showExpandActionCard}
                                                                       setShowExpandActionCard={setShowExpandActionCard}/>}
                            {!itemViewState.activity && !itemViewState.resource &&
                                <Card elevation={0}>
                                    <CardContent>
                                      <p>
                                        Click on a {sessionData.getMessages()[`rota.demandType.${demandType}.displayName`] || 'worker'} or activity to see details and actions here
                                      </p>
                                        {runOrRota == "runs" && <p>
                                          If you select the "recurring" option your allocations will be repeated into the future. You'll have the option to say how far.
                                        </p>}
                                    </CardContent>
                                    <CardActions>
                                        <Grid container direction="row" alignItems="center">
                                            {runOrRota == "runs" && <Grid item>

                                                {svccatIds && <CareRunEditFromSvcRecIds svccatIds={svccatIds}/>}
                                                {buildingId &&

                                                    link("new care run", () => showCareRun(null, [buildingId]))
                                                }
                                            </Grid>}
                                          <Grid item xs /*give flex-grow: 1 for spacing*//>
                                            {runOrRota == "runs" && <Grid item>
                                                {createCheckBox("recurring", <small>RECURRING</small>, recurringAllocate, () => setRecurringAllocate(!recurringAllocate))}
                                            </Grid>}
                                        </Grid>
                                    </CardActions>
                                </Card>
                            }
                        </Box>
                    </Paper>
                </Box>
            </div>
            {!loadDemandWithRota && !loadingDemand && control && control.getRota() && control.getRota().getUnallocatedOrDroppedActivities().length == 0 && // FIXME: control has updated version of rota
            <Box p={1}>
              <Grid container>
                <Grid item title="To help with performance you'll need to click to load these if you want to manage appointments">
                    {button("load/refresh unallocated/dropped", () => loadDemandTrigger(), "primary")}
                </Grid>
              </Grid>
            </Box>
            }
        </AnchorLeft>
        {newApptOptions && <CreateAppointmentModal
            serviceId={null}
            demandFilter={demandFilter}
            resourceFilter={resourceFilter}
            resource={newApptOptions.resource}
            date={newApptOptions.date}
            setShow={() => setNewApptOptions(undefined)}
        />}
        {/*<div id="properties">*/}
        {/*    <em>Click on a <fmt:message key="rota.demandType.${demandType}.displayName" /> or activity to see details and actions here</em>*/}
        {/*</div>*/}
        {/*<div id="controls-filters"></div>*/}
        <div className="v-gap-15">
            {
                loading || loadingDemand ? <LoadingSpinner useProgress={useApiClientProgress}/>
                    : <DomElementContainer content={control.domElement()} afterNextPaint={afterRotaPainted}/>
            }
        </div>
    </>;
};

export default RotaView;