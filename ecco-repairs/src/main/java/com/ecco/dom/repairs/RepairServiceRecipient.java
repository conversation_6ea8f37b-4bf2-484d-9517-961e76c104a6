package com.ecco.dom.repairs;

import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.persistence.*;

import com.ecco.dom.BaseServiceRecipientEvidence;
import com.ecco.dom.Individual;
import com.ecco.dom.ServiceRecipientAttachment;
import com.ecco.dom.contacts.AddressLike;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Entity
@Getter
@Setter
@DiscriminatorValue(RepairServiceRecipient.DISCRIMINATOR)
public class RepairServiceRecipient extends BaseServiceRecipientEvidence {

    private static final long serialVersionUID = 1L;
    public static final String DISCRIMINATOR = "mtn"; // maintenance
    public static final String PREFIX = "m"; // see BaseServiceRecipient.getPrefix, and service-recipient-dto.ts

    @OneToOne(mappedBy="serviceRecipient", fetch=FetchType.LAZY, cascade= CascadeType.REMOVE)
    private Repair repair;

    @OneToMany(mappedBy = "serviceRecipient", orphanRemoval = true, cascade = CascadeType.REMOVE, fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @OrderBy("id DESC")
    private Set<ServiceRecipientAttachment> attachments = new HashSet<>(); // see https://hibernate.atlassian.net/browse/HHH-9940

    @Override
    public String getDisplayName() {
        var dte = repair.getReceivedDate() != null ? DateTimeFormatter.ISO_DATE.format(repair.getReceivedDateJdk()) : "";
        var bldg = repair.getDisplayName();
        return dte + " " + bldg;
    }

    @Override
    public AddressLike getAddress() {
        return null;
    }

    @Override
    public String getCalendarId() {
        return null;
    }

    @Override
    public Individual getContact() {
        return null;
    }

    @Override
    public Map<String,String> getTextMap() {
        return null;
    }

    @Override
    public String getParentCode() {
        return repair.getId().toString();
    }

    @Override
    public Long getParentId() {
        return null;
    }

    @Override
    public Repair getTargetEntity() {
        return repair;
    }

    @Override
    public String getPrefix() {
        return PREFIX;
    }
}
