package com.ecco.dom.incidents;

import com.ecco.calendar.core.util.DateTimeUtils;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.Agency;
import com.ecco.dom.EvidenceCapable;
import com.ecco.dom.Individual;
import com.ecco.infrastructure.Created;
import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.dom.Identified;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusUpdate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.annotations.QueryInit;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.time.LocalDate;

/**
 * An incident - such as accident/incident/safeguarding/cause-for-concern
 */
@Entity
@Table(name = "incidents")
@Configurable
@Getter
@Setter
public class Incident extends AbstractUnidentifiedVersionedEntity<Integer> implements EvidenceCapable, Created, ServiceRecipientCaseStatusUpdate {

    public static final int DEFAULT_SERVICE_ALLOCATION_ID = -500;

    public static Boolean isSignificant(Incident input, ListDefinitionRepository listDefinitionRepository, ObjectMapper objectMapper) {
        if (input.getCategoryId() != null) {
            var category = listDefinitionRepository.findById(input.getCategoryId()).get();
            if (category.getMetadata() != null) {
                JsonNode tree;
                try {
                    tree = objectMapper.readTree(category.getMetadata());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                var sig = tree.get("significantIncident");
                return sig != null ? sig.asBoolean() : null;
            }
        }
        return null;
    }

    @Id
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    @GeneratedValue(generator="incidentTableGenerator")
    @TableGenerator(
            name = "incidentTableGenerator", initialValue = 1, pkColumnValue = "incidents",
            allocationSize = 1, table = "hibernate_sequences")
    private Integer id = null;

    @PersistenceContext
    private transient EntityManager em;

    @OneToOne(cascade= {CascadeType.PERSIST, CascadeType.REMOVE})
    @JoinColumn(name="serviceRecipientId")
    @QueryInit("*.*.*")
    private IncidentServiceRecipient serviceRecipient;

    @Column(updatable = false, insertable = false)
    private Integer serviceRecipientId;

    //private String name;

    private LocalDate receivedDate;

    private Integer categoryId;

    private boolean acceptedOnService;

    private boolean signpostedBack;

    //public static String SIGNPOSTREASON_LISTNAME = "signpostReason";
    private Integer signpostedReasonId;

    @Lob
    private String signpostedExitComment;

    private Long signpostedAgencyId;

    // start
    private Long supportWorkerId;

    @DateTimeFormat(style = "S-")
    @Column
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime receivingServiceDate; // TODO: LocalDate

    // exit
    private LocalDate exited;

    private Integer exitReasonId;

    @DateTimeFormat
    @Column(name="decisionMadeOn")
    @Type(type = "org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    private DateTime decisionMadeOnDT;

    @Override
    public org.joda.time.LocalDate getDecisionMadeOn() {
        return DateTimeUtils.convertFromUtcToUsersLocalDate(decisionMadeOnDT);
    }

    @Override
    public void setDecisionMadeOn(DateTime decisionMadeOn) {
        this.decisionMadeOnDT = decisionMadeOn;
    }

    /**
     * Individual who reported this (internal)
     */
    private Long reportedById;

    /**
     * Individual who reported this (external)
     */
    private String reportedBy;
    private String reportedByContact;

    private Boolean emergencyServicesInvolved;

    private Boolean hospitalisationInvolved;

    private LocalDate reviewDate;

    @Override
    public DateTime getCreated() {
        return serviceRecipient.getCreated();
    }

    @Override
    public void setCreated(DateTime created) {
        createServiceRecipientIfNull();
        this.serviceRecipient.setCreated(created);
    }

    public Incident() {
        super();
        injectServices();
    }

    public Object readResolve()  {
        injectServices();
        return this;
    }
    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    @Override
    public Integer countSiblings() {
        return null;
    }

    @Override
    public Identified getGrandParent() {
        return null;
    }

    @PrePersist
    protected void createServiceRecipientIfNull() {
        if (this.serviceRecipient == null) {
            this.serviceRecipient = new IncidentServiceRecipient();
            this.serviceRecipient.setIncident(this);
        }
    }

    public void setServiceAllocation(ServiceCategorisation serviceAllocation) {
        Assert.notNull(serviceAllocation, "allocation cannot be null");
        this.createServiceRecipientIfNull();
        this.serviceRecipient.setServiceAllocation(serviceAllocation);
    }

    public Long getServiceTypeId() {
        return serviceRecipient.getServiceTypeId();
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.createServiceRecipientIfNull();
        this.serviceRecipient.setServiceTypeId(serviceTypeId);
    }

    @Override
    public EvidenceCapable getParentEvidenceCapable() {
        return null; // not supported
    }

    @Override
    public boolean isFinalDecision() {
        return decisionMadeOnDT != null;
    }

    @Override
    public Boolean isReferralDecision() {
        return null;
    }

    public LocalDate getReceivedDateJdk() {
        return this.receivedDate;
    }
    public void setReceivedDateJdk(LocalDate receivedDate) {
        this.receivedDate = receivedDate;
    }

    @Override
    public org.joda.time.LocalDate getReceivedDate() {
        return this.receivedDate != null ? JodaToJDKAdapters.localDateToJoda(this.receivedDate) : null;
    }

    @Override
    public void setReceivedDate(DateTime receivedDate) {
        this.receivedDate = receivedDate != null ? JodaToJDKAdapters.dateTimeToJdk(receivedDate).toLocalDate() : null;
    }

    @Override
    public void setSignpostedAgency(Agency agency) {
        this.signpostedAgencyId = agency != null ? agency.getId() : null;
    }

    @Override
    public void withSignpostComment(String comment) {
        this.signpostedExitComment = comment;
    }

    @Override
    public void setSignpostReason(ListDefinitionEntry signpostReason) {
        this.signpostedReasonId = signpostReason != null ? signpostReason.getId() : null;
    }

    @Override
    public void setSupportWorker(Individual worker) {
        this.supportWorkerId = worker != null ? worker.getId() : null;
    }

    @Override
    public void withExitComment(String comment) {
        this.signpostedExitComment = comment;
    }

    @Override
    public void setExitReason(ListDefinitionEntry exitReason) {
        this.exitReasonId = exitReason != null ? exitReason.getId() : null;
    }

    @Override
    public void setExited(DateTime exited) {
        this.exited = exited != null ? JodaToJDKAdapters.dateTimeToJdk(exited).toLocalDate() : null;
    }

    @Override
    public org.joda.time.LocalDate getExitedDate() {
        return JodaToJDKAdapters.localDateToJoda(exited);
    }

    @Override
    public org.joda.time.LocalDate getReceivingServiceLocalDate() {
        return receivingServiceDate == null ? null : receivingServiceDate.toLocalDate();
    }

    @Override
    public String getDisplayName() {
        return "";
    }

    @Override
    public boolean isPending() {
        return false;
    }

    @Override
    public boolean isRequestedDelete() {
        return false;
    }

    @Override
    public void setAcceptedReferral(boolean acceptedReferral) {
        // NO-OP
    }

    @Override
    public void setDecisionReferralMadeOn(DateTime decisionReferralMadeOn) {
        // NO-OP
    }

    @Override
    public void setFinalDecision(boolean finalDecision) {
        // NO-OP
    }

    @Override
    public org.joda.time.LocalDate getDecisionReferralMadeOn() {
        return null;
    }

    @Override
    public Boolean isAcceptedReferral() {
        return isAcceptedOnService();
    }

}
