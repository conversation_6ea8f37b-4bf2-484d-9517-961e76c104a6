package com.ecco.dom.commands;

import java.util.UUID;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;

/**
 * Commands around Contact.
 */
@Entity
@Table(name = "cont_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("update")
public class ContactCommand extends BaseIntKeyedCommand {

    /** recipient if !inbound, sender if inbound */
    @Column
    private int contactId;

    public ContactCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, int contactId) {
        super(uuid, remoteCreationTime, userId, body);
        this.contactId = contactId;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected ContactCommand() {
        super();
    }

    public long getContactId() {
        return contactId;
    }
}
