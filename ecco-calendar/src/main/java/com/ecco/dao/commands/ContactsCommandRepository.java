package com.ecco.dao.commands;

import com.ecco.dom.commands.ContactCalendarEntryCommand;
import com.ecco.dom.commands.ContactCommand;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import javax.persistence.QueryHint;
import java.util.List;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface ContactsCommandRepository extends BaseCommandRepository<ContactCommand, Integer> {

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT cc FROM ContactCommand cc WHERE cc.contactId = ?1")
    // ? draft false
    List<ContactCommand> findAllUpdatesByContactId(Integer contactId);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e FROM ContactCalendarEntryCommand e WHERE TYPE(e) = ContactCalendarEntryCommand AND e.contactId = ?1 order by e.created asc")
    // ? draft false
    List<ContactCalendarEntryCommand> findCalendarEntriesByContactId(Integer contactId);

    void deleteAllInBatch(Iterable<ContactCommand> entities);
}
