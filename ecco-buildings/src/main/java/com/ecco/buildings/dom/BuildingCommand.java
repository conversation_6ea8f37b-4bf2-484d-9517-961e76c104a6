package com.ecco.buildings.dom;

import java.util.UUID;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.*;

import com.ecco.infrastructure.dom.BaseIntKeyedCommand;

/**
 * Commands around buildings.
 */
@Entity
@Table(name = "bldg_commands")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "commandname", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("update") // TODO change to bldgUpdate
public class BuildingCommand extends BaseIntKeyedCommand {

    @Column(nullable=false)
    private Integer serviceRecipientId;

    public BuildingCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body, Integer serviceRecipientId) {
        super(uuid, remoteCreationTime, userId, body);
        this.serviceRecipientId = serviceRecipientId;
    }

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    protected BuildingCommand() {
        super();
    }

    public long getServiceRecipientId() {
        return serviceRecipientId;
    }
}
